# CaseBuilderAI Development Guide

## Commands
- **Run Application**: `streamlit run app.py`
- **Docker Build**: `docker build -t casebuilderai .`
- **Docker Run**: `docker run -p 8080:8080 casebuilderai`
- **Linting**: `autopep8 --in-place --aggressive --aggressive *.py`
- **Format Code**: `pycodestyle *.py`

## Code Style Guidelines
- **Imports**: Group standard library, third-party, and local imports
- **Formatting**: Follow PEP 8 conventions
- **Types**: Use docstrings for function/parameter types
- **Naming**: 
  - snake_case for functions/variables
  - CamelCase for classes
- **Error Handling**: Use try/except blocks with specific exceptions
- **Async**: Use async/await patterns consistently
- **Documentation**: Document functions with docstrings (purpose, params, return)
- **Streamlit Components**: Follow streamlit design patterns
- **Modules**: Keep related functionality in dedicated modules
- **Environment Variables**: Use dotenv for configuration