FROM python:3.9.5-slim-buster

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set the working directory
WORKDIR /app

# Create and activate a virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip
RUN pip install --upgrade pip

# Copy the current directory contents into the container at /app
COPY . /app

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Install system packages including poppler-utils
RUN apt-get update && apt-get install -y \
    build-essential \
    libffi-dev \
    libssl-dev \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev \
    poppler-utils

# Copy the config.toml file to the Streamlit configuration directory
RUN mkdir -p /root/.streamlit
COPY config.toml /root/.streamlit/config.toml    

EXPOSE 8080

CMD ["streamlit", "run", "--server.port", "8080", "--server.address", "0.0.0.0", "app.py"]
