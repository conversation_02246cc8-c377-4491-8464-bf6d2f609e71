# Guía de Prueba de Integración para CaseBuilder.AI

Esta guía proporciona instrucciones paso a paso para probar la integración de la nueva arquitectura hexagonal con la aplicación Streamlit existente.

## Requisitos Previos

- Python 3.8 o superior
- Dependencias instaladas (`pip install -r requirements.txt`)
- Variables de entorno configuradas en un archivo `.env`

### Configuración de Variables de Entorno

CaseBuilder.AI utiliza variables de entorno para gestionar claves de API y otras configuraciones sensibles. Para configurar correctamente:

1. Crea un archivo `.env` en la raíz del proyecto:

```
# .env
OPENAI_API_KEY=tu-clave-api-aquí
```

2. Asegúrate de que este archivo esté incluido en `.gitignore` para evitar compartir tus credenciales.

3. Para entornos de producción, puedes utilizar variables de entorno del sistema o servicios de gestión de secretos.

## Paso 1: Ejecutar las Pruebas Automatizadas

Las pruebas automatizadas verifican que los componentes de la nueva arquitectura funcionen correctamente con datos de muestra.

```bash
python integration_test_runner.py
```

Este script ejecutará pruebas para las siguientes funciones:
- `extract_facts_and_liability`
- `comprehensive_medical_analysis`
- `identify_medical_expenses`
- `create_demand_letter`

También verificará el manejo de errores con entradas inválidas.

## Paso 2: Pruebas Manuales con la Interfaz de Streamlit

Para probar la integración directamente en la interfaz de Streamlit:

```bash
python integration_test_runner.py --streamlit
```

Este comando:
1. Hace una copia de seguridad del archivo `app.py` original
2. Modifica temporalmente `app.py` para usar la nueva arquitectura
3. Inicia la aplicación Streamlit
4. Restaura el archivo `app.py` original al finalizar

### Procedimiento de Prueba Manual

Una vez que la aplicación Streamlit esté en ejecución:

1. **Preparación**:
   - Abra la URL mostrada en el terminal (generalmente http://localhost:8501)
   - Prepare un documento de muestra (informe policial o registro médico) para cargar

2. **Configuración del Caso**:
   - Ingrese "John Smith" como nombre del cliente
   - Seleccione "California" como estado
   - Seleccione "Bodily Injury Liability" como tipo de responsabilidad
   - Cargue el documento de muestra

3. **Análisis de Hechos y Responsabilidad**:
   - Haga clic en "Extract Facts & Liability"
   - Verifique que el resultado muestre un análisis adecuado
   - Compruebe que el resultado mencione la colisión y el nombre del cliente

4. **Análisis Médico**:
   - Haga clic en "Analyze Medical Records"
   - Verifique que el resultado muestre un análisis médico detallado
   - Compruebe que el resultado mencione diagnósticos y tratamientos

5. **Análisis de Gastos Médicos**:
   - Haga clic en "Identify Medical Expenses"
   - Verifique que el resultado muestre una tabla de gastos médicos
   - Compruebe que el resultado incluya costos y proveedores

6. **Generación de Carta de Demanda**:
   - Haga clic en "Create Demand Letter"
   - Verifique que se genere una carta de demanda completa
   - Compruebe que la carta mencione al cliente, la lesión y la demanda

7. **Prueba de Manejo de Errores**:
   - Intente ejecutar análisis sin cargar un documento
   - Verifique que se muestren mensajes de error apropiados

8. **Finalización de la Prueba**:
   - Presione Ctrl+C en el terminal cuando termine
   - Confirme que el archivo `app.py` original se ha restaurado

## Documento de Muestra para Pruebas

A continuación se muestra un documento de texto de muestra que puede copiar y usar para pruebas:

```
TRAFFIC COLLISION REPORT
Date: 2023-06-15
Time: 14:30
Location: Main St & Oak Ave, Los Angeles, CA

Driver 1: John Smith (Toyota Corolla)
Driver 2: Jane Doe (Honda Civic)

Narrative: Driver 1 was traveling northbound on Main St. when Driver 2, traveling westbound on Oak Ave,
failed to stop at the red light and collided with Driver 1's vehicle. Driver 1 was transported to
Memorial Hospital with complaints of neck and back pain.

MEDICAL RECORD
Patient: John Smith
Date: 2023-06-15

Diagnosis: Cervical strain, lumbar sprain, contusion to left shoulder
Treatment: Pain medication, physical therapy recommended 3x/week for 8 weeks
Cost: $3,500 for emergency treatment
Follow-up: Patient reported ongoing neck pain and difficulty sleeping
```

## Solución de Problemas

Si encuentra errores durante las pruebas:

1. **Problemas de Dependencias**:
   - Verifique que todas las dependencias estén instaladas con `pip install -r requirements.txt`
   - Asegúrese de que la estructura de directorios esté intacta

2. **Errores de API de OpenAI**:
   - Confirme que su clave API de OpenAI esté configurada correctamente en el archivo `.env`
   - Verifique que la clave tenga suficientes créditos disponibles
   - El script de prueba utiliza una clave de API falsa por defecto para pruebas; para pruebas completas establezca una clave API válida en su archivo `.env`

3. **Errores de ServiceLocator**:
   - Se ha actualizado la clase `ServiceLocator` para usar los métodos `register_service` y `get_service` en lugar de los métodos antiguos
   - Si encuentra errores relacionados con el ServiceLocator, asegúrese de estar usando la versión más reciente

4. **Errores en la Aplicación Streamlit**:
   - Revise los mensajes de error en la consola
   - Verifique que se esté utilizando la versión modificada de `app.py`

5. **Restauración Fallida de app.py**:
   - Si el script falla antes de restaurar `app.py`, utilice el archivo de respaldo `app.py.bak`