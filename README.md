# CaseBuilderAI Code Submission

## Overview
CaseBuilderAI is an AI-powered legal tech app designed to assist law firms by automating critical tasks in personal injury cases. The app integrates advanced AI models (`gpt-4o`, `o1`, and `gpt-4o-mini`) to process legal documents, generate narratives, and analyze data. This submission includes the core modules that power CaseBuilderAI.

## Configuration
CaseBuilderAI uses environment variables for API keys and sensitive configuration:

1. Create a `.env` file in the project root:
   ```bash
   touch .env
   ```

2. Add your OpenAI API key to the file:
   ```
   OPENAI_API_KEY=your-actual-api-key
   ```

3. For database connection (if needed), add the following variables:
   ```
   DATABASE_HOST=localhost
   DATABASE_PORT=3306
   DATABASE_USER=username
   DATABASE_PASSWORD=password
   DATABASE_NAME=database
   ```

4. The `.env` file is automatically gitignored to prevent credential leakage.

IMPORTANT: The application uses dotenv to load environment variables from the `.env` file.

---

## Key Modules
### **1. `app.py`**
- **Central Application Logic**: Integrates all functionalities and manages user interaction through a responsive Streamlit-based interface.
- **User Authentication**: Handles login/logout securely using hashed passwords and session management.
- **Token-Based Action Management**: Implements a credit/token system to regulate user actions, ensuring fair usage and scalability.
- **Dynamic Sidebar**:
  - Allows users to select case types, customize liability parameters, and provide additional details.
  - Includes options for purchasing tokens and accessing help resources.
- **Section Highlights**:
  - **Police Report Analysis**: Summarizes reports, identifies liability, and creates narratives.
  - **Medical Record Processing**: Extracts injury details, predicts future treatments, and identifies ICD-10 codes.
  - **Image Analysis**: Analyzes accident scene photos, property damage, and road conditions.
  - **Prelitigation Files**: Drafts demand letters, estimates damages, and provides case law references.

---

### **2. `case_builder.py`**
- **Prompt Management**: Defines and manages prompts for diverse legal tasks, including:
  - Summarizing police reports and medical records.
  - Identifying liability and damages.
  - Drafting comprehensive demand letters.
- **Dynamic Prompt Generation**: Personalizes outputs based on user input, jurisdiction, and case specifics.
- **Subinstruction Framework**: Breaks down complex tasks into manageable steps for precise and actionable results.
- **Examples and Templates**: Includes ready-to-use examples for consistent and accurate output.

---

### **3. `gpt_api.py`**
- **Multi-Model Support**:
  - Utilizes `gpt-4o` for tasks requiring extensive context (up to 128,000 tokens).
  - Employs `o1-preview` for faster and more concise outputs.
- **Asynchronous API Calls**: Processes multiple requests in parallel using `aiohttp`, ensuring efficiency under high workloads.
- **Error Handling**: Validates API responses and provides detailed error messages for debugging.
- **Payload Customization**: Configurable parameters for token limits, role definitions, and combined content processing.

---

### **4. `gpt4o_vision.py`**
- **Image Processing with AI**:
  - Extracts insights from images (e.g., accident scenes, injuries) using the `gpt-4o` model.
  - Encodes images in base64 for API compatibility.
- **Advanced Analysis**:
  - Combines image data with contextual text for detailed narratives.
  - Handles tasks such as describing injuries, road conditions, and property damage.
- **Asynchronous Design**: Optimized for handling high volumes of image-based inputs efficiently.

---

---

## Migration Status: Streamlit to React + FastAPI

### **Current Progress (December 2024)**

#### ✅ **COMPLETED FEATURES**

**Backend Infrastructure**
- ✅ FastAPI backend with modular router structure
- ✅ Authentication system with JWT tokens
- ✅ Session management with volatile storage
- ✅ Database integration with existing MySQL schema
- ✅ Token-based usage tracking system
- ✅ Document upload and processing endpoints
- ✅ Real-time analysis progress tracking

**Frontend Foundation**
- ✅ React TypeScript application with modern UI
- ✅ Responsive design with Tailwind CSS
- ✅ Authentication flow (login/logout)
- ✅ File upload with drag-and-drop support
- ✅ Preferences management system
- ✅ Real-time statistics dashboard
- ✅ Processing modals with progress indicators

**Document Analysis**
- ✅ Police Report Summary with professional formatting
- ✅ In-Depth Liability Analysis with structured output
- ✅ Facts & Liability Analysis with legal formatting
- ✅ Medical Analysis with organized sections
- ✅ Medical Expenses with categorized breakdown
- ✅ Future Treatment with projection details
- ✅ Injury Analysis with medical-legal evaluation
- ✅ Accident Scene analysis with evidence structure
- ✅ Property Damage assessment with organized format

**Core Functionality**
- ✅ OpenAI API integration (gpt-4o, gpt-4o-vision, o4-mini)
- ✅ PDF text extraction with PyPDF2
- ✅ OCR capabilities using GPT-4o Vision
- ✅ Image analysis for injuries, accidents, property damage
- ✅ Document export to Word (.docx) format
- ✅ Real token deduction system (1 token/analysis, 5 tokens/demand letter)
- ✅ Client information management in preferences

#### 🔧 **RECENTLY FIXED ISSUES**

**Document Export**
- ✅ Fixed "Failed to download document" error
- ✅ Created dedicated `/api/analysis/export/word` endpoint
- ✅ Proper content-type headers and file naming
- ✅ Integration with DocumentExportService

**Token System**
- ✅ Implemented real token tracking using `button_clicks` table
- ✅ Automatic token deduction for analysis and generation
- ✅ Real-time token display in dashboard
- ✅ Validation before processing (HTTP 402 for insufficient tokens)
- ✅ Test endpoint for frontend development

**Professional Formatting**
- ✅ Applied consistent structure to ALL analysis types
- ✅ Bold headers (`**SECTION**`) for visual organization
- ✅ Professional content suitable for legal documents
- ✅ Clean export formatting for Word documents

**Client Management**
- ✅ Added client_name field to preferences
- ✅ UI input for client information
- ✅ Integration in document export
- ✅ Fallback handling for missing client data

#### 🚧 **IN PROGRESS**

**Current Sprint**
- 🔄 Frontend token display optimization (endpoint working, UI updating)
- 🔄 Authentication flow refinement
- 🔄 Error handling improvements

#### 📋 **NEXT MILESTONES**

**Phase 1: Core Completion (Next 1-2 weeks)**
- [ ] Demand Letter Generation integration
- [ ] Complete authentication system with real user management
- [ ] Image upload validation and processing
- [ ] Advanced error handling and user feedback
- [ ] Performance optimization for large documents

**Phase 2: Advanced Features (Following 2-3 weeks)**
- [ ] CaseBuilder Assistant (in-app chat with OpenAI)
- [ ] Batch processing capabilities
- [ ] Advanced document templates
- [ ] User preferences persistence
- [ ] Analytics and usage tracking

**Phase 3: Production Ready (Final phase)**
- [ ] Security audit and hardening
- [ ] Performance testing and optimization
- [ ] Deployment configuration
- [ ] User documentation
- [ ] Admin panel for user management

### **Technical Architecture**

**Backend Stack**
- FastAPI (Python 3.9+)
- MySQL database (existing schema)
- OpenAI API integration
- JWT authentication
- Async/await patterns

**Frontend Stack**
- React 18 with TypeScript
- Tailwind CSS for styling
- Zustand for state management
- React Hook Form for forms
- Axios for API communication

**Key Design Decisions**
- Volatile sessions (no file storage in DB for security)
- Token-based usage control
- Modular router architecture
- Professional document formatting
- Real-time progress tracking

### **Migration Benefits Achieved**

1. **Modern Tech Stack**: React + FastAPI vs. Streamlit monolith
2. **Better UX**: Responsive design, real-time updates, professional UI
3. **Scalability**: Modular architecture, async processing
4. **Security**: JWT tokens, session management, input validation
5. **Maintainability**: TypeScript, modular code, clear separation of concerns

---

## Hosted App Access
If you would like to test the fully functional application, please reach out, and we can provide you with access to a hosted version along with test credentials.
