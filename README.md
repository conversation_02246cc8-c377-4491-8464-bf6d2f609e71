# CaseBuilderAI Code Submission

## Overview
CaseBuilderAI is an AI-powered legal tech app designed to assist law firms by automating critical tasks in personal injury cases. The app integrates advanced AI models (`gpt-4o`, `o1`, and `gpt-4o-mini`) to process legal documents, generate narratives, and analyze data. This submission includes the core modules that power CaseBuilderAI.

## Configuration
CaseBuilderAI uses environment variables for API keys and sensitive configuration:

1. Create a `.env` file in the project root:
   ```bash
   touch .env
   ```

2. Add your OpenAI API key to the file:
   ```
   OPENAI_API_KEY=your-actual-api-key
   ```

3. For database connection (if needed), add the following variables:
   ```
   DATABASE_HOST=localhost
   DATABASE_PORT=3306
   DATABASE_USER=username
   DATABASE_PASSWORD=password
   DATABASE_NAME=database
   ```

4. The `.env` file is automatically gitignored to prevent credential leakage.

IMPORTANT: The application uses dotenv to load environment variables from the `.env` file.

---

## Key Modules
### **1. `app.py`**
- **Central Application Logic**: Integrates all functionalities and manages user interaction through a responsive Streamlit-based interface.
- **User Authentication**: Handles login/logout securely using hashed passwords and session management.
- **Token-Based Action Management**: Implements a credit/token system to regulate user actions, ensuring fair usage and scalability.
- **Dynamic Sidebar**:
  - Allows users to select case types, customize liability parameters, and provide additional details.
  - Includes options for purchasing tokens and accessing help resources.
- **Section Highlights**:
  - **Police Report Analysis**: Summarizes reports, identifies liability, and creates narratives.
  - **Medical Record Processing**: Extracts injury details, predicts future treatments, and identifies ICD-10 codes.
  - **Image Analysis**: Analyzes accident scene photos, property damage, and road conditions.
  - **Prelitigation Files**: Drafts demand letters, estimates damages, and provides case law references.

---

### **2. `case_builder.py`**
- **Prompt Management**: Defines and manages prompts for diverse legal tasks, including:
  - Summarizing police reports and medical records.
  - Identifying liability and damages.
  - Drafting comprehensive demand letters.
- **Dynamic Prompt Generation**: Personalizes outputs based on user input, jurisdiction, and case specifics.
- **Subinstruction Framework**: Breaks down complex tasks into manageable steps for precise and actionable results.
- **Examples and Templates**: Includes ready-to-use examples for consistent and accurate output.

---

### **3. `gpt_api.py`**
- **Multi-Model Support**:
  - Utilizes `gpt-4o` for tasks requiring extensive context (up to 128,000 tokens).
  - Employs `o1-preview` for faster and more concise outputs.
- **Asynchronous API Calls**: Processes multiple requests in parallel using `aiohttp`, ensuring efficiency under high workloads.
- **Error Handling**: Validates API responses and provides detailed error messages for debugging.
- **Payload Customization**: Configurable parameters for token limits, role definitions, and combined content processing.

---

### **4. `gpt4o_vision.py`**
- **Image Processing with AI**:
  - Extracts insights from images (e.g., accident scenes, injuries) using the `gpt-4o` model.
  - Encodes images in base64 for API compatibility.
- **Advanced Analysis**:
  - Combines image data with contextual text for detailed narratives.
  - Handles tasks such as describing injuries, road conditions, and property damage.
- **Asynchronous Design**: Optimized for handling high volumes of image-based inputs efficiently.

---

## Hosted App Access
If you would like to test the fully functional application, please reach out, and we can provide you with access to a hosted version along with test credentials.
