# CaseBuilder AI - Migration to FastAPI + React

## 🚀 Overview

This is the new modern architecture for CaseBuilder AI, migrating from Streamlit to FastAPI + React for better performance, scalability, and user experience.

## 📁 Project Structure

```
CaseBuilderAI/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── main.py         # FastAPI application
│   │   ├── config.py       # Configuration settings
│   │   ├── dependencies.py # Dependency injection
│   │   ├── middleware/     # Custom middleware
│   │   ├── models/         # Pydantic models
│   │   ├── routers/        # API endpoints
│   │   └── services/       # Business logic services
│   ├── requirements.txt    # Python dependencies
│   └── .env.example       # Environment variables template
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   ├── store/         # Zustand stores
│   │   ├── types/         # TypeScript types
│   │   └── utils/         # Utility functions
│   ├── package.json       # Node.js dependencies
│   └── .env.example      # Environment variables template
└── casebuilder/           # Existing business logic (preserved)
    ├── core/              # Domain models
    ├── application/       # Use cases and handlers
    └── infrastructure/    # External services
```

## 🛠️ Installation & Setup

### Prerequisites

- Python 3.11+
- Node.js 18+
- MySQL database
- OpenAI API key

### Backend Setup

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Run the backend:**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Frontend Setup

1. **Navigate to frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the frontend:**
   ```bash
   npm start
   ```

## 🔧 Configuration

### Backend Environment Variables

```env
# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database
DATABASE_HOST=127.0.0.1
DATABASE_PORT=3306
DATABASE_USER=your_db_user
DATABASE_PASSWORD=your_db_password
DATABASE_NAME=your_db_name

# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Session Management
REDIS_URL=redis://localhost:6379
SESSION_EXPIRE_SECONDS=3600
```

### Frontend Environment Variables

```env
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_NAME=CaseBuilder AI
REACT_APP_VERSION=2.0.0
```

## 🏗️ Architecture Features

### Backend (FastAPI)

- **Async/Await Support**: Native async support for better performance
- **Automatic API Documentation**: Swagger UI at `/api/docs`
- **Type Safety**: Pydantic models for request/response validation
- **JWT Authentication**: Secure token-based authentication
- **Session Management**: Volatile sessions for security
- **Middleware**: Security, CORS, and session management
- **Error Handling**: Comprehensive error handling and logging

### Frontend (React + TypeScript)

- **Modern UI**: Tailwind CSS for beautiful, responsive design
- **Type Safety**: Full TypeScript support
- **State Management**: Zustand for simple, effective state management
- **Form Handling**: React Hook Form for efficient form management
- **API Integration**: Axios with interceptors for API calls
- **Real-time Updates**: Toast notifications and progress tracking
- **Responsive Design**: Mobile-first approach

### Key Improvements

1. **Volatile Sessions**: No sensitive data stored in database
2. **Advanced Preferences**: Granular control over analysis settings
3. **Unified Workflow**: Preferences → Upload → Generate
4. **Real-time Progress**: Live updates during processing
5. **Better UX**: Modern, intuitive interface
6. **Scalability**: Separate frontend/backend for better scaling

## 🔄 Migration Strategy

### Phase 1: Core Infrastructure ✅
- [x] FastAPI backend setup
- [x] React frontend setup
- [x] Authentication system
- [x] Session management
- [x] Preferences system

### Phase 2: Document Processing (Next)
- [ ] File upload with drag & drop
- [ ] Document triage integration
- [ ] Progress tracking
- [ ] Real-time updates

### Phase 3: Analysis Integration
- [ ] Connect existing CaseBuilder handlers
- [ ] Analysis orchestration
- [ ] Results management
- [ ] Export functionality

### Phase 4: Advanced Features
- [ ] WebSocket support
- [ ] Advanced UI components
- [ ] Performance optimization
- [ ] Testing suite

## 🚦 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh token

### Preferences
- `GET /api/preferences/` - Get user preferences
- `PUT /api/preferences/` - Update preferences
- `POST /api/preferences/reset` - Reset to defaults
- `GET /api/preferences/analysis-types` - Get available analysis types

### Documents
- `POST /api/documents/upload` - Upload document
- `GET /api/documents/` - List documents
- `GET /api/documents/{id}` - Get document details
- `DELETE /api/documents/{id}` - Delete document

### Analysis
- `POST /api/analysis/start` - Start analysis
- `GET /api/analysis/` - List analyses
- `GET /api/analysis/{id}` - Get analysis results
- `GET /api/analysis/{id}/progress` - Get progress

### Generation
- `POST /api/generation/demand-letter` - Generate demand letter
- `GET /api/generation/{id}` - Get generation results
- `GET /api/generation/{id}/export/word` - Export as Word

## 🧪 Testing

### Backend Testing
```bash
cd backend
pytest
```

### Frontend Testing
```bash
cd frontend
npm test
```

## 📝 Development Notes

### Preserving Existing Logic
- All existing business logic in `casebuilder/` is preserved
- Handlers and commands are reused through adapters
- Database schema remains unchanged (only auth table used)

### Security Considerations
- Volatile sessions prevent data persistence
- JWT tokens with expiration
- CORS protection
- Input validation
- Rate limiting

### Performance Optimizations
- Async processing
- Background tasks
- Efficient state management
- Lazy loading
- Code splitting

## 🤝 Contributing

1. Follow the existing code structure
2. Use TypeScript for frontend
3. Follow Python type hints for backend
4. Write tests for new features
5. Update documentation

## 📞 Support

For questions or issues with the migration:
1. Check the existing documentation
2. Review the API documentation at `/api/docs`
3. Check the browser console for frontend issues
4. Review backend logs for API issues

## 🎯 Next Steps

1. Complete document upload functionality
2. Integrate existing analysis handlers
3. Add real-time progress tracking
4. Implement export functionality
5. Add comprehensive testing
6. Deploy to production environment
