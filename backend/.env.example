# Environment Configuration
ENVIRONMENT=development
DEBUG=true

# Security Settings
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
ALLOWED_HOSTS=["localhost", "127.0.0.1"]

# Database Settings (MySQL)
DATABASE_HOST=127.0.0.1
DATABASE_PORT=3306
DATABASE_USER=your_db_user
DATABASE_PASSWORD=your_db_password
DATABASE_NAME=your_db_name

# Cloud SQL Settings (for production)
# INSTANCE_CONNECTION_NAME=your-project:region:instance-name
# DB_SOCKET_DIR=/cloudsql

# OpenAI API
OPENAI_API_KEY=your-openai-api-key-here

# Redis Settings (for session management)
REDIS_URL=redis://localhost:6379
SESSION_EXPIRE_SECONDS=3600

# File Upload Settings
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_FILE_TYPES=["pdf", "png", "jpg", "jpeg", "docx"]

# Processing Settings
MAX_CONCURRENT_ANALYSES=5
ANALYSIS_TIMEOUT_SECONDS=300
