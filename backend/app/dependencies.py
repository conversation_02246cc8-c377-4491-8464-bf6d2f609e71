"""
Dependency injection for CaseBuilder AI FastAPI application.
"""

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt
import logging
from typing import Optional

from .services.auth_service import AuthService, User
from .middleware.session import session_store
from .config import get_settings

logger = logging.getLogger(__name__)
security = HTTPBearer()
settings = get_settings()


# Service dependencies
def get_auth_service() -> AuthService:
    """Get authentication service instance."""
    return AuthService()


# Authentication dependencies
async def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    Get current authenticated user from JWT token.
    
    Args:
        request: FastAPI request object
        credentials: JWT credentials
        
    Returns:
        User object
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Decode JWT token
        token = credentials.credentials
        payload = jwt.decode(
            token, 
            settings.secret_key, 
            algorithms=[settings.algorithm]
        )
        
        username = payload.get("sub")
        user_id = payload.get("user_id")
        
        if not username:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token: missing username"
            )
        
        # Verify session exists
        session_id = getattr(request.state, "session_id", None)
        session_data = getattr(request.state, "session_data", None)
        
        if not session_id or not session_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No active session"
            )
        
        # Verify session belongs to the user
        if session_data.get("user_id") != user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Session user mismatch"
            )
        
        # Create user object from token data
        user = User(
            username=username,
            user_id=int(user_id) if user_id else None,
            first_name="",  # Will be populated from session if needed
            last_name=None,
            email=None
        )
        
        return user
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired"
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )


async def get_current_session(request: Request) -> dict:
    """
    Get current user session data.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Session data dictionary
        
    Raises:
        HTTPException: If no active session
    """
    session_data = getattr(request.state, "session_data", None)
    
    if not session_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No active session"
        )
    
    return session_data


async def get_session_id(request: Request) -> str:
    """
    Get current session ID.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Session ID string
        
    Raises:
        HTTPException: If no active session
    """
    session_id = getattr(request.state, "session_id", None)
    
    if not session_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No active session"
        )
    
    return session_id


# Optional authentication (for endpoints that work with or without auth)
async def get_current_user_optional(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[User]:
    """
    Get current user if authenticated, None otherwise.
    
    Args:
        request: FastAPI request object
        credentials: Optional JWT credentials
        
    Returns:
        User object if authenticated, None otherwise
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(request, credentials)
    except HTTPException:
        return None


# Session management dependencies
def get_session_store():
    """Get session store instance."""
    return session_store


# Validation dependencies
async def validate_session_access(
    request: Request,
    user: User = Depends(get_current_user)
) -> bool:
    """
    Validate that the current user has access to the session.
    
    Args:
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        True if access is valid
        
    Raises:
        HTTPException: If access is denied
    """
    session_data = await get_current_session(request)
    
    if session_data.get("user_id") != str(user.user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this session"
        )
    
    return True


# Rate limiting dependency
class RateLimiter:
    """Simple rate limiter for API endpoints."""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # In production, use Redis
    
    async def __call__(self, request: Request) -> bool:
        """
        Check rate limit for the current request.
        
        Args:
            request: FastAPI request object
            
        Returns:
            True if request is allowed
            
        Raises:
            HTTPException: If rate limit exceeded
        """
        import time
        
        # Get client identifier (IP or user ID)
        client_id = request.client.host
        if hasattr(request.state, "user_id") and request.state.user_id:
            client_id = f"user_{request.state.user_id}"
        
        current_time = time.time()
        window_start = current_time - self.window_seconds
        
        # Clean old entries
        if client_id in self.requests:
            self.requests[client_id] = [
                req_time for req_time in self.requests[client_id]
                if req_time > window_start
            ]
        else:
            self.requests[client_id] = []
        
        # Check limit
        if len(self.requests[client_id]) >= self.max_requests:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded"
            )
        
        # Add current request
        self.requests[client_id].append(current_time)
        
        return True


# Create rate limiter instances
standard_rate_limiter = RateLimiter(max_requests=100, window_seconds=60)
strict_rate_limiter = RateLimiter(max_requests=10, window_seconds=60)
