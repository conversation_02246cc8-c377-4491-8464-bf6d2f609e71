"""
FastAPI main application for CaseBuilder AI.
Provides REST API endpoints for document analysis and demand letter generation.
"""

from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>earer
import uvicorn
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import routers
from .routers import auth, preferences, documents, analysis, generation
from .middleware.security import SecurityMiddleware
from .middleware.session import SessionMiddleware
from .config import get_settings

# Initialize FastAPI app
app = FastAPI(
    title="CaseBuilder AI API",
    description="Advanced legal document analysis and demand letter generation",
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json"
)

# Get settings
settings = get_settings()

# Add security middleware
app.add_middleware(SecurityMiddleware)
app.add_middleware(SessionMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add trusted host middleware for production
if settings.environment == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.allowed_hosts
    )

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(preferences.router, prefix="/api/preferences", tags=["Preferences"])
app.include_router(documents.router, prefix="/api/documents", tags=["Documents"])
app.include_router(analysis.router, prefix="/api/analysis", tags=["Analysis"])
app.include_router(generation.router, prefix="/api/generation", tags=["Generation"])

@app.get("/")
async def root():
    """Root endpoint - API health check."""
    return {
        "message": "CaseBuilder AI API",
        "version": "2.0.0",
        "status": "healthy"
    }

@app.get("/api/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "environment": settings.environment,
        "version": "2.0.0"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.environment == "development"
    )
