"""
Pydantic models for user preferences and analysis configuration.
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Literal
from enum import Enum


class AnalysisType(str, Enum):
    """Available analysis types."""
    POLICE_REPORT_SUMMARY = "police_report_summary"
    FACTS_LIABILITY = "facts_liability"
    IN_DEPTH_LIABILITY = "in_depth_liability"
    MEDICAL_ANALYSIS = "medical_analysis"
    MEDICAL_EXPENSES = "medical_expenses"
    FUTURE_TREATMENT = "future_treatment"
    ANALYZE_INJURIES = "analyze_injuries"
    ACCIDENT_SCENE = "accident_scene"
    PROPERTY_DAMAGE = "property_damage"
    GENERATE_DEMAND_LETTER = "generate_demand_letter"


class DetailLevel(str, Enum):
    """Analysis detail levels."""
    BASIC = "basic"
    STANDARD = "standard"
    COMPREHENSIVE = "comprehensive"


class ContentEmphasis(str, Enum):
    """Content emphasis options."""
    FACTS = "facts"
    LEGAL = "legal"
    MEDICAL = "medical"
    FINANCIAL = "financial"


class AnalysisStyle(str, Enum):
    """Analysis presentation styles."""
    NARRATIVE = "narrative"
    CHRONOLOGICAL = "chronological"
    BULLETED = "bulleted"
    TABULAR = "tabular"


class DemandLetterSection(str, Enum):
    """Demand letter sections."""
    INTRODUCTION = "introduction"
    FACTS = "facts"
    LIABILITY = "liability"
    DAMAGES = "damages"
    MEDICAL_EXPENSES = "medical_expenses"
    FUTURE_TREATMENT = "future_treatment"
    GENERAL_DAMAGES = "general_damages"
    CONCLUSION = "conclusion"


class LetterLength(str, Enum):
    """Demand letter length options."""
    CONCISE = "concise"
    STANDARD = "standard"
    DETAILED = "detailed"


class ToneSetting(str, Enum):
    """Tone settings for demand letters."""
    PROFESSIONAL = "professional"
    ASSERTIVE = "assertive"
    DIPLOMATIC = "diplomatic"


class DemandLetterEmphasis(str, Enum):
    """Content emphasis for demand letters."""
    LEGAL = "legal"
    MEDICAL = "medical"
    FINANCIAL = "financial"
    EMOTIONAL = "emotional"


class AnalysisPreferences(BaseModel):
    """User preferences for document analysis."""
    selected_analyses: List[AnalysisType] = Field(
        default=[],
        description="List of selected analysis types to perform"
    )
    detail_level: DetailLevel = Field(
        default=DetailLevel.STANDARD,
        description="Level of detail for analysis"
    )
    content_emphasis: ContentEmphasis = Field(
        default=ContentEmphasis.FACTS,
        description="Primary content emphasis"
    )
    analysis_style: AnalysisStyle = Field(
        default=AnalysisStyle.NARRATIVE,
        description="Presentation style for analysis"
    )


class DemandLetterPreferences(BaseModel):
    """User preferences for demand letter generation."""
    sections: List[DemandLetterSection] = Field(
        default=[
            DemandLetterSection.INTRODUCTION,
            DemandLetterSection.FACTS,
            DemandLetterSection.LIABILITY,
            DemandLetterSection.DAMAGES,
            DemandLetterSection.CONCLUSION
        ],
        description="Sections to include in demand letter"
    )
    length: LetterLength = Field(
        default=LetterLength.STANDARD,
        description="Length of demand letter"
    )
    tone: ToneSetting = Field(
        default=ToneSetting.PROFESSIONAL,
        description="Tone of demand letter"
    )
    content_emphasis: DemandLetterEmphasis = Field(
        default=DemandLetterEmphasis.LEGAL,
        description="Primary content emphasis for demand letter"
    )


class UserPreferences(BaseModel):
    """Complete user preferences model."""
    analysis: AnalysisPreferences = Field(
        default_factory=AnalysisPreferences,
        description="Analysis preferences"
    )
    demand_letter: DemandLetterPreferences = Field(
        default_factory=DemandLetterPreferences,
        description="Demand letter preferences"
    )


class PreferencesUpdateRequest(BaseModel):
    """Request model for updating preferences."""
    analysis: Optional[AnalysisPreferences] = None
    demand_letter: Optional[DemandLetterPreferences] = None


class PreferencesResponse(BaseModel):
    """Response model for preferences."""
    preferences: UserPreferences
    message: str = "Preferences retrieved successfully"
