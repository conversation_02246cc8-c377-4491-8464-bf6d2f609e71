"""
Pydantic request models for API endpoints.
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from .preferences import AnalysisType, UserPreferences


class LoginRequest(BaseModel):
    """Login request model."""
    username: str = Field(..., min_length=1, max_length=50)
    password: str = Field(..., min_length=1)


class DocumentUploadRequest(BaseModel):
    """Document upload request model."""
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="MIME type of the file")
    file_size: int = Field(..., gt=0, description="File size in bytes")
    
    @validator('content_type')
    def validate_content_type(cls, v):
        allowed_types = [
            'application/pdf',
            'image/png',
            'image/jpeg',
            'image/jpg',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]
        if v not in allowed_types:
            raise ValueError(f'Content type {v} not allowed')
        return v


class AnalysisRequest(BaseModel):
    """Analysis request model."""
    document_ids: List[str] = Field(..., description="List of document IDs to analyze")
    preferences: UserPreferences = Field(..., description="User preferences for analysis")
    selected_analyses: List[AnalysisType] = Field(..., description="Specific analyses to perform")


class GenerationRequest(BaseModel):
    """Content generation request model."""
    analysis_results: Dict[str, Any] = Field(..., description="Analysis results to use for generation")
    preferences: UserPreferences = Field(..., description="User preferences for generation")
    generation_type: str = Field(..., description="Type of content to generate")


class SessionRequest(BaseModel):
    """Session management request model."""
    action: str = Field(..., description="Session action (create, update, destroy)")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Session data")


class FeedbackRequest(BaseModel):
    """User feedback request model."""
    rating: int = Field(..., ge=1, le=5, description="Rating from 1 to 5")
    comment: Optional[str] = Field(default=None, max_length=1000, description="Optional feedback comment")
    feature: Optional[str] = Field(default=None, description="Feature being rated")


class ProgressRequest(BaseModel):
    """Progress tracking request model."""
    task_id: str = Field(..., description="Task ID to track")
    action: str = Field(..., description="Progress action (start, update, complete, error)")
    progress: Optional[int] = Field(default=None, ge=0, le=100, description="Progress percentage")
    message: Optional[str] = Field(default=None, description="Progress message")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Additional progress data")
