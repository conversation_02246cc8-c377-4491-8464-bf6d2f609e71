"""
Pydantic response models for API endpoints.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from .preferences import UserPreferences


class BaseResponse(BaseModel):
    """Base response model with common fields."""
    success: bool = Field(default=True, description="Whether the request was successful")
    message: str = Field(default="Success", description="Response message")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class ErrorResponse(BaseResponse):
    """Error response model."""
    success: bool = Field(default=False)
    error_code: Optional[str] = Field(default=None, description="Error code")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Error details")


class LoginResponse(BaseResponse):
    """Login response model."""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: Dict[str, Any] = Field(..., description="User information")


class UserResponse(BaseResponse):
    """User information response model."""
    user: Dict[str, Any] = Field(..., description="User information")


class DocumentResponse(BaseResponse):
    """Document upload response model."""
    document_id: str = Field(..., description="Unique document identifier")
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="MIME type")
    file_size: int = Field(..., description="File size in bytes")
    document_type: Optional[str] = Field(default=None, description="Detected document type")
    processing_status: str = Field(default="uploaded", description="Processing status")


class DocumentListResponse(BaseResponse):
    """Document list response model."""
    documents: List[DocumentResponse] = Field(..., description="List of uploaded documents")
    total_count: int = Field(..., description="Total number of documents")


class AnalysisResponse(BaseResponse):
    """Analysis response model."""
    analysis_id: str = Field(..., description="Unique analysis identifier")
    analysis_type: str = Field(..., description="Type of analysis performed")
    status: str = Field(..., description="Analysis status")
    result: Optional[str] = Field(default=None, description="Analysis result")
    progress: int = Field(default=0, description="Progress percentage")
    started_at: datetime = Field(..., description="Analysis start time")
    completed_at: Optional[datetime] = Field(default=None, description="Analysis completion time")


class AnalysisListResponse(BaseResponse):
    """Analysis list response model."""
    analyses: List[AnalysisResponse] = Field(..., description="List of analyses")
    total_count: int = Field(..., description="Total number of analyses")


class GenerationResponse(BaseResponse):
    """Content generation response model."""
    generation_id: str = Field(..., description="Unique generation identifier")
    generation_type: str = Field(..., description="Type of content generated")
    status: str = Field(..., description="Generation status")
    content: Optional[str] = Field(default=None, description="Generated content")
    export_url: Optional[str] = Field(default=None, description="URL for content export")
    progress: int = Field(default=0, description="Progress percentage")
    started_at: datetime = Field(..., description="Generation start time")
    completed_at: Optional[datetime] = Field(default=None, description="Generation completion time")


class SessionResponse(BaseResponse):
    """Session response model."""
    session_id: str = Field(..., description="Session identifier")
    status: str = Field(..., description="Session status")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Session data")
    expires_at: datetime = Field(..., description="Session expiration time")


class SessionStatsResponse(BaseResponse):
    """Session statistics response model."""
    documents_uploaded: int = Field(default=0, description="Number of documents uploaded")
    analyses_completed: int = Field(default=0, description="Number of analyses completed")
    generations_completed: int = Field(default=0, description="Number of generations completed")
    session_duration: int = Field(default=0, description="Session duration in seconds")
    tokens_used: int = Field(default=0, description="Number of tokens used")
    current_activity: Optional[str] = Field(default=None, description="Current activity")


class ProgressResponse(BaseResponse):
    """Progress tracking response model."""
    task_id: str = Field(..., description="Task identifier")
    status: str = Field(..., description="Task status")
    progress: int = Field(default=0, description="Progress percentage")
    message: Optional[str] = Field(default=None, description="Progress message")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Additional progress data")
    started_at: datetime = Field(..., description="Task start time")
    updated_at: datetime = Field(..., description="Last update time")


class HealthResponse(BaseResponse):
    """Health check response model."""
    status: str = Field(default="healthy", description="Service health status")
    version: str = Field(..., description="API version")
    environment: str = Field(..., description="Environment name")
    uptime: int = Field(..., description="Uptime in seconds")
    dependencies: Dict[str, str] = Field(default_factory=dict, description="Dependency status")
