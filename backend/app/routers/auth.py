"""
Authentication router for CaseBuilder AI.
Handles user login, logout, and token management.
"""

from fastapi import API<PERSON>outer, Depends, HTTPException, status, Response, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from datetime import datetime, timedelta
import jwt
import logging
from typing import Optional

from ..models.requests import LoginRequest
from ..models.responses import LoginResponse, UserResponse, BaseResponse, ErrorResponse
from ..services.auth_service import AuthService
from ..middleware.session import session_store
from ..config import get_settings
from ..dependencies import get_auth_service

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()
settings = get_settings()


@router.post("/login", response_model=LoginResponse)
async def login(
    login_request: LoginRequest,
    response: Response,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Authenticate user and create session.
    
    Args:
        login_request: Login credentials
        response: FastAPI response object
        auth_service: Authentication service
        
    Returns:
        LoginResponse with access token and user info
    """
    try:
        # Authenticate user
        user = await auth_service.authenticate_user(
            login_request.username, 
            login_request.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password"
            )
        
        # Create JWT token
        token_data = {
            "sub": user.username,
            "user_id": str(user.user_id),
            "exp": datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
        }
        
        access_token = jwt.encode(
            token_data, 
            settings.secret_key, 
            algorithm=settings.algorithm
        )
        
        # Create volatile session
        session_id = session_store.create_session(str(user.user_id))
        
        # Set session cookie
        response.set_cookie(
            key="session_id",
            value=session_id,
            max_age=settings.session_expire_seconds,
            httponly=True,
            secure=settings.environment == "production",
            samesite="lax"
        )
        
        # Update last login
        await auth_service.update_last_login(user.username)
        
        logger.info(f"User {user.username} logged in successfully")
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
            user={
                "username": user.username,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "email": user.email,
                "user_id": user.user_id
            },
            message="Login successful"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during login"
        )


@router.post("/logout", response_model=BaseResponse)
async def logout(
    request: Request,
    response: Response,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Logout user and destroy session.
    
    Args:
        request: FastAPI request object
        response: FastAPI response object
        credentials: JWT credentials
        
    Returns:
        BaseResponse confirming logout
    """
    try:
        # Get session ID from request
        session_id = getattr(request.state, "session_id", None)
        
        if session_id:
            # Delete session
            session_store.delete_session(session_id)
            
            # Clear session cookie
            response.delete_cookie(key="session_id")
        
        logger.info("User logged out successfully")
        
        return BaseResponse(message="Logout successful")
        
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during logout"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Get current user information.
    
    Args:
        request: FastAPI request object
        credentials: JWT credentials
        
    Returns:
        UserResponse with current user info
    """
    try:
        # Decode JWT token
        token = credentials.credentials
        payload = jwt.decode(
            token, 
            settings.secret_key, 
            algorithms=[settings.algorithm]
        )
        
        username = payload.get("sub")
        if not username:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        
        # Get session data
        session_data = getattr(request.state, "session_data", None)
        if not session_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No active session"
            )
        
        return UserResponse(
            user={
                "username": username,
                "user_id": session_data.get("user_id"),
                "session_id": getattr(request.state, "session_id", None)
            },
            message="User information retrieved successfully"
        )
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired"
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    except Exception as e:
        logger.error(f"Get current user error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/refresh", response_model=LoginResponse)
async def refresh_token(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Refresh JWT token.
    
    Args:
        request: FastAPI request object
        credentials: JWT credentials
        
    Returns:
        LoginResponse with new access token
    """
    try:
        # Decode current token
        token = credentials.credentials
        payload = jwt.decode(
            token, 
            settings.secret_key, 
            algorithms=[settings.algorithm],
            options={"verify_exp": False}  # Allow expired tokens for refresh
        )
        
        username = payload.get("sub")
        user_id = payload.get("user_id")
        
        if not username or not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        
        # Create new token
        token_data = {
            "sub": username,
            "user_id": user_id,
            "exp": datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
        }
        
        new_access_token = jwt.encode(
            token_data, 
            settings.secret_key, 
            algorithm=settings.algorithm
        )
        
        return LoginResponse(
            access_token=new_access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
            user={
                "username": username,
                "user_id": user_id
            },
            message="Token refreshed successfully"
        )
        
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during token refresh"
        )
