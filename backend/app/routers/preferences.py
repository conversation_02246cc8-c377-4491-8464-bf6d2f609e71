"""
Preferences router for CaseBuilder AI.
Handles user preferences for analysis and demand letter generation.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
import logging
from typing import Dict, Any

from ..models.preferences import (
    UserPreferences, 
    PreferencesUpdateRequest, 
    PreferencesResponse,
    AnalysisType
)
from ..models.responses import BaseResponse
from ..services.auth_service import User
from ..dependencies import get_current_user, get_current_session, validate_session_access
from ..middleware.session import session_store

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=PreferencesResponse)
async def get_preferences(
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get current user preferences.
    
    Args:
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        PreferencesResponse with current preferences
    """
    try:
        session_data = await get_current_session(request)
        
        # Get preferences from session or return defaults
        preferences_data = session_data.get("preferences", {})
        
        # Create UserPreferences object with defaults if not set
        preferences = UserPreferences()
        
        # Update with stored preferences if they exist
        if preferences_data:
            if "analysis" in preferences_data:
                preferences.analysis = preferences.analysis.copy(
                    update=preferences_data["analysis"]
                )
            if "demand_letter" in preferences_data:
                preferences.demand_letter = preferences.demand_letter.copy(
                    update=preferences_data["demand_letter"]
                )
        
        logger.info(f"Retrieved preferences for user {user.username}")
        
        return PreferencesResponse(
            preferences=preferences,
            message="Preferences retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error retrieving preferences for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve preferences"
        )


@router.put("/", response_model=PreferencesResponse)
async def update_preferences(
    preferences_update: PreferencesUpdateRequest,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Update user preferences.
    
    Args:
        preferences_update: Preferences update request
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        PreferencesResponse with updated preferences
    """
    try:
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)
        
        # Get current preferences or create defaults
        current_preferences = session_data.get("preferences", {})
        
        # Update analysis preferences if provided
        if preferences_update.analysis:
            current_preferences["analysis"] = preferences_update.analysis.dict()
        
        # Update demand letter preferences if provided
        if preferences_update.demand_letter:
            current_preferences["demand_letter"] = preferences_update.demand_letter.dict()
        
        # Update session with new preferences
        session_store.update_session(session_id, {
            "preferences": current_preferences
        })
        
        # Create response with updated preferences
        updated_preferences = UserPreferences()
        if "analysis" in current_preferences:
            updated_preferences.analysis = updated_preferences.analysis.copy(
                update=current_preferences["analysis"]
            )
        if "demand_letter" in current_preferences:
            updated_preferences.demand_letter = updated_preferences.demand_letter.copy(
                update=current_preferences["demand_letter"]
            )
        
        logger.info(f"Updated preferences for user {user.username}")
        
        return PreferencesResponse(
            preferences=updated_preferences,
            message="Preferences updated successfully"
        )
        
    except Exception as e:
        logger.error(f"Error updating preferences for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update preferences"
        )


@router.post("/reset", response_model=PreferencesResponse)
async def reset_preferences(
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Reset user preferences to defaults.
    
    Args:
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        PreferencesResponse with default preferences
    """
    try:
        session_id = getattr(request.state, "session_id")
        
        # Reset preferences to defaults
        default_preferences = UserPreferences()
        
        # Update session with default preferences
        session_store.update_session(session_id, {
            "preferences": {
                "analysis": default_preferences.analysis.dict(),
                "demand_letter": default_preferences.demand_letter.dict()
            }
        })
        
        logger.info(f"Reset preferences for user {user.username}")
        
        return PreferencesResponse(
            preferences=default_preferences,
            message="Preferences reset to defaults"
        )
        
    except Exception as e:
        logger.error(f"Error resetting preferences for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset preferences"
        )


@router.get("/analysis-types", response_model=Dict[str, Any])
async def get_analysis_types():
    """
    Get available analysis types and their descriptions.
    
    Returns:
        Dictionary of analysis types with descriptions
    """
    analysis_types = {
        AnalysisType.POLICE_REPORT_SUMMARY: {
            "name": "Police Report Summary",
            "description": "Generate a comprehensive summary of police report contents",
            "category": "police_report"
        },
        AnalysisType.FACTS_LIABILITY: {
            "name": "Facts & Liability Analysis",
            "description": "Analyze facts and determine liability in the case",
            "category": "liability"
        },
        AnalysisType.IN_DEPTH_LIABILITY: {
            "name": "In-Depth Liability Analysis",
            "description": "Detailed liability analysis focusing on facts and applicable laws",
            "category": "liability"
        },
        AnalysisType.MEDICAL_ANALYSIS: {
            "name": "Medical Analysis",
            "description": "Comprehensive analysis of medical records and treatments",
            "category": "medical"
        },
        AnalysisType.MEDICAL_EXPENSES: {
            "name": "Medical Expenses",
            "description": "Calculate and analyze medical expenses from records",
            "category": "medical"
        },
        AnalysisType.FUTURE_TREATMENT: {
            "name": "Future Treatment",
            "description": "Analyze future medical treatment needs and costs",
            "category": "medical"
        },
        AnalysisType.ANALYZE_INJURIES: {
            "name": "Analyze Injuries",
            "description": "Detailed analysis of injuries and their impact",
            "category": "medical"
        },
        AnalysisType.ACCIDENT_SCENE: {
            "name": "Accident Scene",
            "description": "Analyze accident scene details and circumstances",
            "category": "accident"
        },
        AnalysisType.PROPERTY_DAMAGE: {
            "name": "Property Damage",
            "description": "Assess and analyze property damage claims",
            "category": "property"
        },
        AnalysisType.GENERATE_DEMAND_LETTER: {
            "name": "Generate Demand Letter",
            "description": "Create comprehensive demand letter with all case information",
            "category": "generation"
        }
    }
    
    return {
        "analysis_types": analysis_types,
        "categories": {
            "police_report": "Police Report Analysis",
            "liability": "Liability Analysis",
            "medical": "Medical Analysis",
            "accident": "Accident Analysis",
            "property": "Property Analysis",
            "generation": "Document Generation"
        }
    }


@router.get("/defaults", response_model=PreferencesResponse)
async def get_default_preferences():
    """
    Get default preferences configuration.
    
    Returns:
        PreferencesResponse with default preferences
    """
    default_preferences = UserPreferences()
    
    return PreferencesResponse(
        preferences=default_preferences,
        message="Default preferences retrieved successfully"
    )
