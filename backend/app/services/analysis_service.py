"""
Analysis service for integrating CaseBuilder with FastAPI backend.
Handles document analysis using CaseBuilder's existing handlers and commands.
"""

import logging
import asyncio
import base64
from typing import Dict, Any, List, Optional
from io import BytesIO

# CaseBuilder imports
from casebuilder.infrastructure.dependency_injection import Dependency<PERSON>ontainer
from casebuilder.application.commands.facts_liability_commands import <PERSON><PERSON><PERSON>f<PERSON>oss<PERSON>ommand, DetermineLiabilityCommand
from casebuilder.application.handlers.facts_liability_handlers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DetermineL<PERSON>Handler
from casebuilder.core.interfaces.IAIService import IAIService
from casebuilder.core.interfaces.ITextProcessorService import ITextProcessorService
from casebuilder.adapters.ai.openai_adapter import OpenAIAdapter
from casebuilder.core.services.text_processor_service import TextProcessorService

# PDF processing
try:
    from PyPDF2 import PdfReader
except ImportError:
    from PyPDF2 import Pdf<PERSON>ile<PERSON>eader as PdfReader

from ..middleware.session import session_store
from ..models.preferences import AnalysisType

logger = logging.getLogger(__name__)


class AnalysisService:
    """
    Service for processing document analysis using CaseBuilder's existing infrastructure.
    """
    
    def __init__(self):
        """Initialize the analysis service with CaseBuilder dependencies."""
        self.container = DependencyContainer()
        self._setup_dependencies()
    
    def _setup_dependencies(self):
        """Setup CaseBuilder dependencies."""
        try:
            # Initialize AI service
            self.ai_service = OpenAIAdapter()
            
            # Initialize text processor
            self.text_processor = TextProcessorService(self.ai_service)
            
            # Initialize handlers
            self.facts_handler = FactsOfLossHandler(self.ai_service, self.text_processor)
            self.liability_handler = DetermineLiabilityHandler(self.ai_service, self.text_processor)
            
            logger.info("CaseBuilder dependencies initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing CaseBuilder dependencies: {str(e)}")
            raise
    
    async def process_analysis(
        self,
        analysis_type: AnalysisType,
        document_ids: List[str],
        preferences: Dict[str, Any],
        session_id: str
    ) -> str:
        """
        Process analysis for the given documents and analysis type.
        
        Args:
            analysis_type: Type of analysis to perform
            document_ids: List of document IDs to analyze
            preferences: User preferences for analysis
            session_id: Session ID for retrieving documents
            
        Returns:
            Analysis result as formatted string
        """
        try:
            # Extract text from documents
            document_text = await self._extract_text_from_documents(document_ids, session_id)
            
            if not document_text.strip():
                return "No text content found in uploaded documents. Please ensure documents contain readable text."
            
            # Process based on analysis type
            if analysis_type == AnalysisType.POLICE_REPORT_SUMMARY:
                return await self._process_police_report_summary(document_text, preferences)
            elif analysis_type == AnalysisType.FACTS_LIABILITY:
                return await self._process_facts_liability(document_text, preferences)
            elif analysis_type == AnalysisType.IN_DEPTH_LIABILITY:
                return await self._process_in_depth_liability(document_text, preferences)
            else:
                return f"Analysis type {analysis_type.value} not yet implemented"
                
        except Exception as e:
            logger.error(f"Error processing analysis {analysis_type.value}: {str(e)}")
            raise
    
    async def _extract_text_from_documents(self, document_ids: List[str], session_id: str) -> str:
        """
        Extract text content from uploaded documents.
        
        Args:
            document_ids: List of document IDs
            session_id: Session ID for retrieving documents
            
        Returns:
            Combined text content from all documents
        """
        try:
            # Get session data
            session_data = session_store.get_session(session_id)
            if not session_data:
                raise ValueError("Session not found")
            
            # Get documents from session storage (they're stored as base64)
            documents = session_data.get("documents", {})
            combined_text = []
            
            for doc_id in document_ids:
                if doc_id in documents:
                    doc_data = documents[doc_id]
                    file_content = base64.b64decode(doc_data["content"])
                    
                    # Extract text based on file type
                    if doc_data["content_type"] == "application/pdf":
                        text = await self._extract_pdf_text(file_content)
                    elif doc_data["content_type"].startswith("image/"):
                        text = await self._extract_image_text(file_content)
                    else:
                        # Try to decode as text
                        try:
                            text = file_content.decode('utf-8')
                        except UnicodeDecodeError:
                            text = f"Could not extract text from {doc_data['filename']}"
                    
                    if text.strip():
                        combined_text.append(f"=== {doc_data['filename']} ===\n{text}\n")
            
            return "\n".join(combined_text)
            
        except Exception as e:
            logger.error(f"Error extracting text from documents: {str(e)}")
            raise
    
    async def _extract_pdf_text(self, pdf_content: bytes) -> str:
        """Extract text from PDF content."""
        try:
            pdf_file = BytesIO(pdf_content)
            pdf_reader = PdfReader(pdf_file)
            
            text_parts = []
            for page in pdf_reader.pages:
                text = page.extract_text()
                if text.strip():
                    text_parts.append(text)
            
            return "\n".join(text_parts)
        except Exception as e:
            logger.error(f"Error extracting PDF text: {str(e)}")
            return "Error extracting text from PDF"
    
    async def _extract_image_text(self, image_content: bytes) -> str:
        """Extract text from image using GPT-4o Vision."""
        return await self._extract_image_text_with_vision(image_content)

    async def _extract_image_text_with_vision(self, image_content: bytes) -> str:
        """Extract text from image using GPT-4o Vision."""
        try:
            # Use GPT-4o Vision for OCR
            instruction = """Please extract all text from this image.
            If this appears to be a police report, traffic collision report, or legal document,
            please extract all text accurately, maintaining the structure and formatting as much as possible.
            Return only the extracted text without any additional commentary."""

            text = await self.ai_service.process_image(
                image_data=image_content,
                instruction=instruction,
                model_alias="gpt-4o-2024-05-13"
            )
            return text
        except Exception as e:
            logger.error(f"Error extracting image text with Vision: {str(e)}")
            return f"Error extracting text from image: {str(e)}"
    
    async def _process_police_report_summary(self, document_text: str, preferences: Dict[str, Any]) -> str:
        """Process police report summary analysis."""
        try:
            # Create command for police report analysis
            command = FactsOfLossCommand(
                document_text=document_text,
                client_name=preferences.get('client_name', ''),
                additional_notes=preferences.get('additional_notes', ''),
                summary_only=True,
                detail_level=preferences.get('analysis_detail_level', 'standard'),
                style='narrative',
                emphasis={'liability': 3, 'property_damage': 3},
                include_witness=preferences.get('include_witness', False)
            )
            
            # Process with facts handler
            result = await self.facts_handler.handle(command)
            return result
            
        except Exception as e:
            logger.error(f"Error processing police report summary: {str(e)}")
            raise
    
    async def _process_facts_liability(self, document_text: str, preferences: Dict[str, Any]) -> str:
        """Process facts and liability analysis."""
        try:
            # Create command for facts and liability analysis
            command = FactsOfLossCommand(
                document_text=document_text,
                client_name=preferences.get('client_name', ''),
                additional_notes=preferences.get('additional_notes', ''),
                summary_only=False,
                detail_level=preferences.get('analysis_detail_level', 'standard'),
                style='narrative',
                emphasis={'liability': 5, 'property_damage': 3},
                include_witness=preferences.get('include_witness', True)
            )

            # Process with facts handler
            result = await self.facts_handler.handle(command)
            return result

        except Exception as e:
            logger.error(f"Error processing facts and liability: {str(e)}")
            raise

    async def _process_in_depth_liability(self, document_text: str, preferences: Dict[str, Any]) -> str:
        """Process in-depth liability analysis."""
        try:
            # Create command for in-depth liability analysis
            command = DetermineLiabilityCommand(
                document_text=document_text,
                client_name=preferences.get('client_name', ''),
                additional_notes=preferences.get('additional_notes', ''),
                in_depth=True,  # Flag to indicate we want an in-depth analysis
                detail_level=preferences.get('analysis_detail_level', 'facts_liability'),
                style='narrative',
                emphasis={'liability': 5, 'property_damage': 3},
                include_witness=preferences.get('include_witness', True)
            )

            # Process with liability handler
            result = await self.liability_handler.handle(command)
            return result

        except Exception as e:
            logger.error(f"Error processing in-depth liability: {str(e)}")
            raise
