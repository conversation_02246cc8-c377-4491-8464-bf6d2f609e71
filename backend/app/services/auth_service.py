"""
Authentication service for CaseBuilder AI.
Handles user authentication and database operations.
"""

import mysql.connector
import os
import logging
from typing import Optional
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass

from ..config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class User:
    """User model for authentication."""
    username: str
    first_name: str
    last_name: Optional[str] = None
    email: Optional[str] = None
    user_id: Optional[int] = None
    last_login: Optional[datetime] = None

    @property
    def full_name(self) -> str:
        """Get the user's full name."""
        if self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name


class AuthService:
    """Service for handling user authentication."""
    
    def __init__(self):
        """Initialize the authentication service."""
        self.settings = get_settings()
    
    def _get_connection(self):
        """
        Get database connection using the same logic as the legacy system.
        
        Returns:
            mysql.connector connection object
            
        Raises:
            RuntimeError: If connection fails
        """
        try:
            db_user = self.settings.database_user
            db_pass = self.settings.database_password
            db_name = self.settings.database_name

            # Detect if we're in Cloud Run or local environment
            if self.settings.instance_connection_name:
                # Production using socket
                db_socket_dir = self.settings.db_socket_dir
                cloud_sql_connection_name = self.settings.instance_connection_name

                conn = mysql.connector.connect(
                    user=db_user,
                    password=db_pass,
                    database=db_name,
                    unix_socket=f"{db_socket_dir}/{cloud_sql_connection_name}"
                )
            else:
                # Local using IP and port
                db_host = self.settings.database_host
                db_port = self.settings.database_port

                conn = mysql.connector.connect(
                    user=db_user,
                    password=db_pass,
                    database=db_name,
                    host=db_host,
                    port=db_port
                )

            return conn

        except mysql.connector.Error as e:
            logger.error(f"Database connection failed: {str(e)}")
            raise RuntimeError(f"Database connection failed: {str(e)}")
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        Authenticate user with username and password.
        
        Args:
            username: User's username
            password: User's password (plain text, as in legacy system)
            
        Returns:
            User object if authentication successful, None otherwise
            
        Raises:
            RuntimeError: If database operation fails
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Execute query (same as legacy login_user function)
            cursor.execute(
                'SELECT * FROM user WHERE username = %s AND password = %s',
                (username, password)
            )

            # Fetch result
            result = cursor.fetchone()

            # Close resources
            cursor.close()
            conn.close()

            # Return User object if authentication successful
            if result:
                # Assuming columns: id, username, password, first_name, last_name, email, last_login
                user = User(
                    username=result[1],
                    first_name=result[3],
                    last_name=result[4] if len(result) > 4 and result[4] else None,
                    email=result[5] if len(result) > 5 and result[5] else None,
                    user_id=result[0],
                    last_login=result[6] if len(result) > 6 and result[6] else None
                )

                logger.info(f"User {username} authenticated successfully")
                return user

            logger.warning(f"Authentication failed for user {username}")
            return None

        except Exception as e:
            logger.error(f"Authentication error for user {username}: {str(e)}")
            raise RuntimeError(f"Authentication failed: {str(e)}")
    
    async def update_last_login(self, username: str) -> bool:
        """
        Update user's last login timestamp.
        
        Args:
            username: User's username
            
        Returns:
            True if update successful, False otherwise
            
        Raises:
            RuntimeError: If database operation fails
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Get current timestamp in GMT-7 (as per user preference)
            gmt_minus_7 = timezone(timedelta(hours=-7))
            current_time = datetime.now(gmt_minus_7)

            # Update last login timestamp
            cursor.execute(
                'UPDATE user SET last_login = %s WHERE username = %s',
                (current_time, username)
            )

            # Commit the transaction
            conn.commit()

            # Check if update was successful
            success = cursor.rowcount > 0

            # Close resources
            cursor.close()
            conn.close()

            if success:
                logger.info(f"Updated last login for user {username}")
            else:
                logger.warning(f"Failed to update last login for user {username}")

            return success

        except Exception as e:
            logger.error(f"Error updating last login for user {username}: {str(e)}")
            raise RuntimeError(f"Failed to update last login: {str(e)}")
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """
        Get user information by username.
        
        Args:
            username: User's username
            
        Returns:
            User object if found, None otherwise
            
        Raises:
            RuntimeError: If database operation fails
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Execute query
            cursor.execute(
                'SELECT * FROM user WHERE username = %s',
                (username,)
            )

            # Fetch result
            result = cursor.fetchone()

            # Close resources
            cursor.close()
            conn.close()

            # Return User object if found
            if result:
                user = User(
                    username=result[1],
                    first_name=result[3],
                    last_name=result[4] if len(result) > 4 and result[4] else None,
                    email=result[5] if len(result) > 5 and result[5] else None,
                    user_id=result[0],
                    last_login=result[6] if len(result) > 6 and result[6] else None
                )

                return user

            return None

        except Exception as e:
            logger.error(f"Error getting user {username}: {str(e)}")
            raise RuntimeError(f"Failed to get user: {str(e)}")
