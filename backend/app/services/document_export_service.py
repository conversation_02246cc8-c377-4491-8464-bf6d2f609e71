"""
Document export service for CaseBuilder AI.
Handles conversion of analysis results to various formats including Word (.docx).
"""

import logging
import io
import re
from typing import Optional, Dict, Any
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE

logger = logging.getLogger(__name__)


class DocumentExportService:
    """Service for exporting analysis results to various document formats."""
    
    def __init__(self):
        """Initialize the document export service."""
        pass
    
    def export_to_word(
        self, 
        content: str, 
        title: str = "Analysis Report",
        client_name: str = "Client",
        analysis_type: str = "Analysis"
    ) -> io.BytesIO:
        """
        Export analysis content to Word document (.docx).
        
        Args:
            content: The analysis content to export
            title: Document title
            client_name: Client name for the document
            analysis_type: Type of analysis
            
        Returns:
            BytesIO object containing the Word document
        """
        try:
            # Create a new Document
            doc = Document()
            
            # Add document title
            title_paragraph = doc.add_heading(title, 0)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add subtitle with analysis type and client
            subtitle = f"{analysis_type} for {client_name}"
            subtitle_paragraph = doc.add_heading(subtitle, level=1)
            subtitle_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add a line break
            doc.add_paragraph()
            
            # Process the content and add to document
            self._add_formatted_content(doc, content)
            
            # Save to BytesIO
            doc_stream = io.BytesIO()
            doc.save(doc_stream)
            doc_stream.seek(0)
            
            logger.info(f"Successfully exported {analysis_type} to Word document")
            return doc_stream
            
        except Exception as e:
            logger.error(f"Error exporting to Word: {str(e)}")
            raise
    
    def _add_formatted_content(self, doc: Document, content: str) -> None:
        """
        Add formatted content to the Word document.
        Handles markdown-style formatting and converts to Word formatting.
        
        Args:
            doc: The Word document object
            content: The content to add
        """
        try:
            # Split content into lines
            lines = content.split('\n')
            current_paragraph = None
            
            for line in lines:
                line = line.strip()
                
                # Skip empty lines but add paragraph break
                if not line:
                    if current_paragraph is not None:
                        doc.add_paragraph()
                        current_paragraph = None
                    continue
                
                # Handle bold headers (markdown style **HEADER**)
                if line.startswith('**') and line.endswith('**') and len(line) > 4:
                    # This is a header
                    header_text = line[2:-2].strip()
                    header_paragraph = doc.add_heading(header_text, level=2)
                    current_paragraph = None
                    continue
                
                # Handle regular headers (markdown style # Header)
                if line.startswith('#'):
                    # Count the number of # to determine header level
                    level = 0
                    for char in line:
                        if char == '#':
                            level += 1
                        else:
                            break
                    
                    header_text = line[level:].strip()
                    # Limit header level to 3 for Word compatibility
                    word_level = min(level, 3)
                    header_paragraph = doc.add_heading(header_text, level=word_level)
                    current_paragraph = None
                    continue
                
                # Handle table rows (markdown style |col1|col2|col3|)
                if line.startswith('|') and line.endswith('|'):
                    # This might be a table row
                    self._handle_table_row(doc, line)
                    current_paragraph = None
                    continue
                
                # Handle bullet points
                if line.startswith('- ') or line.startswith('* '):
                    bullet_text = line[2:].strip()
                    bullet_paragraph = doc.add_paragraph(bullet_text, style='List Bullet')
                    current_paragraph = None
                    continue
                
                # Handle numbered lists
                if re.match(r'^\d+\.\s', line):
                    list_text = re.sub(r'^\d+\.\s', '', line).strip()
                    list_paragraph = doc.add_paragraph(list_text, style='List Number')
                    current_paragraph = None
                    continue
                
                # Regular paragraph text
                if current_paragraph is None:
                    current_paragraph = doc.add_paragraph()
                
                # Add the line to current paragraph
                # Handle inline bold formatting
                self._add_formatted_text(current_paragraph, line)
                
        except Exception as e:
            logger.error(f"Error formatting content: {str(e)}")
            # Fallback: add content as plain text
            doc.add_paragraph(content)
    
    def _add_formatted_text(self, paragraph, text: str) -> None:
        """
        Add text with inline formatting to a paragraph.
        
        Args:
            paragraph: The Word paragraph object
            text: The text to add with potential formatting
        """
        try:
            # Handle inline bold text (**text**)
            parts = re.split(r'(\*\*.*?\*\*)', text)
            
            for part in parts:
                if part.startswith('**') and part.endswith('**') and len(part) > 4:
                    # Bold text
                    bold_text = part[2:-2]
                    run = paragraph.add_run(bold_text)
                    run.bold = True
                else:
                    # Regular text
                    paragraph.add_run(part)
                    
        except Exception as e:
            logger.error(f"Error adding formatted text: {str(e)}")
            # Fallback: add as plain text
            paragraph.add_run(text)
    
    def _handle_table_row(self, doc: Document, line: str) -> None:
        """
        Handle table row formatting (basic implementation).
        
        Args:
            doc: The Word document object
            line: The table row line
        """
        try:
            # For now, just add as formatted text
            # A full table implementation would require tracking table state
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            if cells:
                table_text = ' | '.join(cells)
                paragraph = doc.add_paragraph(table_text)
                # Make it slightly indented to show it's tabular data
                paragraph.paragraph_format.left_indent = Inches(0.25)
                
        except Exception as e:
            logger.error(f"Error handling table row: {str(e)}")
            # Fallback: add as plain text
            doc.add_paragraph(line)
    
    def get_filename(self, analysis_type: str, client_name: str = "client") -> str:
        """
        Generate appropriate filename for the document.
        
        Args:
            analysis_type: Type of analysis
            client_name: Client name
            
        Returns:
            Formatted filename
        """
        # Clean the analysis type for filename
        clean_type = analysis_type.replace('_', '-').replace(' ', '-').lower()
        clean_client = re.sub(r'[^\w\s-]', '', client_name).replace(' ', '-').lower()
        
        return f"{clean_type}-{clean_client}.docx"
