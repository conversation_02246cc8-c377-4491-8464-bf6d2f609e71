"""
Token management service for CaseBuilder AI.
Handles token allocation, usage tracking, and deduction for analyses and demand generation.
Uses the existing button_clicks table structure from the legacy system.
"""

import logging
import mysql.connector
import os
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class TokenService:
    """
    Service for managing user tokens and usage tracking.
    Uses the existing button_clicks table structure.

    Token costs:
    - Analysis: 1 token per analysis
    - Demand Letter Generation: 5 tokens per generation
    """

    # Token cost constants
    ANALYSIS_TOKEN_COST = 1
    DEMAND_GENERATION_TOKEN_COST = 5
    DEFAULT_TOKEN_ALLOCATION = 2500
    BUTTON_NAME = "compose_demand"  # Use existing button name from legacy system

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def _get_connection(self):
        """Get database connection using the same logic as legacy system."""
        try:
            db_user = os.getenv("DATABASE_USER")
            db_pass = os.getenv("DATABASE_PASSWORD")
            db_name = os.getenv("DATABASE_NAME")

            # Detecta si estamos en Cloud Run o en local
            if os.getenv("INSTANCE_CONNECTION_NAME"):
                # Estamos en producción usando socket
                db_socket_dir = os.getenv("DB_SOCKET_DIR", "/cloudsql")
                cloud_sql_connection_name = os.getenv("INSTANCE_CONNECTION_NAME")

                conn = mysql.connector.connect(
                    user=db_user,
                    password=db_pass,
                    database=db_name,
                    unix_socket=f"{db_socket_dir}/{cloud_sql_connection_name}"
                )
            else:
                # Estamos en local usando IP y puerto
                db_host = os.getenv("DATABASE_HOST", "127.0.0.1")
                db_port = int(os.getenv("DATABASE_PORT", 3306))

                conn = mysql.connector.connect(
                    user=db_user,
                    password=db_pass,
                    database=db_name,
                    host=db_host,
                    port=db_port
                )
            return conn
        except mysql.connector.Error as e:
            self.logger.error(f"Database connection failed: {str(e)}")
            raise RuntimeError(f"Database connection failed: {str(e)}")

    async def get_user_tokens(self, username: str) -> int:
        """
        Get the available tokens for a user using the button_clicks table.

        Args:
            username: Username to check tokens for

        Returns:
            Number of available tokens
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Get token info from button_clicks table
            cursor.execute(
                'SELECT * FROM button_clicks WHERE username = %s AND button_name = %s',
                (username, self.BUTTON_NAME)
            )

            result = cursor.fetchone()
            cursor.close()
            conn.close()

            if result:
                # result format: [id, username, button_name, click_count, limit_count]
                click_count = int(result[3])
                limit_count = int(result[4])
                available_tokens = max(0, limit_count - click_count)

                self.logger.info(f"User {username} has {available_tokens} tokens available")
                return available_tokens
            else:
                self.logger.warning(f"No token allocation found for user: {username}")
                return 0

        except Exception as e:
            self.logger.error(f"Error getting user tokens for {username}: {str(e)}")
            return 0
    
    async def deduct_tokens(self, username: str, token_cost: int, action_type: str) -> bool:
        """
        Deduct tokens from a user's account using the button_clicks table.

        Args:
            username: Username to deduct tokens from
            token_cost: Number of tokens to deduct
            action_type: Type of action (for logging)

        Returns:
            True if tokens were successfully deducted, False otherwise
        """
        try:
            # Check available tokens first
            available_tokens = await self.get_user_tokens(username)

            if available_tokens < token_cost:
                self.logger.warning(
                    f"Insufficient tokens for user {username}. "
                    f"Required: {token_cost}, Available: {available_tokens}"
                )
                return False

            # Get current token info
            conn = self._get_connection()
            cursor = conn.cursor()

            cursor.execute(
                'SELECT * FROM button_clicks WHERE username = %s AND button_name = %s',
                (username, self.BUTTON_NAME)
            )

            result = cursor.fetchone()

            if not result:
                cursor.close()
                conn.close()
                self.logger.warning(f"No token allocation found for user {username}")
                return False

            # Update token usage
            button_id = result[0]
            current_click_count = int(result[3])
            new_click_count = current_click_count + token_cost

            cursor.execute(
                'UPDATE button_clicks SET click_count = %s WHERE id = %s',
                (new_click_count, button_id)
            )

            conn.commit()
            cursor.close()
            conn.close()

            self.logger.info(
                f"Deducted {token_cost} tokens from user {username} for {action_type}. "
                f"Remaining: {available_tokens - token_cost}"
            )
            return True

        except Exception as e:
            self.logger.error(f"Error deducting tokens for {username}: {str(e)}")
            return False
    
    async def check_and_deduct_tokens(self, username: str, action_type: str) -> tuple[bool, int]:
        """
        Check if user has enough tokens and deduct them if so.
        
        Args:
            username: Username to check and deduct tokens for
            action_type: Type of action ('analysis' or 'demand_generation')
            
        Returns:
            Tuple of (success, remaining_tokens)
        """
        try:
            # Determine token cost based on action type
            if action_type == 'demand_generation':
                token_cost = self.DEMAND_GENERATION_TOKEN_COST
            else:  # analysis or other actions
                token_cost = self.ANALYSIS_TOKEN_COST
            
            # Check available tokens
            available_tokens = await self.get_user_tokens(username)
            
            if available_tokens < token_cost:
                self.logger.warning(
                    f"Insufficient tokens for {action_type}. "
                    f"User: {username}, Required: {token_cost}, Available: {available_tokens}"
                )
                return False, available_tokens
            
            # Deduct tokens
            success = await self.deduct_tokens(username, token_cost, action_type)
            
            if success:
                remaining_tokens = available_tokens - token_cost
                return True, remaining_tokens
            else:
                return False, available_tokens
                
        except Exception as e:
            self.logger.error(f"Error in check_and_deduct_tokens for {username}: {str(e)}")
            return False, 0
    
    async def get_token_cost(self, action_type: str) -> int:
        """
        Get the token cost for a specific action type.
        
        Args:
            action_type: Type of action
            
        Returns:
            Number of tokens required
        """
        if action_type == 'demand_generation':
            return self.DEMAND_GENERATION_TOKEN_COST
        else:
            return self.ANALYSIS_TOKEN_COST
    
    async def refund_tokens(self, username: str, token_amount: int, reason: str) -> bool:
        """
        Refund tokens to a user's account using the button_clicks table.

        Args:
            username: Username to refund tokens to
            token_amount: Number of tokens to refund
            reason: Reason for refund (for logging)

        Returns:
            True if tokens were successfully refunded, False otherwise
        """
        try:
            # Get current token info
            conn = self._get_connection()
            cursor = conn.cursor()

            cursor.execute(
                'SELECT * FROM button_clicks WHERE username = %s AND button_name = %s',
                (username, self.BUTTON_NAME)
            )

            result = cursor.fetchone()

            if not result:
                cursor.close()
                conn.close()
                self.logger.warning(f"No token allocation found for user {username}")
                return False

            # Refund tokens by reducing click count
            button_id = result[0]
            current_click_count = int(result[3])
            new_click_count = max(0, current_click_count - token_amount)

            cursor.execute(
                'UPDATE button_clicks SET click_count = %s WHERE id = %s',
                (new_click_count, button_id)
            )

            conn.commit()
            cursor.close()
            conn.close()

            self.logger.info(
                f"Refunded {token_amount} tokens to user {username}. "
                f"Reason: {reason}"
            )
            return True

        except Exception as e:
            self.logger.error(f"Error refunding tokens for {username}: {str(e)}")
            return False


# Global token service instance
token_service = TokenService()
