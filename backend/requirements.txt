# FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0

# Database
mysql-connector-python==8.0.28

# HTTP client
aiohttp==3.8.4
aiofiles==24.1.0

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Async support
asyncio-mqtt==0.13.0

# File processing (for document handling)
python-docx==0.8.11
PyPDF2==3.0.1
Pillow==9.5.0
pytesseract==0.3.10

# OpenAI integration
openai==1.3.0

# Text processing
tiktoken==0.5.1

# Logging and monitoring
structlog==23.2.0

# Development dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Optional: Redis for session storage (if needed in production)
redis==4.5.5

# Optional: WebSocket support
websockets==12.0
