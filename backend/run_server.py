#!/usr/bin/env python3
"""
Stable script to run the FastAPI server.
IMPORTANT: Auto-reload disabled to prevent crashes on file changes.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import uvicorn
import uvicorn

if __name__ == "__main__":
    # STABILITY FIX: Disable auto-reload to prevent crashes
    # Auto-reload was causing server instability on file changes
    # For development with auto-reload, use: uvicorn app.main:app --reload
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # DISABLED: Prevents crashes on file changes
        log_level="info"
    )
