#!/usr/bin/env python3
"""
Development script to run the FastAPI server with auto-reload.
WARNING: This may cause crashes on file changes. Use only for active development.
For stable operation, use run_server.py instead.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import uvicorn
import uvicorn

if __name__ == "__main__":
    print("🚨 WARNING: Running in development mode with auto-reload enabled")
    print("🚨 This may cause server crashes on file changes")
    print("🚨 For stable operation, use: python3 run_server.py")
    print("="*60)
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # ENABLED: For development only
        log_level="debug"
    )
