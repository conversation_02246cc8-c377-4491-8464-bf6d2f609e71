import streamlit as st
from gpt_api import chat_with_model #chat_with_gpt4
from text_processing import process_text_in_segments, process_full_prompt


def get_prompts(task_name, consolidated_info=""):
    
    prompts = {
        'police_report_summary': {
            'instruction': """
                You are an AI legal expert specialized in personal injury cases. 
                Your task is to analyze and summarize the provided police report concerning a traffic collision. 
                Focus on key facts, party involvement, observations by the officer, and any critical evidence or witnesses, 
                ensuring the summary is clear, concise, and relevant for legal professionals.
            """,
            'sub_instructions': {
                'Accident Details': """
                    - State the exact date, time, and location of the accident.
                    - Describe, in chronological order, the sequence of events leading up to the collision.
                    - Emphasize any significant actions or movements of the involved parties.
                """,
                'Parties Involved': """
                    - List all individuals or entities involved (drivers, passengers, etc.).
                    - Provide names, vehicle details (make, model), and roles (e.g., driver, passenger).
                """,
                'Officer Observations': """
                    - Summarize key observations or findings made by the officer.
                    - Include any preliminary assessment regarding the cause or any traffic infractions noted.
                """,
                'Damages and Injuries': """
                    - Describe property damage, especially to vehicles.
                    - Include injuries sustained by any parties and reference if medical attention was provided on-site 
                    or if transportation to medical facilities was necessary.
                """,
                'Witnesses and Evidence': """
                    - Identify witnesses (if any) and any relevant statements they provided.
                    - Note any additional evidence (photographs, videos, measurements) mentioned in the report.
                """
            },
            'summary': """
                Provide a concise but comprehensive summary of the police report. 
                The summary should be structured in a way that allows an attorney to quickly understand 
                the key points of the accident, the individuals involved, damages, and any initial officer conclusions.
            """,
            'examples': {
                'summary_details': """
                    **Police Report Summary Example**:
                    
                    - **Date and Time**: July 15, 2022, at 4:20 PM
                    - **Location**: Intersection of Pine St and Elm St, [City], [State]
                    - **Parties Involved**:
                    - Driver A: John Perez (Toyota Corolla)
                    - Driver B: Maria Garcia (Honda Civic)
                    - **Sequence of Events**:
                    John Perez was stopped at a red light when Maria Garcia, 
                    failing to stop, rear-ended his Corolla. 
                    - **Officer Observations**:
                    The responding officer noted significant rear-end damage to the Corolla 
                    and issued Maria Garcia a citation for reckless driving.
                    - **Damages and Injuries**:
                    - Toyota Corolla: Severe rear-end damage.
                    - Maria Garcia: Minor contusion, treated on-scene.
                    - **Witnesses and Evidence**:
                    - Witness Carlos Lopez: Confirmed Maria Garcia ran the red light.
                    - Photographs of the accident scene were attached in the report.
                """
                }
            },

        'facts_of_loss': {
            'instruction': """
                You are an AI legal expert specializing in personal injury cases. 
                Your task is to produce a clear, chronological, and objective narrative 
                of the circumstances leading to the traffic collision based solely on the provided police report.
                
                1. Analyze the facts in the report and form a concise, fact-based account of how the collision occurred.
                2. **Before finalizing**, review your narrative to ensure it does not include:
                - Witness statements or testimonies that are not explicitly documented in the report.
                - Any other detail (e.g., injuries, road conditions) that the report does not mention.
                3. If the report omits a detail (e.g., no witnesses, no injuries), omit that detail in your final narrative.
                4. Provide only the final, corrected version in your response, without showing the revision process.
                
                The goal is a factual summary suitable for legal professionals, 
                strictly reflecting the content of the police report, 
                and free of any "invented" information.
            """,
            'sub_instructions': {
                'Incident Overview': """
                    - State the date, time, and location of the incident (only if explicitly given).
                    - Provide a chronological account of events leading to the collision.
                    - Emphasize how each moment connects in the timeline, but do not guess any missing info.
                """,
                'Involved Parties': """
                    - Identify all individuals or entities (drivers, passengers) mentioned in the report.
                    - Specify roles and pertinent details only if the report provides them.
                """,
                'Actions Leading to the Incident': """
                    - Detail the specific actions or behaviors by each party that contributed to the accident,
                    referencing only what is in the report.
                """,
                'Conditions and Environment': """
                    - Describe environmental factors (weather, lighting, road conditions) only if mentioned.
                """,
                'Officer Observations': """
                    - Summarize key points made by the reporting officer regarding possible fault or infractions.
                    - Do not include opinions not documented in the report.
                """,
                'Witness Accounts': """
                    - Include witness statements only if they are explicitly documented.
                    - Omit this entire section if the report does not mention any witness statements.
                """,
                'Damages and Injuries': """
                    - Outline property damage (vehicles, structures) or injuries mentioned.
                    - If not documented, do not speculate on possible injuries or damages.
                """
            },
            'summary': """
                Produce a cohesive, fact-based narrative integrating all relevant details from the report. 
                Then, verify it does not contain any data not included in the report. 
                Provide only the final version after this verification. 
                If certain sections (e.g., Witnesses) are absent in the report, either omit them 
                or state explicitly that the report does not mention them.
                """,
            'examples': {
                'facts_of_loss_narration': """
                    **Facts of Loss Narrative Example**:

                    On July 15, 2022, at approximately 4:20 PM, a traffic collision occurred
                    at the intersection of Pine St and Elm St in [City], [State]. The incident
                    involved two vehicles: a Toyota Corolla (John Perez) and a Honda Civic (Maria Garcia).

                    According to the police report, John Perez was waiting at a red light
                    when Maria Garcia failed to stop, rear-ending his vehicle. The impact caused
                    substantial damage to the Corolla's rear. The weather at the time was clear,
                    with dry road conditions. Officer Sarah Thompson, who responded to the scene,
                    noted that Maria Garcia was traveling at an excessive speed and did not yield
                    at the stop sign, directly contributing to the collision.

                    If there are no witness statements, you would omit this section entirely.
                    If a witness is mentioned, e.g., Carlos Lopez, include only what is explicitly
                    stated in the report. For instance: 
                    "Witness Carlos Lopez confirmed that Maria Garcia did not adhere to the traffic signals."

                    Based on the evidence gathered, Officer Thompson issued a citation to Maria Garcia
                    for reckless driving.
                """
            }
        },

        'determine_liability': {
            'instruction': """
                You are an AI legal expert. 
                A traffic collision report is provided with factual details and the officer's opinion of who is at fault. 
                Ignore the officer's opinion entirely. 
                Instead, determine who is liable based solely on the factual evidence 
                (e.g., descriptions of events, witness statements, physical damage) stated in the report. 
                If the facts are inconclusive or insufficient, state that clearly.
            """,
            'sub_instructions': {
                'Liability Analysis': """
                    - Identify which party or parties appear to be at fault, relying on the factual events 
                    and any documented actions or negligence.
                    - Do not include or reference the officer’s conclusion at all.
                """,
                'Factual Evidence Only': """
                    - Use only the concrete details in the report (e.g., statements from drivers/witnesses, 
                    vehicle positions, road conditions).
                    - Omit any detail not explicitly mentioned in the factual portion of the documents.
                """,
                'Alternative Scenarios': """
                    - If the facts suggest multiple causes or shared liability, address them 
                    without considering the officer’s stance.
                    - If the report doesn’t provide enough information to assign fault definitively, 
                    indicate that the evidence is insufficient.
                """
                },
            'summary': """
                Provide a concise statement of who is at fault, strictly grounded in the reported facts. 
                Ignore any direct or implied conclusion from the officer, and do not rely on subjective opinions. 
                If liability cannot be determined from the facts, note that the evidence is inconclusive.
            """,
            'examples': {
                'determine_liability_summary': """
                    **Determine Liability Summary Example**:
                    
                    Based on the factual descriptions (driver statements, vehicle damage, 
                    and the positions reported), it appears that Maria Garcia failed to yield 
                    at a stop sign and collided with John Perez’s vehicle. No information in the report 
                    indicates wrongdoing on Perez’s part. 
                    
                    Therefore, Garcia is likely liable for the collision. 
                    The officer’s opinion is not considered in this conclusion.
                """
            }
        },

        'facts_and_liability_narrative': {
            'instruction': f"""
                As an AI legal expert, you are tasked with crafting a narrative for a personal injury legal demand for {st.session_state.get('client_name', 'the client')}. 
                The narrative should cover the 'Facts' and 'Liability' sections, adapting to the available information in the uploaded documents.

                - **Facts**: Create a detailed narrative of the accident from {st.session_state.get('client_name', 'our client')}'s perspective, including specific 
                details such as the date, time, location, actions of all involved parties, and the sequence of events leading to the accident. 
                Summarize any key observations or conclusions made by the reporting officer **but maintain that our client’s account 
                is truthful, even if the TCR contradicts it**. You may point out potential oversights or biases in the TCR or the officer’s conclusions.

                - **Liability**: Assess and establish the liability of the other party involved. Your narrative should illustrate how their actions or negligence 
                directly led to the accident and the resulting injuries to {st.session_state.get('client_name', 'our client')}. Cite any relevant laws or regulations 
                that were violated and explain how these violations contributed to the accident. If no explicit details are provided, infer the most likely scenario 
                based on common legal principles. **Even if the TCR suggests fault might lie elsewhere, prioritize our client’s assertion of innocence and highlight 
                why the other party is at fault.**

                **User Additional Notes** (if any):
                {st.session_state.get('additional_notes', '')}

                (Please incorporate any relevant points from these notes into your Facts or Liability analysis, 
                ensuring they bolster our client's position or clarify disputed details.)
            """,
            'sub_instructions': {
                'Facts': """
                    Weave the accident's facts into a compelling narrative, highlighting the client's experience 
                    and the circumstances leading to the incident. If the TCR's account conflicts with our client's statement, 
                    emphasize that the client denies any fault indicated in the report, and consider possible reasons 
                    the TCR might be incomplete or inaccurate.
                """,
                'Liability': """
                    Build a strong narrative that points to the negligence or fault of the other party, 
                    using legal reasoning to establish their liability. Include references to any officer 
                    observations if available, but do not hesitate to question them if they clash with 
                    the client's version of events. Position our client as the victim, whose perspective 
                    we fully support.
                """
            },
            'summary': f"""
                Construct two narrative paragraphs based on the information provided:

                1. **Facts**: Create a narrative that vividly describes the accident, emphasizing the role and actions of the other party involved. This narrative 
                should paint a clear picture of the events, portraying {st.session_state.get('client_name', 'our client')} as the victim of the circumstances, 
                **even if the TCR suggests otherwise.** Incorporate any details from a police report if available, 
                but remain critical of any conclusions that contradict the client's statements.

                2. **Liability**: Formulate a narrative that logically argues the liability of the other party. Use the facts to show negligence or breach of legal duty, 
                leading to {st.session_state.get('client_name', 'our client')}'s injuries and damages. If a police report is not available, rely on the extracted details 
                and infer likely legal outcomes. **If the TCR or officer's opinion conflicts with our client's innocence, explicitly address and refute it, 
                maintaining that the other party bears responsibility.**
            """,
            'examples': {
                'liability_analysis': f"""
                    **Liability Analysis:**
                    1. **Liable Party**: Jane Smith.
                    2. **Actions Leading to Accident**: Jane Smith failed to yield at the stop sign, entering the intersection abruptly 
                    and colliding with {st.session_state.get('client_name', 'our client')}'s vehicle. 
                    Even if the TCR mentions potential fault by our client, we assert that the report overlooked 
                    key evidence showing Ms. Smith's negligent turn caused the collision.
                """,
            }
        },

        'facts_and_liability_demand': {
            'instruction': f"""
                You are an AI legal expert tasked with creating a persuasive narrative for a personal injury demand 
                on behalf of {st.session_state.get('client_name', 'the client')}. The narrative must contain two sections:

                1. **Facts**: Provide a detailed account of the accident from our client's perspective. 
                - Include the date, time, location, and the actions of all involved parties 
                    leading to the collision. 
                - If a police report (TCR) is available, incorporate any factual observations 
                    (e.g., physical evidence, witness statements) but ignore the officer's subjective conclusions. 
                - If no police report is provided, rely on other available documents or common sense 
                    to infer the sequence of events.

                2. **Liability**: Establish how the other party’s negligence or breach of duty 
                directly caused the accident and led to {st.session_state.get('client_name', 'our client')}'s injuries. 
                - If relevant, cite laws or regulations evidently violated (e.g., failing to yield, speeding). 
                - Emphasize how these violations confirm the other party's fault and support the claim 
                    for damages and injuries.

                Use actual names or descriptive terms for the parties (e.g., “John Smith,” “our client”); 
                avoid impersonal labels like “Unit 1.” Write with a compelling tone that favors our client’s position, 
                while still grounding the account in the documented facts.

                **User Additional Notes** (if any):
                {st.session_state.get('additional_notes', '')}

                (Please incorporate any relevant points from these notes into your Facts or Liability analysis, 
                ensuring they bolster our client's position or clarify disputed details.)
            """,
            'sub_instructions': {
                'Facts': """
                    - Summarize the circumstances from the client's viewpoint, 
                    detailing date/time/location/actions as precisely as possible.
                    - If a TCR is present, include factual items like damage assessments or witness quotes, 
                    ignoring any officer opinions. 
                    - Portray our client as the victim, highlighting any negligent acts by the other party.
                """,
                'Liability': """
                    - Argue why the other party is legally responsible, 
                    linking their actions (or failures) to the collision and the client’s resulting injuries.
                    - Cite traffic rules or legal principles if the documents clearly indicate them.
                    - Maintain a persuasive yet fact-based tone, oriented in favor of our client's claim.
                """
            },
            'summary': f"""
                Construct two narrative paragraphs:

                1. **Facts Paragraph**: Vividly describe how the accident happened from our client's perspective, 
                incorporating any factual data from a TCR if available, or from other sources if not.

                2. **Liability Paragraph**: Show how the other party's negligence led directly to 
                {st.session_state.get('client_name', 'our client')}'s injuries and damages, 
                citing any relevant legal standards as needed.
            """,
            'examples': {
                'narrative_example': f"""
                    **Facts**: 
                    On August 19, 2023, at approximately 4:30 PM, our client, Jane Alvarez, was driving 
                    along Maple Avenue when John Smith suddenly swerved into her lane without signaling. 
                    According to the factual notes in the police report, Smith's vehicle struck the rear 
                    passenger side of Ms. Alvarez’s car, causing it to spin and collide with a guardrail. 
                    Ms. Alvarez sustained whiplash and bruising, requiring immediate medical attention. 
                    (No officer opinions are referenced here—only the documented physical evidence 
                    and the statements of both drivers.)

                    **Liability**: 
                    By abruptly changing lanes and failing to check for oncoming traffic, John Smith 
                    clearly violated standard rules of safe driving, constituting negligence. 
                    This unsafe maneuver directly caused the crash, resulting in injuries to Ms. Alvarez 
                    and damage to her vehicle. Consequently, Smith bears full responsibility for the accident 
                    and any resulting damages, as his failure to drive with due care and caution 
                    contravenes established traffic safety regulations.
                """
            }
        },

        'detailed_medical_analysis': {
            'instruction': f"""
                As an AI legal expert, your task is to produce a comprehensive narrative analysis of the medical records 
                from a single medical provider for a personal injury case involving {st.session_state.get('client_name', 'the client')}.
                Your analysis should present a vivid and detailed account of the injuries sustained and the treatment journey, 
                written as a cohesive narrative rather than a bullet list.

                Begin by describing the client's injuries in depth. Explain the nature, severity, and specific impact of each key injury on the client's daily life, using clear, descriptive language. Base your account solely on the facts from the medical records and avoid any speculation. 
                **For key findings—such as tears, fractures, brain injuries, or when a treatment recommendation (like surgery or injection) is made—include the corresponding page reference in the format [Page X].** 
                If a finding is mentioned with multiple references, use only the lowest page number.

                Next, chronologically recount the medical visits and treatments. Weave these events into a continuous narrative that details:
                - The initial emergency response.
                - Each subsequent medical encounter, including dates, the treatments administered, and any significant diagnostic findings.
                When describing a key diagnostic result or a treatment recommendation, include its page reference (e.g., [Page 6]) only once and exactly as provided in the input text.

                **User Additional Notes (if any):**
                {st.session_state.get('additional_notes', '')}

                (Incorporate any relevant points from these notes into your analysis if they enhance clarity or fill in gaps in the records.)

                Finally, if the medical records include ICD-10 codes for the diagnoses, append a concise table that matches each code with its corresponding injury. If no codes are provided, indicate that no ICD-10 codes were specified.
            """,
            'sub_instructions': {
                'injury_details': """
                    **Injury Details**

                    Compose a narrative section that details the most critical injuries sustained by the client. 
                    Focus on injuries that have a significant impact on daily life (for example, tears, fractures, brain injuries, etc.).
                    For each such key injury, include a page reference (e.g., [Page 4]) exactly as found in the records.
                    Do not include page references for minor or less critical details.
                """,
                'chronological_medical_visits': """
                    **Chronological Medical Visits**

                    Provide a coherent, chronological account of the client's medical visits and treatments.
                    Clearly state the date of each visit, and when mentioning a key diagnostic or treatment recommendation 
                    (such as a surgery, injection, or other major intervention), include its page reference (e.g., [Page 6]) only once.
                    The timeline should reflect the progression of the client's recovery without overloading every minor detail.
                """
            },
            'summary': """
                **Summary**

                Create a comprehensive medical analysis that includes:

                1. **Injury Details**: A narrative describing the client's most significant injuries and their impact on daily life, 
                including page references only for the key findings (e.g., tears, fractures, brain injuries, or recommended surgeries/injections).

                2. **Chronological Medical Visits**: A clear timeline of all medical visits and treatments with dates, 
                including page references only for crucial diagnostic or treatment details.

                3. **ICD-10 Codes**: If the records include ICD-10 diagnoses, present them in a table matching each injury with its code;
                otherwise, note that no ICD-10 codes were provided.
            """,
            'examples': {
                'medical_analysis': f"""
                    **Medical Analysis Example**

                    Following the collision, Ms. Plaintiff was taken to Big Village Hospital, where she reported severe pain in her left side.
                    The emergency room staff noted tenderness in her elbow and knee and administered x-rays, prescribing Tylenol for pain relief.
                    Despite initial treatment, Ms. Plaintiff continued to experience debilitating pain.
                    
                    Key Findings:
                    - Traumatic Brain Injury (TBI) - Mild (S06.2X9D) [Page 5]: A brain MRI revealed evidence consistent with a mild TBI,
                    contributing to cognitive impairments and memory loss.
                    - Fracture of the left wrist [Page 3]: Imaging confirmed a fracture that required immobilization.
                    
                    Ms. Plaintiff's recovery continued with physical therapy. On November 21, 2074, she began therapy at Tx Group,
                    where she reported persistent pain in her left shoulder, elbow, and lower back.
                    Notably, her physical therapist recommended an injection for pain management [Page 6].
                    
                    **Sample ICD-10 Codes** (if provided):
                    | ICD-10 Code | Diagnosis                          |
                    |-------------|------------------------------------|
                    | S06.2X9D    | Mild Traumatic Brain Injury        |
                    | S62.0XXA    | Fracture of wrist, initial encounter|
                """
            }
        },

        'medical_expenses': {
            'instruction': f"""
            As an AI legal assistant, your task is to compile a **structured and provider-based summary** of the current medical expenses incurred by {st.session_state.get('client_name', 'the client')} as a result of the incident.

            Your response must include:

            ---

            ### 1. Medical Expenses Table

            Group all medical costs by **provider or facility**, not by individual treatment.

            For each provider:
            - Aggregate the treatments into a **summarized description** (e.g., Initial Exam, X-rays, Therapy Sessions).
            - Indicate the **overall treatment period** (from earliest to latest date of service).
            - Include **page references** in the summary (e.g., [Page 3]) for key treatments if mentioned in the records.
            - Calculate the **total cost** incurred from that provider.

            Only use verifiable information from the medical records.  
            If any detail (like a date or total cost) is missing, indicate “Not specified”.

            ---

            ### 2. Contextual Narrative

            Write a brief, formal narrative that:
            - Introduces the table and its purpose.
            - Explains how the aggregated costs reflect necessary medical care.
            - Supports the claim for compensation by highlighting the documented financial impact.

            Do not guess or fabricate any details. Use only the data extracted from the provided records.
            """,
                'sub_instructions': {
                    'table_format': """
            Present the medical expenses in a table with the following columns:

            - Medical Provider/Facility
            - Treatment/Procedure Summary (include page references when available)
            - Treatment Period (From – To)
            - Total Cost

            Each row should represent a **single provider**, summarizing all treatments and their combined cost.
            """,
                    'details_on_extraction': """
            - Review the uploaded records (e.g., bills, medical summaries, invoices).
            - Group all treatments by provider.
            - Summarize treatments into a clear, professional description.
            - Include page references (e.g., [Page 5]) for major procedures if present in the document.
            - Calculate the total cost per provider.
            - If a date or cost is not available, write “Not specified”.
            """
                },
                'summary': f"""
            Generate a concise financial summary composed of:

            1. A **Medical Expenses Table** listing each provider, a summary of treatments received, treatment period, and total cost.

            2. A **Contextual Narrative** that emphasizes the medical necessity of these expenses and their role in supporting the damages claim.
            Use clear and factual language, without speculation.
            """,
                'examples': {
                    'medical_expenses_table': """
            ### Aggregated Medical Expenses Table (Format Reference)

            | Medical Provider/Facility      | Treatment/Procedure Summary                                  | Treatment Period        | Total Cost  |
            |--------------------------------|--------------------------------------------------------------|--------------------------|-------------|
            | Waleed Arshaid DC              | Initial Exam, X-ray, Stim, Ultrasound, Massage, Adjustments [Pages 1, 3] | 06/09/2022 – 08/30/2022 | $2,500.00   |
            | Dr. Michael Moheimani          | Orthopedic Evaluation, Medication, Medical Report [Page 2]   | 10/03/2022              | $1,952.00   |
            | Charles Gruver, M.D.           | Telehealth Consultations, Cervical Epidural Injection [Pages 4–5] | 04/24/2023 – 05/25/2023 | $5,275.00   |
            | Montclair Open MRI             | MRI Cervical Spine                                            | 11/03/2022              | $1,950.00   |
            | Vigilant Anesthesia, Inc.      | Anesthesia Services                                           | 05/10/2023              | $2,850.00   |
            |--------------------------------|--------------------------------------------------------------|--------------------------|-------------|
            | **Total Cost**                 |                                                              |                          | **$14,527.00** |
            """,
                    'contextual_narrative': f"""
            ### Contextual Narrative (Reference Example)

            The table above outlines the aggregated current medical expenses incurred by {st.session_state.get('client_name', 'the client')} as documented across several medical providers. Each entry summarizes treatments received from a specific provider and includes page references for key procedures when available.

            These costs reflect medically necessary care provided as part of the client’s recovery journey. The expenses are well-documented and serve as clear evidence of the financial impact of the injuries, justifying full compensation for the amounts incurred.
            """
                }
            },


        'medical_expenses2': {
            'instruction': f"""
                As an AI legal expert, your task is to extract and compile a table of current medical expenses 
                from the provided documents related to {st.session_state.get('client_name', 'the client')}'s case.
                Instead of listing each treatment separately, you must group the expenses by medical provider or facility.
                For each provider, aggregate the treatments into a summarized description, indicate the overall treatment period 
                (from the earliest to the latest date among the treatments), and calculate the total cost incurred.
                
                Additionally, if the records include page references for the treatment details, incorporate those references 
                (using the format [Page X]) in the Treatment/Procedure Summary for verification purposes.
                
                Specifically, extract the following details:
                - **Medical Provider/Facility**: The name of the hospital, clinic, or provider.
                - **Treatment/Procedure Summary**: A brief summary of the treatments and procedures received from that provider. 
                Include page references for key treatment details when available.
                - **Treatment Period (From – To)**: The overall range of dates covering the treatments.
                - **Total Cost**: The sum of all costs incurred from that provider.
                
                Present the information in a clear table format. If any detail is missing, indicate "Not specified". 
                Use only the data available from the documents.
            """,
            'sub_instructions': {
                'table_format': """
                    Present the aggregated information in a table with the following columns:
                    - Medical Provider/Facility
                    - Treatment/Procedure Summary (include page references for key treatments when available)
                    - Treatment Period (From – To)
                    - Total Cost

                    Each row should represent a single provider or facility, summarizing all treatments and their combined cost.
                """,
                'details_on_extraction': """
                    - Review the uploaded documents (such as bills, treatment summaries, or receipts) to extract each expense entry.
                    - Group the entries by the medical provider or facility.
                    - For each provider, create a summary of the treatments (e.g., "Initial exam, X-ray, follow-up consultations"). 
                    If the records mention page numbers for any key treatment or procedure, include them in the summary in the format [Page X].
                    - Determine the overall treatment period as the range from the earliest treatment date to the latest.
                    - Sum the costs of all treatments for that provider.
                    - If exact dates are not available, provide the best approximation.
                """
            },
            'summary': f"""
                Compile a detailed analysis that includes:

                1. **Medical Expenses Table**: A table listing the aggregated current medical expenses for {st.session_state.get('client_name', 'the client')}. 
                Each row should include:
                    - The provider or facility name.
                    - A summarized list of the treatments or procedures received (with page references for key details when available).
                    - The overall treatment period.
                    - The total cost incurred from that provider.
                
                2. **Contextual Narrative**: A brief narrative that explains the significance of these aggregated expenses for the client's recovery and 
                supports the claim for damages. The narrative should clarify that these costs reflect the actual amounts charged for the treatments.
            """,
            'examples': {
                'medical_expenses_table': """
                    **Aggregated Medical Expenses Table Example:**

                    | Medical Provider/Facility      | Treatment/Procedure Summary                                  | Treatment Period          | Total Cost  |
                    |--------------------------------|--------------------------------------------------------------|---------------------------|-------------|
                    | Waleed Arshaid DC              | Initial Exam, X-ray, Electric Stim, Ultrasound, Massage, Hot Pack, Follow Up, Adj 1-2 Regions [Pages 1, 3] | 06/09/2022 – 08/30/2022   | $2,500.00   |
                    | Michael Moheimani              | Comprehensive Orthopaedic Consultation, Special Report, Medications [Page 2]   | 10/03/2022                | $1,952.00   |
                    | Charles Gruver, M.D.           | Telemedicine Consultations, Cervical Epidural Injection [Pages 4, 5]        | 04/24/2023 – 05/25/2023    | $5,275.00   |
                    | Montclair Open MRI             | MRI of the Cervical Spine                                    | 11/03/2022                | $1,950.00   |
                    | Vigilant Anesthesia, Inc.      | Anesthesia Services                                          | 05/10/2023                | $2,850.00   |
                    |--------------------------------|--------------------------------------------------------------|---------------------------|-------------|
                    | **Total Cost**                 |                                                              |                           | **$14,527.00** |
                """,
                'contextual_narrative': f"""
                    **Contextual Narrative:**

                    Based on the submitted records, the table above aggregates the current medical expenses incurred by {st.session_state.get('client_name', 'the client')}. 
                    Each row represents a provider with a summarized description of the treatments, including page references for key treatment details when available.
                    This aggregated view provides a clear overview of the financial burden from necessary treatments and supports the claim for full compensation.
                """
            }
        },

        'comprehensive_medical_analysis': {
            'instruction': f"""
            As an AI legal assistant, your task is to produce a thorough and structured **medical narrative** for {st.session_state.get('client_name', 'the client')}, based on their treatment history and medical records following a personal injury incident.

            Your analysis must be:
            - Factually accurate and grounded exclusively in the data provided.
            - Structured in a natural, chronological narrative.
            - Divided clearly into sections that reflect the injury progression, treatment steps, and client-reported symptoms.

            ---

            ### SECTION 1 — Injury Details

            Describe all major injuries sustained by the client. For each one:
            - Detail the severity, nature, and location of the injury.
            - Explain the resulting limitations in mobility, function, or daily life.
            - Do not simply list symptoms — weave them into a cohesive narrative.
            - Avoid any assumptions or exaggeration — stick to the evidence.

            ---

            ### SECTION 2 — Chronological Medical Visits

            Organize all medical visits in timeline order, beginning with the initial treatment or emergency response.

            For each encounter:
            - Identify the provider and date.
            - Describe the client’s condition, treatments performed, and follow-up plans.
            - Include relevant quotes or complaints from the client.
            - If multiple providers were involved, group visits under each provider but maintain the overall timeline flow.

            ---

            ### SECTION 3 — User Notes (if applicable)

            {st.session_state.get('additional_notes', '')}

            If the user has included additional observations or context, incorporate those insights carefully — only if they clarify or enhance the narrative.

            ---

            ### STYLE & CLARITY GUIDELINES

            - Use professional and formal language.
            - Maintain a flowing, story-like structure.
            - Highlight key milestones in the recovery process.
            - Avoid generic phrases like "ongoing pain" unless clearly supported.
            - Do not invent missing facts — omit them entirely if not provided.
            """,
                'sub_instructions': {
                    'injury_details': """
            Describe each major injury and how it affected the client's body, comfort, and ability to perform daily activities. Use clinical terminology where appropriate, but write in a clear and natural tone.

            Tie symptoms to physical limitations — e.g., numbness affecting walking, or pain preventing sleep.
            """,
                    'chronological_medical_visits': """
            Lay out each medical visit in order of occurrence. For each one:
            - Mention the provider name, specialty (if relevant), and date.
            - Explain the reason for the visit and the outcome.
            - Note any referrals, prescriptions, or follow-up plans.
            - Include notable quotes from the client when available.
            """
                },
                'summary': """
            Create a narrative that provides:

            1. A detailed review of the injuries sustained and how they affected the client's life.
            2. A chronological timeline of medical care, organized by provider (if multiple) but preserving the full sequence of events.
            3. Integration of user-provided notes (if available) to fill in gaps or clarify events.
            """,
                'examples': {
                    'medical_analysis': """
            ### Format Reference Only — Do Not Copy Content

            This example shows how to structure the analysis across multiple visits and providers. Always use the client’s actual data.

            ---

            After the accident, Ms. Plaintiff was transported to Greenfield Hospital by ambulance, reporting sharp lower back pain. The ER physician noted muscle spasms and tenderness in the lumbar region and prescribed anti-inflammatories.

            She then followed up with her primary care provider, Dr. Smith, on January 5. At that visit, she complained of persistent pain radiating down her right leg. Dr. Smith diagnosed lumbar radiculopathy and referred her to physical therapy.

            Between January 10 and February 28, Ms. Plaintiff attended 10 sessions at Active Rehab. She consistently reported difficulty standing for long periods and described numbness in her foot. On January 25, orthopedic specialist Dr. Lee ordered an MRI, which revealed a moderate disc bulge at L4–L5. An epidural steroid injection was administered on February 2.

            Ms. Plaintiff noted temporary relief but continued to struggle with household tasks. Dr. Lee recommended a home exercise program and additional follow-up, and the case remained under review for possible surgical intervention.

            Throughout this period, Ms. Plaintiff expressed ongoing frustration with her limitations and difficulty sleeping due to discomfort.
            """
                }
            },

        'comprehensive_medical_analysis2': {
            'instruction': f"""
                Comprehensive Medical Analysis

                As an AI legal expert, your task is to craft a thorough and narrative analysis 
                of the medical records for a client involved in a personal injury case. 
                This may include records from one or **multiple** healthcare providers. 
                Your objective is to present a clear, detailed account of the injuries sustained 
                and the overall treatment journey, highlighting significant moments in a straightforward manner.

                1. **Injury Details**: 
                Begin by describing the injuries in depth, explaining their nature, severity, 
                and specific impact on the client's daily life. Base your narrative on the details 
                provided in the medical records—avoid assumptions or speculations. 
                Use vivid language to illustrate how these injuries affected the client’s physical capabilities 
                (e.g., pain, numbness, restricted mobility), integrating these details into a flowing story 
                rather than merely listing symptoms.

                2. **Chronological Medical Visits**:
                Recount the sequence of medical visits and treatments in chronological order, 
                starting from the initial emergency response (if applicable) and continuing 
                through each encounter with one or several providers. Emphasize how each step 
                contributed to the client’s progression or setbacks, and include specific examples 
                of any complaints or statements the client relayed to healthcare professionals. 
                - **Single Provider**: Keep a straightforward timeline from first visit to last. 
                - **Multiple Providers**: Organize the information by provider **while** 
                    maintaining an overall chronological flow. For instance, you may group sessions 
                    from Provider A, then Provider B, making clear where each fits in the timeline.

                3. **User Additional Notes (if any)**:
                {st.session_state.get('additional_notes', '')}

                (If the user has provided extra notes—such as clarifications, missing details, or personal observations— 
                please incorporate them into your analysis if they enhance or fill gaps in the medical narrative.)

                **Key Points to Incorporate**:
                - Injuries: severity, functional limitations, and everyday impact.
                - Treatments: a chronological account of each medical visit, including dates, procedures, 
                provider observations, or medications prescribed.
                - Client’s Own Reports: highlight what the client reported (e.g., pain scale, 
                difficulties in daily activities, emotional toll, etc.).
                - Multiple Providers: distinctly address each provider’s role and findings, 
                ensuring a cohesive timeline.

            """,
            'sub_instructions': {
                'injury_details': """
                    **Injury Details**

                    Describe each major injury's nature, severity, and daily-life impact, 
                    strictly referencing the facts in the medical documents. 
                    For each injury, detail any pain, weakness, numbness, or discomfort 
                    that hindered the client’s mobility or quality of life. 
                    Incorporate these descriptions into a natural narrative that conveys 
                    the client's struggles and challenges over time.
                """,
                'chronological_medical_visits': """
                    **Chronological Medical Visits**

                    Provide a chronological overview of the treatment history, 
                    noting the client's condition at each appointment, 
                    the treatments administered, and any significant changes or setbacks. 
                    If the client visited multiple providers, group each provider’s notes logically 
                    but maintain a clear overall timeline. 
                    Include notable remarks or complaints made by the client 
                    that may be relevant for a personal injury claim. 
                    Highlight relevant dates to show progression.
                """
            },
            'summary': """
                **Summary**

                Generate a comprehensive medical analysis that features:

                1. **Injury Details**: A detailed portrayal of the client's most critical injuries, 
                focusing on how each one affects mobility, comfort, and daily living. 
                Stick to verifiable information, avoiding speculation.

                2. **Chronological Medical Visits**: A timeline of all appointments, procedures, 
                and medical interventions, specifying each provider (if multiple) 
                and illustrating how each phase contributed to the client’s recovery 
                or ongoing symptoms. Incorporate the client’s reported experiences or complaints.
            """,
            'examples': {
                'medical_analysis': """
                    **Medical Analysis Example**

                    After the collision, Ms. Plaintiff was taken by ambulance to Greenfield Hospital, 
                    where she reported significant back and neck pain. The attending physician, Dr. Jones, 
                    noted muscle spasms in her lumbar region and prescribed anti-inflammatory medication. 
                    Over the following two weeks, Ms. Plaintiff experienced ongoing discomfort 
                    that limited her ability to sit for extended periods, prompting her to seek further evaluation 
                    from her primary care provider, Dr. Smith.

                    Dr. Smith diagnosed chronic lower back pain and referred her to physical therapy. 
                    From January 10 to February 22, Ms. Plaintiff attended 12 therapy sessions at Active Rehab Center, 
                    consistently reporting numbness in her right leg and difficulty performing routine tasks. 
                    Meanwhile, a separate orthopedic consultation with Dr. Lee on January 25 revealed 
                    a mild disc bulge at L4-L5, explaining her continued pain. Dr. Lee recommended 
                    an epidural steroid injection, which Ms. Plaintiff received on February 5. 
                    She noted slight improvement after the injection but continued to struggle with prolonged standing 
                    or walking more than a few minutes at a time.

                    By the end of February, Ms. Plaintiff's physical therapist reported gradual gains 
                    in strength and flexibility, though Ms. Plaintiff still mentioned occasional sharp pain 
                    when bending or lifting objects. Dr. Lee’s final evaluation on March 1 suggested 
                    further imaging to rule out additional disc damage, and Ms. Plaintiff was advised 
                    to continue a home exercise regimen. While some symptoms had improved, she remained unable 
                    to perform certain household tasks without assistance, indicating ongoing functional limitations.
                """
            }
        },

        'future_medical_expenses2': {
            'instruction': f"""
                As an AI legal expert, your task is to compile a detailed analysis of all foreseeable future medical expenses 
                that {st.session_state.get('client_name', 'the client')} is expected to incur. This analysis must include two parts:

                1. **Future Medical Expenses Table**: Create a table that lists all anticipated medical expenses related to the injuries, 
                including treatments, surgeries, injections, rehabilitation, therapy sessions, and other necessary medical procedures. 
                The table should have the following columns: Future Medical Expense, Frequency, Duration, Cost per Session, and Total Estimated Cost.

                2. **Contextual Narrative**: Write a concise narrative that explains the significance of these expenses in the context 
                of {st.session_state.get('client_name', 'the client')}'s ongoing recovery. Highlight how each expense directly supports 
                the client's treatment and why it is critical to their long-term health and overall claim for damages.

                Focus only on those expenses that are reasonably foreseeable and directly related to the injuries sustained. 
                Do not include speculative costs. Base your analysis solely on the available medical records and prognosis.
            """,
            'sub_instructions': {
                'table_format': """
                    Present the future medical expenses in a table with these columns:
                    - Future Medical Expense
                    - Frequency (e.g., Daily, Weekly, Monthly)
                    - Duration (e.g., 6 months, 1 year)
                    - Cost per Session
                    - Total Estimated Cost

                    Each row should represent a specific expense that is anticipated based on the client's injuries.
                """,
                'details_on_calculation': """
                    For the Total Estimated Cost, multiply the cost per session by the expected number of sessions 
                    over the indicated duration. This calculation should be clear and precise.
                """,
                'narrative_construction': f"""
                    Construct a narrative that:
                    - Introduces the table and explains its importance for {st.session_state.get('client_name', 'the client')}'s recovery.
                    - Highlights that each listed expense is directly linked to treating the client's injuries.
                    - Emphasizes why these expenses are essential for restoring the client's health and supports the claim for damages.
                    - Additionally, whenever you mention a recommended treatment (such as surgery, injection, or specific therapy sessions) 
                    in the narrative, include a page reference (e.g., [Page X]) if available from the records, so that the recommended treatments 
                    can be easily verified.
                """
            },
            'summary': f"""
                Produce a detailed analysis that includes:

                1. **Future Medical Expenses Table**: A table listing all foreseeable future medical expenses for {st.session_state.get('client_name', 'the client')}, 
                detailing the type of expense, frequency, duration, cost per session, and total estimated cost.

                2. *Contextual Narrative**: A narrative that explains how these expenses are essential for the client’s recovery and 
                how they contribute to the overall claim for damages. Include page references for any key treatment recommendations 
                mentioned in the narrative.
            """,
            'examples': {
                'future_medical_expenses_table': """
                    **Future Medical Expenses Table Example:**

                    | Future Medical Expense     | Frequency | Duration | Cost per Session | Total Estimated Cost |
                    |-----------------------------|-----------|----------|------------------|----------------------|
                    | Physical Therapy Sessions   | Weekly    | 6 months | $150             | $3,600               |
                    | Epidural Steroid Injections | Monthly   | 1 year   | $500             | $6,000               |
                    | MRI Scans                  | Biannually| 2 years  | $1,200           | $4,800               |
                    | Prescription Medications    | Monthly   | Ongoing  | $100             | $1,200               |
                    |---------------------------- |-----------|----------|------------------|----------------------|
                    | **Total Future Expenses**   |           |          |                  | **$15,600**          |
                """,
                'contextual_narrative': f"""
                    **Contextual Narrative:**

                    The future medical expenses table above outlines the anticipated costs that {st.session_state.get('client_name', 'the client')} 
                    is expected to incur for ongoing treatment and recovery. Each listed expense is directly related to the injuries sustained 
                    and is crucial for {st.session_state.get('client_name', 'the client')}'s long-term health. These expenses are essential 
                    for the client's recovery and are integral to the overall claim for damages.
                """
            }
        },

        'future_medical_expenses': {
            'instruction': f"""
            As an AI legal assistant, your task is to prepare a clear and structured analysis of all foreseeable **future medical expenses** that {st.session_state.get('client_name', 'the client')} is expected to incur as a direct result of their injuries.

            Your response must include **two parts**:

            1. **Future Medical Expenses Table**: A structured table listing all anticipated future treatments or medical costs. The table must include the following columns: 
            - Future Medical Expense
            - Frequency
            - Duration
            - Cost per Session
            - Total Estimated Cost

            2. **Contextual Narrative**: A concise but persuasive narrative that:
            - Introduces the table and explains its importance for the client’s recovery.
            - Connects each listed treatment or cost directly to the client’s injuries.
            - Justifies why these future medical needs are essential for full recovery and part of the total compensation.
            - Refers to page numbers from medical records (e.g., [Page X]) whenever citing specific treatment recommendations.

            Only include expenses that are **medically supported**, **reasonably foreseeable**, and clearly connected to the injuries.  
            **Do not include speculative treatments, vague ongoing care, or invented costs.**
            """,
                'sub_instructions': {
                    'table_format': """
            Present the future medical expenses in a table with the following columns:
            - Future Medical Expense
            - Frequency (e.g., Weekly, Monthly)
            - Duration (e.g., 6 months, 1 year)
            - Cost per Session
            - Total Estimated Cost

            Each row should represent a specific treatment or medical cost directly tied to the injuries.
            """,
                    'details_on_calculation': """
            The Total Estimated Cost must be calculated as:
            (Cost per Session) × (Number of Sessions, based on Frequency × Duration).

            Make sure your math is correct and clearly aligns with the entries in the table.
            """,
                    'narrative_construction': f"""
            The narrative must:
            - Highlight the necessity of each treatment for {st.session_state.get('client_name', 'the client')}'s recovery.
            - Show how these expenses contribute to long-term healing and rehabilitation.
            - Be clear, professional, and focused — avoid filler language or assumptions.
            - Reference page numbers (e.g., [Page X]) from medical records when specific treatments or recommendations are mentioned.
            """
                },
                'summary': f"""
            Create a structured future medical expenses analysis consisting of:

            1. A table of all relevant and medically supported future medical costs for {st.session_state.get('client_name', 'the client')} — organized by treatment type, frequency, duration, cost per session, and total cost.

            2. A narrative that explains how these expenses are tied to the client's recovery and claim for damages, with medical record references where applicable.
            """,
                'examples': {
                    'future_medical_expenses_table': """
            ### Format Example — Do Not Copy Values

            | Future Medical Expense     | Frequency | Duration | Cost per Session | Total Estimated Cost |
            |---------------------------|-----------|----------|------------------|----------------------|
            | Physical Therapy Sessions | Weekly    | 6 months | $150             | $3,600               |
            | Epidural Injections       | Monthly   | 1 year   | $500             | $6,000               |
            | MRI Scans                 | Biannually| 2 years  | $1,200           | $4,800               |
            | Prescription Medication   | Monthly   | 12 months| $100             | $1,200               |
            | **Total**                 |           |          |                  | **$15,600**          |
            """,
                    'contextual_narrative': f"""
            ### Contextual Narrative Example — Reference Only

            The table above outlines the future medical care that {st.session_state.get('client_name', 'the client')} is expected to undergo based on their current injuries and prognosis. These treatments — such as physical therapy [Page 3] and steroid injections [Page 6] — are not optional; they are medically necessary for managing pain, regaining mobility, and ensuring long-term recovery. Each cost has been estimated using the duration and frequency of treatment specified in the records. These expenses form a vital part of the damages claimed and are supported by ongoing medical evaluations.
            """
                }
            },


        'injury_details': {
            'instruction': f"""
                As an AI legal expert, your task is to compile a detailed, narrative list of the injuries sustained 
                by {st.session_state.get('client_name', 'the client')} in a personal injury case. 
                Review the provided medical records and reports to identify and accurately describe each injury, 
                focusing on the type, severity, and specific anatomical location.

                For key injuries—such as tears, fractures, brain injuries, or any injuries that warrant major treatments— 
                if the medical records provide a reference (e.g., a page number), include that reference in the format [Page X] 
                next to the description. Do not add references for minor details.
            """,
            'sub_instructions': {
                'injury_list': """
                    For each injury:
                    - Provide a clear description using proper medical terminology.
                    - Specify the severity (e.g., mild, moderate, severe) and the exact location on the body.
                    - If the injury is a key finding (e.g., a tear, fracture, brain injury, or one that led to a recommended procedure), 
                    append the page reference in the format [Page X] as found in the records.
                    - List the injuries in a bullet or numbered format for clarity.
                """
            }
        },

        'injury_codes': {
            'instruction': f"""
                As an AI legal expert, your task is to compile a table of ICD-10 codes for the injuries sustained 
                by {st.session_state.get('client_name', 'the client')} as documented in the provided case data. 
                Focus on matching each accurately described injury with its corresponding ICD-10 code.

                If the medical records include any reference (e.g., page number) alongside the injury diagnosis, 
                include that reference in the table next to the code.
            """,
            'sub_instructions': {
                'table_format': """
                    Create a table with the following two columns:
                    1. **Diagnose/Injuries**: List each injury or medical diagnosis in clear, understandable terms.
                    2. **ICD-10 Injury Code**: Provide the corresponding ICD-10 code that accurately represents the condition.
                    
                    Optionally, if a page reference is available for a diagnosis, append it in the same cell (e.g., "S52.5 [Page 3]").
                    
                    This table should serve as a concise reference for legal and insurance purposes.
                """,
                'example': """
                    Example table format:

                    | Diagnose/Injuries              | ICD-10 Injury Code            |
                    |--------------------------------|-------------------------------|
                    | Fractured Radius [Page 3]      | S52.5                         |
                    | Concussion [Page 4]            | S06.0                         |
                    | Whiplash Injury                | S13.4                         |

                    Ensure each entry is accurate and directly corresponds to the medical records.
                """
            }
        },

            



        'impact_on_lifestyle': {
            'instruction': """
                As an AI legal expert, assess the impact of the injuries sustained by the client on their lifestyle and daily activities. Your narrative should cover the changes in the client's quality of life due to the incident, including but not limited to emotional distress, inability to perform daily tasks, and any alterations to the client's relationships or recreational activities.
                
                **Key Points:**
                - Identify specific lifestyle changes that the client has experienced as a direct result of the incident.
                - Discuss emotional and psychological effects that may have arisen due to the injuries.
                - Emphasize how these lifestyle changes justify the compensation amount being sought in the demand letter.
            """,
            'sub_instructions': {
                'Lifestyle Changes': "Detail the significant changes in the client's daily routine, including limitations on physical activities, social interactions, and emotional well-being.",
                'Emotional Impact': "Discuss any psychological effects that impact the client's ability to enjoy life and engage in previously enjoyed activities.",
                'Legal Justification': "Argue for the necessity of considering these lifestyle impacts as part of the compensation claim."
            },
            'summary': """
                Synthesize the information into a comprehensive narrative that illustrates how the client's injuries have altered their lifestyle. This summary should highlight the need for compensation based on these significant lifestyle impacts.
            """
        },

        'case_law': {
            'instruction': f"""
                As a legal expert, support personal injury claims related to traffic collisions in {st.session_state.get('selected_state', '')}. Focus on identifying relevant legal precedents, statutes, and case law that could strengthen a client's claim. Ensure all information is based solely on the provided consolidated information.

                **Important Guidelines:**
                - **Accuracy**: Use only the details present in the consolidated information.
                - **No Fabrication**: Do not invent or assume any facts not included in the consolidated data.
                - **Structured Narrative**: Organize the information clearly, separating case law references and statutory laws.
                - **Chain-of-Thought**: Utilize chain-of-thought reasoning to analyze and present the information logically without introducing new data.
            """,
            'sub_instructions': {
                "case_law_references": """
                    Based on the following context, identify and summarize key {st.session_state.get('selected_state', '')} case law that could support the claim. Emphasize precedents related to traffic collisions similar to the client's case. Highlight the legal principles upheld in these cases and how they can be applied to the client's situation.
                    {consolidated_info}
                """,
                "statutory_law": """
                    Based on the following context, outline relevant {st.session_state.get('selected_state', '')} statutes that govern traffic collisions and personal injury claims. Emphasize those statutes that directly impact the assessment of liability and damages in such cases.
                    {consolidated_info}
                """
            }
        },

        'legal_framework': {
            'instruction': f"""
                As a legal expert, develop a comprehensive legal framework to support personal injury claims related to traffic collisions in {st.session_state.get('selected_state', '')}. This framework should encompass relevant case law and statutory laws that can strengthen the client's claim. Ensure all information is derived exclusively from the provided consolidated information.

                **Important Guidelines:**
                - **Accuracy**: Utilize only the details present in the consolidated information.
                - **No Fabrication**: Do not introduce or assume any facts not included in the consolidated data.
                - **Integrated Approach**: Seamlessly integrate case law references and statutory laws to form a robust legal argument.
                - **Chain-of-Thought**: Utilize chain-of-thought reasoning to analyze and present the information logically without introducing new data.
            """,
            'sub_instructions': {
                "case_law_references": """
                    Based on the following context, identify and summarize key {st.session_state.get('selected_state', '')} case law that could support the claim. Emphasize precedents related to traffic collisions similar to the client's case. Highlight the legal principles upheld in these cases and how they can be applied to the client's situation.
                    {consolidated_info}
                """,
                "statutory_law": """
                    Based on the following context, outline relevant {st.session_state.get('selected_state', '')} statutes that govern traffic collisions and personal injury claims. Emphasize those statutes that directly impact the assessment of liability and damages in such cases.
                    {consolidated_info}
                """
            }
        },


        'general_damages': {
            'instruction': f"""
            As an AI legal assistant, your task is to draft a clear, well-structured narrative and estimation focused on the **General Damages** sustained by {st.session_state.get('client_name', 'the client')} as a result of the incident.

            Your response must:
            - Identify and describe the specific categories of **General Damages** relevant to the case, such as pain and suffering, emotional distress, loss of enjoyment of life, etc.
            - Ground each category in the factual context provided (e.g., injuries, treatments, daily limitations).
            - Propose an **estimated compensation amount** for each category, based on severity and long-term impact.
            - Conclude with a compelling justification for the total compensation amount requested for general damages, emphasizing the profound effect on the client’s quality of life.
            """,
                'sub_instructions': {
                    'narrative_and_estimation_construction': """
            Write a persuasive, human-centered narrative that illustrates each category of general damages and supports your proposed compensation amounts. The tone should be formal and empathetic, but legally grounded.

            Focus on:
            - Making the emotional and lifestyle impact tangible through realistic examples (based on provided data).
            - Avoiding generic phrases — every paragraph must be tied to the client's specific situation.
            - Highlighting why these damages deserve full recognition in the settlement.
            """,
                    'expanded_examples': """
            General Damages categories may include:
            - **Pain and Suffering**
            - **Emotional Distress**
            - **Loss of Enjoyment of Life**
            - **Loss of Consortium**
            - **Disfigurement**
            - **Future Psychological Care**
            - **Duties Under Duress**

            Use only the categories relevant to the client's case. If a category is not supported by the information provided, do not include it.
            """
                },
                'summary': f"""
            Develop a structured narrative identifying each type of General Damages experienced by {st.session_state.get('client_name', 'the client')}, including pain and suffering, emotional distress, loss of enjoyment of life, and others where applicable. For each category, propose a realistic compensation amount based on the client’s condition and impact on their life.

            Finish with a justification of why these non-economic damages deserve full consideration in the settlement.
            """,
                'examples': {
                    'general_damages_analysis': f"""
            ### Format Reference – Do Not Copy Content Directly

            This example shows how to structure a general damages narrative using bullet points and a concluding paragraph. Only use categories and amounts that apply to the client’s case.

            ---
                                                                
            **General Damages Analysis:**

            - **Pain and Suffering**: {st.session_state.get('client_name', 'the client')} has endured ongoing physical pain and discomfort since the incident.  
            **Estimated Compensation**: $50,000

            - **Emotional Distress**: The incident caused significant psychological impact, including anxiety and difficulty sleeping.  
            **Estimated Compensation**: $30,000

            - **Loss of Enjoyment of Life**: {st.session_state.get('client_name', 'the client')} is no longer able to enjoy [insert activity/hobby], which was an essential part of their lifestyle.  
            **Estimated Compensation**: $20,000

            **Total Estimated General Damages**: $100,000

            This compensation reflects the lasting effect of the injuries on the client’s life and emotional well-being. The amounts are based on similar cases and the documented severity of the client’s condition.
            """
            }
        },  

        'general_damages2': {
            'instruction': f"""
                As an AI legal expert, your task is to provide a comprehensive narrative and estimation focused on the 'General Damages' sustained by {st.session_state.get('client_name', 'the client')} as a result of the incident.

                Your narrative and estimation should:
                - Identify and describe the specific 'General Damages' experienced by {st.session_state.get('client_name', 'the client')}, incorporating details from the case, current and future medical expenses.
                - Estimate a compensation amount for each category of 'General Damages', based on severity, the impact on {st.session_state.get('client_name', 'the client')}'s life, and comparative cases or legal precedents.
                - Argue for the necessity of substantial compensation for these intangible losses, emphasizing their profound effect on {st.session_state.get('client_name', 'the client')}'s quality of life and well-being.
            """,
            'sub_instructions': {
                'narrative_and_estimation_construction': """
                    Construct a detailed narrative and provide estimations for 'General Damages' that:
                    - Specifies and illustrates each type of 'General Damages' the client has suffered, including pain and suffering, emotional distress, loss of enjoyment of life, duties under duress, among others. Use specific case details and medical expenses to ground your estimation.
                    - Suggests an estimated compensation amount for each 'General Damages' category, considering the overall impact and severity. Reference similar cases or legal guidelines as appropriate.
                    - Concludes with a compelling justification for the inclusion of these compensations in the settlement, underscoring the necessity of acknowledging the full extent of the client's non-economic losses for a comprehensive recovery.
                """,
                'expanded_examples': """
                    Examples of 'General Damages' include, but are not limited to:
                    - **Pain and Suffering**: Compensation for the physical discomfort and distress experienced since the incident.
                    - **Emotional Distress**: Compensation for anxiety, depression, PTSD, or other mental health issues resulting from the incident.
                    - **Loss of Enjoyment of Life**: Compensation for the inability to partake in hobbies, activities, or enjoyment that were part of the client's life before the incident.
                    - **Loss of Consortium**: Compensation for the impact on the client's relationships with their spouse, children, or family members.
                    - **Disfigurement**: Compensation for any physical changes that affect the client's appearance and self-esteem.
                    - **Future Psychological Care**: Estimation of costs for ongoing psychological support or therapy needed to address long-term emotional and mental health issues related to the incident.
                    - **Duties Under Duress**: Compensation for pain experienced while performing necessary activities, despite the physical discomfort, to support the client's family or maintain their household.
                    - **Loss of Income**: Compensation for the inability to work or earn income due to the injuries sustained in the incident.

                    "Considering {st.session_state.get('client_name', 'the client')}'s severe back injury and the resulting chronic pain, the loss of ability to engage in [specific activity], and the ongoing need for psychological support, a comprehensive compensation package for 'General Damages' is essential. The proposed amounts for pain and suffering, emotional distress, loss of enjoyment of life, and duties under duress, totaling [estimated total compensation], reflect the severity of the impact and are justified by {st.session_state.get('client_name', 'the client')}'s profound and enduring suffering."
                """
            },
            'summary': f"""
                Develop a comprehensive analysis that includes:
            
                1. **General Damages Narrative and Estimation**: Create a narrative that details the specific 'General Damages' experienced by {st.session_state.get('client_name', 'the client')}, such as pain and suffering, emotional distress, loss of enjoyment of life, and others. For each category, provide an estimated compensation amount based on the severity and impact on {st.session_state.get('client_name', 'the client')}'s life, referencing comparable cases or legal precedents where applicable.
            
                2. **Justification for Compensation**: Argue the necessity of substantial compensation for these intangible losses, highlighting their profound effect on {st.session_state.get('client_name', 'the client')}'s quality of life and well-being.
            """,
            'examples': {
                'general_damages_analysis': f"""
                    **General Damages Analysis:**
                    
                    - **Pain and Suffering**: {st.session_state.get('client_name', 'the client')} has endured continuous physical pain and discomfort since the incident, impacting daily activities and overall well-being. **Estimated Compensation**: $50,000.
                    
                    - **Emotional Distress**: As a result of the incident, {st.session_state.get('client_name', 'the client')} has experienced significant anxiety and depression, requiring ongoing psychological therapy. **Estimated Compensation**: $30,000.
                    
                    - **Loss of Enjoyment of Life**: {st.session_state.get('client_name', 'the client')} can no longer participate in [specific activities/hobbies], which were an integral part of their life before the incident. **Estimated Compensation**: $20,000.
                    
                    - **Duties Under Duress**: Despite severe injuries, {st.session_state.get('client_name', 'the client')} continues to perform necessary household duties, enduring significant pain and discomfort in the process. **Estimated Compensation**: $10,000.
                    
                    **Total Estimated Compensation for General Damages**: $110,000.
                    
                    "Considering {st.session_state.get('client_name', 'the client')}'s severe back injury and the resulting chronic pain, the loss of ability to engage in [specific activity], and the ongoing need for psychological support, a comprehensive compensation package for 'General Damages' is essential. The proposed amounts for pain and suffering, emotional distress, loss of enjoyment of life, and duties under duress, totaling $110,000, reflect the severity of the impact and are justified by {st.session_state.get('client_name', 'the client')}'s profound and enduring suffering."
                """
            }
        },

        'demand_letter': {
            'instruction': f"""

                As an AI legal assistant, your task is to draft a **comprehensive and professionally formatted demand letter** for a personal injury case under **'{st.session_state.get('liability_type', 'Bodily Injury Liability')}'** in **{st.session_state.get('selected_state', '')}**.
                ---

                ### STRUCTURE TO FOLLOW

                The demand letter must include the following clearly labeled sections:
                - Introduction  
                - Facts  
                - Liability  
                - Damages  
                - Medical Treatment  
                - Future Medical Expenses  
                - Impact on Lifestyle  
                - General Damages Estimation  
                - Policy Limit Demand  
                - Conclusion

                ---

                ### FORMATTING & STYLE INSTRUCTIONS

                - The letter must include the **client's name**, **claim number**, and relevant **incident details**.
                - It should be addressed to the appropriate **insurance adjuster** or **claims representative**.
                - Maintain a **formal tone** and use **legal terminology** where appropriate.
                - Use **headings** and **paragraphs** for clarity and professional appearance.
                - The letter must be **concise but comprehensive**, presenting facts and legal arguments clearly.
                - Include a **demand for compensation**, specifying the amount and the justification.
                - Conclude with a **clear call to action**, requesting a prompt response from the insurer.
                - Use **bulleted lists** or **simple tables** only when they genuinely improve clarity, such as when organizing complex medical treatments or itemized expenses. However, prioritize a natural and persuasive narrative flow throughout the letter.
                - Use **bold** or *italics* sparingly to highlight key facts or figures (e.g., total damages or policy limits), without disrupting the professional tone or narrative structure.

                ---

                ### LENGTH AND COMPLEXITY

                Adjust the level of detail and length based on the **demand amount**:

                - **Policy Limit (typically $30K–$50K)** → Standard demand letter (1–2 pages).
                - **$100K–$499K** → Enhanced letter with more legal analysis and detailed facts.
                - **$500K+** → Extended letter (multi-page), including legal precedent, breakdowns, and strong argumentation.

                ---

                ### IMPORTANT GUIDELINES

                - ✅ **Accuracy**: Only use the provided consolidated information.
                - ❌ **No Fabrication**: Do not invent or assume any missing facts.
                - 📚 **Strict Structure**: Follow the structure listed above without skipping sections.

                ---

                ### MISSING INFORMATION PROTOCOL

                You must not invent or assume any information that is not explicitly included in the consolidated data.  
                If a detail is missing, simply **exclude that section or sentence entirely** — do not use placeholders or generic filler content.
                
                For example:
                - ❌ Do not write about witnesses unless they are mentioned.
                - ❌ Do not guess policy limits — only include them if explicitly provided.
                - ❌ Do not use placeholders like "[Information not provided]" or "[Details not available]".
                - ❌ Do not include generic statements like "The client suffered injuries" without specific details.
               
                Keep the demand letter 100% grounded in the consolidated information. If something isn't there, it doesn't exist.
                If something isn't there, it doesn't exist.

                ---

                ### CONSOLIDATED INFORMATION

                This information will be provided below after the prompt. Use it as the only source of facts for generating the demand letter.
                """,

            'examples': {
                'demand_letter_example': f"""
            ### 📄 FORMAT REFERENCE ONLY — DO NOT COPY CONTENT

            The following is a visual reference to help you understand how the structure and formatting of a demand letter should look. Do not copy or reuse any names, dates, locations, or injuries. Use only the data provided in the consolidated information section.

            ---

            [Your Law Firm's Letterhead]  
            [Date]  
            [Insurance Adjuster's Name]  
            [Insurance Company]  
            [Address]  
            [City, State, ZIP Code]  

            Re: Personal Injury Claim for {st.session_state.get('client_name', 'the client')}  
            Claim Number: {st.session_state.get('claim_number', 'N/A')}  

            Dear [Insurance Adjuster's Name],  

            **Introduction**  
            ...  

            **Facts**  
            ...  

            **Liability**  
            ...  

            **Damages**  
            ...  

            **Medical Treatment**  
            ...  

            **Future Medical Expenses**  
            ...  

            **Impact on Lifestyle**  
            ...  

            **General Damages Estimation**  
            ...  

            **Policy Limit Demand**  
            ...  

            **Conclusion**  
            ...

            Sincerely,  
            [Your Name]  
            [Your Law Firm]  
            [Contact Information]
            """
            }

        },

}

    prompt_dict = prompts.get(task_name)
    if not prompt_dict:
        raise KeyError(f"Task name '{task_name}' not found in prompts dictionary.")

    instruction = prompt_dict.get("instruction", "").strip()
    sub_instructions = prompt_dict.get("sub_instructions", {})
    summary = prompt_dict.get("summary", "").strip()
    examples = prompt_dict.get("examples", {})

    sub_instructions_text = ""
    if sub_instructions:
        for sub_key, sub_val in sub_instructions.items():
            if sub_val.strip():
                sub_instructions_text += f"\n\n[Sub-Instruction: {sub_key}]\n{sub_val.strip()}"

    examples_text = ""
    for ex_key, ex_val in examples.items():
        examples_text += f"\n\n[Example - {ex_key}]\n{ex_val.strip()}"

    final_consolidated_section = f"\n\n[Consolidated Info]\n{consolidated_info}"

    full_prompt = (
        f"{instruction}\n"
        f"{sub_instructions_text}\n"
        f"\n{summary}\n"
        f"{examples_text}"
        f"{final_consolidated_section}"
    ).strip()

    return full_prompt




async def extract_facts_and_liability():
    prompt = get_prompts(
        task_name='facts_and_liability_narrative',
        consolidated_info=consolidate_info(),
    )
    with st.spinner("Extracting Facts & Liability. Please wait..."):
        final_summary = await process_text_in_segments(
            model_alias="gpt-4o",  # Changed from gpt4o
            text=st.session_state.prelit_text_buffer,
            prompt=prompt
        )
        st.markdown("### Extracted Facts & Liability:")
        st.write(final_summary)
        st.session_state['facts_and_liability_demand'] = final_summary


async def detailed_medical_analysis():
    prompt = get_prompts(
        task_name='detailed_medical_analysis',
        consolidated_info=consolidate_info(),
    )
    with st.spinner("Analyzing Medical Records. Please wait..."):
        final_summary = await process_text_in_segments(
            model_alias="gpt-4o",  # Changed from gpt4o
            text=st.session_state.prelit_text_buffer,
            prompt=prompt
        )
        st.markdown("### Detailed Medical Analysis:")
        st.write(final_summary)
        st.session_state['detailed_medical_analysis'] = final_summary

async def identify_medical_expenses():
    prompt = get_prompts(
        task_name='medical_expenses',
        consolidated_info=consolidate_info(),
    )
    with st.spinner("Identifying Medical Expenses. Please wait..."):
        final_summary = await process_text_in_segments(
            model_alias="gpt-4o",  # Changed from gpt4o
            text=st.session_state.prelit_text_buffer,
            prompt=prompt
        )
        st.markdown("### Medical Expenses:")
        st.write(final_summary)
        st.session_state['current_medical_expenses'] = final_summary
       
async def comprehensive_medical_analysis():
    prompt = get_prompts(
        task_name='comprehensive_medical_analysis',
        consolidated_info=consolidate_info(),
    )
    with st.spinner("Extracting Data. Please wait..."):
        final_summary = await process_text_in_segments(
            model_alias="gpt-4o",  # Changed from gpt4o
            text=st.session_state.prelit_text_buffer,
            prompt=prompt
        )
        st.markdown("### Comprehensive Medical Analysis:")
        st.write(final_summary)
        st.session_state['comprehensive_medical_analysis'] = final_summary

async def identify_future_treatments():
    prompt = get_prompts(
        task_name='future_medical_expenses',
        consolidated_info=consolidate_info(),
    )
    with st.spinner("Identifying Future Medical Expenses. Please wait..."):
        final_summary = await process_text_in_segments(
            model_alias="gpt-4o",  # Changed from gpt4o
            text=st.session_state.prelit_text_buffer,
            prompt=prompt
        )
        st.markdown("### Future Medical Expenses:")
        st.write(final_summary)
        st.session_state['future_medical_expenses'] = final_summary

async def impact_on_lifestyle():
    prompt = get_prompts(
        task_name='impact_on_lifestyle',
        consolidated_info=consolidate_info(),
    )
    with st.spinner("Assessing Impact on Lifestyle. Please wait..."):
        final_summary = await process_text_in_segments(
            model_alias="gpt-4o",  # Changed from gpt4o
            text=st.session_state.prelit_text_buffer,
            prompt=prompt
        )
        st.markdown("### Impact on Lifestyle:")
        st.write(final_summary)
        st.session_state['impact_on_lifestyle'] = final_summary

async def identify_general_damages():
    prompt = get_prompts(
        task_name='general_damages',
        consolidated_info=consolidate_info(),
    )
    with st.spinner("Identifying General Damages. Please wait..."):
        final_summary = await process_text_in_segments(
            model_alias="gpt-4o",  # Changed from gpt4o
            text=st.session_state.prelit_text_buffer,
            prompt=prompt
        )
        st.markdown("### General Damages:")
        st.write(final_summary)
        st.session_state['general_damages'] = final_summary

async def search_legal_framework():
    prompt = get_prompts(
        task_name='legal_framework',
        consolidated_info=consolidate_info(),
    )
    with st.spinner("Searching Legal Framework. Please wait..."):
        final_summary = await process_text_in_segments(
            model_alias="gpt-4o",  # Changed from gpt4o
            text=st.session_state.prelit_text_buffer,
            prompt=prompt
        )
        st.markdown("### Legal Framework:")
        st.write(final_summary)
        st.session_state['legal_framework'] = final_summary

async def search_case_law():    
    prompt = get_prompts(
        task_name='case_law',
        consolidated_info=consolidate_info(),
    )
    with st.spinner("Searching Case Law. Please wait..."):
        final_summary = await process_text_in_segments(
            model_alias="gpt-4o",  # Changed from gpt4o
            text=st.session_state.prelit_text_buffer,
            prompt=prompt
        )
        st.markdown("### Case Law:")
        st.write(final_summary)
        st.session_state['case_law'] = final_summary



def consolidate_info():
    info_parts = [
        f"State: {st.session_state.get('selected_state', '')}",
        f"Liability Type: {st.session_state.get('liability_type', '')}",
        f"Loss of Income: {st.session_state.get('loss_of_income', '')}",
        f"Demand Amount: {st.session_state.get('demand_amount', 'Policy Limit')}",
        f"Due Date: {st.session_state.get('due_date', 'No Date Set')}",
        f"UM Amount Requested: ${st.session_state.get('um_amount', '')}" if st.session_state.get('liability_type', '') in ['Uninsured Motorist', 'Under-Insured Motorist'] else "",
        f"Liability Accepted: {'Yes' if st.session_state.get('liability_accepted', False) else 'No'}",
        f"Liability Percentage: {st.session_state.get('liability_percentage', 0)}%",
        "Additional Notes:",
        st.session_state.get('additional_notes', ''),
        "Facts of Loss & Liability:",
        st.session_state.get('facts_and_liability_narrative', ''),
        "Medical Information:",
        st.session_state.get('comprehensive_medical_analysis', ''),
        "Medical Expenses",
        st.session_state.get('current_medical_expenses', ''),
        "Future Treatments:",
        st.session_state.get('future_medical_expenses', ''),
        "General Damages:",
        st.session_state.get('general_damages', ''),
        "Lifestyle Impact:",
        st.session_state.get('impact_on_lifestyle', ''),
        "Legal Framework:",
        st.session_state.get('legal_framework', '')
    ]

    consolidated_info = "\n".join(part for part in info_parts if isinstance(part, str) and part.strip())
    return consolidated_info


async def create_demand_letter():
    consolidated_info = consolidate_info()

    prompt = get_prompts(
        task_name='demand_letter',
        consolidated_info=consolidated_info,
    )
    with st.spinner("Drafting Demand Letter. Please wait..."):
        demand_letter = await process_full_prompt(
            model_alias="gpt-4o",  # Changed from gpt4o
            prompt=prompt
        )

        # Display the demand letter in the Streamlit app
        st.markdown("### Draft Demand Letter:")
        st.write(demand_letter)
        st.session_state['demand_letter'] = demand_letter

        st.download_button(
            label="Download Demand Letter",
            data=demand_letter,
            file_name="demand_letter.txt",
            mime="text/plain"
        )
