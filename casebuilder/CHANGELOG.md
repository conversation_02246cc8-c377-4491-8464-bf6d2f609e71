# Changelog de CaseBuilder.AI

Este archivo documenta los cambios significativos realizados durante la refactorización a arquitectura hexagonal.

## [Última Versión] - 2024-03-27

### Correcciones

- **ServiceLocator**: Reimplementado completamente para resolver problemas de inconsistencia en la API. La nueva implementación soporta:
  - API basada en strings: `register_service("IAIService", instance)` y `get_service("IAIService")`
  - API basada en tipos: `register(IAIService, implementation)` y `get(IAIService)`
  - Funciones globales de conveniencia para ambos estilos
  - Soporte para registro de instancias o fábricas de servicios

- **Gestión de Configuración**: Estandarizada la gestión de claves API y configuraciones sensibles:
  - Uso exclusivo de variables de entorno mediante dotenv (`.env`)
  - Eliminadas referencias a Streamlit secrets
  - Documentación actualizada con instrucciones para configurar el archivo `.env`

### Agregado

- **Patrón Query**: Se implementó el patrón Query para operaciones de lectura, siguiendo los principios CQRS.
  - `Query`: Clase base para las consultas
  - `QueryHandler`: Interfaz base para manejadores de consultas
  - `QueryBus`: Mediador para enrutar consultas a sus manejadores

- **Validación y Manejo de Errores**: 
  - Jerarquía de excepciones para manejo específico de errores
  - Utilidades de validación para comandos y parámetros
  - Decoradores para validación, registro y manejo de errores

- **Mediator Pattern**: 
  - Implementación unificada que combina CommandBus y QueryBus
  - Manejo centralizado de errores con registros personalizables
  - Soporte para múltiples tipos de errores y respuestas configurables

- **Modelos de Dominio**:
  - `Case`: Modelo completo para casos legales con métodos auxiliares
  - `Document`: Modelo para documentos con soporte para tipos y metadatos
  - Interfaces de repositorio para acceso a datos

- **Inicialización de Aplicación**:
  - `AppInitializer`: Configuración centralizada de todos los componentes
  - Registro de servicios, manejadores y mediador
  - Configuración de manejo de errores y logging

- **Integración con app.py**:
  - Adaptador de Streamlit mejorado para mejor manejo de errores
  - Módulo de integración para reemplazar las funciones existentes
  - Soporte para monkey patching para migración gradual

- **Pruebas**:
  - Pruebas unitarias para Command, Query y Mediator
  - Script de integración para pruebas automatizadas y manuales
  - Documentación detallada para pruebas

## [Fase 1-2] - 2024-03-20

### Agregado

- Estructura básica para arquitectura hexagonal
- Implementación de interfaces principales:
  - `IAIService`: Interfaz para servicios de IA
  - `IOCRService`: Interfaz para servicios OCR
  - `ITextProcessorService`: Interfaz para procesamiento de texto
  - `ITokenService`: Interfaz para gestión de tokens

- Adaptadores principales:
  - `OpenAIAdapter`: Adaptador para la API de OpenAI
  - `TextProcessorService`: Servicio para procesamiento de texto

- Command Pattern:
  - `Command`: Clase base para comandos
  - `CommandHandler`: Interfaz base para manejadores
  - `CommandBus`: Mediador para comando-manejador

- Pruebas básicas para verificar la funcionalidad