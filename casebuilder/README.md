# CaseBuilder.AI - Application Layer

This directory contains the implementation of the application layer for CaseBuilder.AI using the hexagonal architecture pattern. The application layer follows the Command pattern to encapsulate use cases and decouple business logic from the presentation layer.

## Architecture Overview

The application layer is designed using the following patterns:

1. **Command Pattern**: Each operation is encapsulated in a command object with its corresponding handler
2. **Dependency Injection**: Services are injected into handlers through a service locator
3. **Adapter Pattern**: UI adapters are provided to bridge the existing Streamlit UI with the new architecture

## Directory Structure

```
casebuilder/
├── application/
│   ├── commands/       # Command objects representing use cases
│   ├── handlers/       # Command handlers implementing use case logic
│   ├── command_bus.py  # Mediator for dispatching commands to handlers
├── interfaces/
│   ├── web/            # Web UI adapters
├── infrastructure/
│   ├── application_initializer.py  # Application initialization
│   ├── dependency_injection.py     # Service locator implementation
```

## Implementation

The application layer implements the following key components:

### Command Bus

A mediator that dispatches commands to their registered handlers. It provides a central point for command handling and enables loose coupling between command senders and handlers.

### Commands

Commands represent use cases and encapsulate the data needed to perform an operation. Each command is a data class with a generic type parameter specifying the expected result type.

Examples:
- `DetailedMedicalAnalysisCommand`: Generate a detailed medical analysis
- `FactsAndLiabilityDemandCommand`: Generate facts and liability analysis for a demand letter
- `GenerateDemandLetterCommand`: Generate a complete demand letter

### Command Handlers

Handlers implement the business logic for each command. They depend on core services through interfaces and return the result specified by the command.

Examples:
- `DetailedMedicalAnalysisHandler`: Processes medical records to generate analysis
- `FactsAndLiabilityDemandHandler`: Analyzes documents to extract facts and liability
- `GenerateDemandLetterHandler`: Combines all case information to generate a demand letter

### Streamlit Adapter

The `StreamlitAdapter` provides a compatibility layer between the new architecture and the existing Streamlit UI. It mirrors the functions in `case_builder.py` but uses the command bus internally.

## Integration Example

See `example_integration.py` for an example of how to integrate the new architecture with the existing Streamlit application. It demonstrates:

1. Setting up the service locator with required services
2. Initializing the application with the application initializer
3. Using the Streamlit adapter to perform operations in the UI

## Next Steps

The application layer is designed to be gradually integrated with the existing codebase. Future enhancements include:

1. Creating more specialized commands and handlers for additional use cases
2. Implementing query objects and handlers for read operations
3. Adding validation and error handling for commands
4. Creating unit tests for command handlers
5. Implementing event publishing for cross-cutting concerns