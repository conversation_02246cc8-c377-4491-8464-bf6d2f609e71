import json
import base64
import io
import time
import asyncio
import aiohttp
import hashlib
import logging
from typing import Optional, Dict, Any, List, BinaryIO, Union
from PIL import Image

from casebuilder.core.interfaces.IAIService import IAIService
from casebuilder.core.interfaces.ICacheService import ICacheService


class OpenAIAdapter(IAIService):
    """
    Adapter for the OpenAI API that implements the IAIService interface.
    Provides methods for interacting with various OpenAI models.

    This adapter supports:
    - Text processing with different models (GPT-4o, o1-mini, o3-mini)
    - Image processing with vision capabilities
    - Combined text and image inputs
    - Different formatting requirements for different models

    It maintains compatibility with the original gpt_api.py and gpt4o_vision.py
    modules while providing a cleaner, more unified interface.
    """

    # Configuration for different model types
    MODEL_CONFIG = {
        # GPT-4o models use system prompt + user separate content
        "gpt4o": {
            "model_name": "gpt-4o",
            "use_system_prompt": True,
            "use_max_completion_tokens": False,
            "reasoning_effort": None,
        },
        "gpt4omini": {
            "model_name": "gpt-4o-mini",
            "use_system_prompt": True,
            "use_max_completion_tokens": False,
            "reasoning_effort": None,
        },
        "gpt-4o": {  # Allow direct model name
            "model_name": "gpt-4o",
            "use_system_prompt": True,
            "use_max_completion_tokens": False,
            "reasoning_effort": None,
        },
        "gpt-4o-mini": {  # Allow direct model name
            "model_name": "gpt-4o-mini",
            "use_system_prompt": True,
            "use_max_completion_tokens": False,
            "reasoning_effort": None,
        },
        "gpt-4o-2024-05-13": {  # Vision model
            "model_name": "gpt-4o-2024-05-13",
            "use_system_prompt": True,
            "use_max_completion_tokens": False,
            "reasoning_effort": None,
        },

        # o4-mini model (new addition)
        "o4-mini": {
            "model_name": "o4-mini",
            "use_system_prompt": True,
            "use_max_completion_tokens": True,  # Uses max_completion_tokens
            "reasoning_effort": None,
        },

    }

    def __init__(self, api_key: str = None, cache_service: Optional[ICacheService] = None):
        """
        Initialize the OpenAI adapter.

        Args:
            api_key: OpenAI API key (if None, will attempt to get from environment variable)
            cache_service: Optional cache service for caching API responses

        Raises:
            ValueError: If no API key is provided or found in environment
        """
        import os
        from dotenv import load_dotenv

        # Load environment variables
        load_dotenv()

        # Get API key from parameter or environment
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')

        if not self.api_key:
            raise ValueError("OpenAI API key is required. Provide it as an argument or set the OPENAI_API_KEY environment variable.")

        # Set up cache service
        self.cache_service = cache_service
        self.logger = logging.getLogger(__name__)

        # Cache configuration
        self.use_cache = cache_service is not None
        self.cache_ttl = 3600  # 1 hour default TTL for cached responses

    async def process_text(
        self,
        model_alias: str,
        system_prompt: str,
        user_content: str,
        max_tokens: Optional[int] = 20000
    ) -> str:
        """Process text using an OpenAI model."""
        config = self.MODEL_CONFIG.get(model_alias)
        if not config:
            raise ValueError(f"Unknown model alias '{model_alias}'.")

        model_name = config["model_name"]
        messages = []

        if config["use_system_prompt"]:
            messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": user_content})
        else:
            combined_content = f"{system_prompt}\n\n{user_content}"
            messages.append({"role": "user", "content": combined_content})

        # Build request data
        data = {
            "model": model_name,
            "messages": messages,
        }

        # Add appropriate token parameter
        # Claude uses max_completion_tokens, GPT-4o uses max_tokens
        if config["use_max_completion_tokens"]:
            data["max_completion_tokens"] = int(max_tokens)
        else:
            data["max_tokens"] = int(max_tokens)

        # Add reasoning effort if specified (for Claude models)
        if config["reasoning_effort"]:
            data["reasoning_effort"] = config["reasoning_effort"]

        return await self._send_api_request(data)

    async def process_image(
        self,
        image_data: Union[bytes, BinaryIO, Image.Image],
        instruction: str,
        model_alias: Optional[str] = None,
        max_tokens: Optional[int] = 4096
    ) -> str:
        """
        Process an image using the OpenAI Vision model.

        Implements the original process_image_with_gpt4o functionality from gpt4o_vision.py.

        Args:
            image_data: Image data (binary data, file object, or PIL Image)
            instruction: Text instruction describing what to analyze in the image
            model_alias: Optional model identifier (defaults to "gpt-4o-2024-05-13")
            max_tokens: Maximum tokens for completion

        Returns:
            Generated text response based on image analysis

        Raises:
            RuntimeError: If the API request fails
            ValueError: If the image data format is invalid
        """
        # Use default model if not specified
        model = model_alias or "gpt-4o-2024-05-13"

        # Convert image to base64
        base64_image = self._encode_image(image_data)

        # Build request payload
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": instruction},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": max_tokens
        }

        return await self._send_api_request(payload)

    async def generate_content_with_reasoning(
        self,
        extracted_text: str,
        prompt: str,
        model: str = "o3-mini",
        reasoning_effort: str = "high",
        max_tokens: int = 4096
    ) -> str:
        """
        Generate content with enhanced reasoning capabilities.

        Implements the original generate_content_with_reasoning functionality from gpt4o_vision.py.

        Args:
            extracted_text: The text to process
            prompt: The system prompt or instruction
            model: Model name to use
            reasoning_effort: Level of reasoning effort (for Claude models)
            max_tokens: Maximum tokens for completion

        Returns:
            Generated text response

        Raises:
            RuntimeError: If the API request fails
        """
        # Build request payload
        payload = {
            "model": model,
            "reasoning_effort": reasoning_effort,
            "messages": [
                {"role": "system", "content": prompt},
                {"role": "user", "content": extracted_text}
            ],
            "max_tokens": max_tokens
        }

        return await self._send_api_request(payload)

    async def generate_content_with_gpt4o(
        self,
        extracted_text: str,
        prompt: str,
        max_tokens: int = 4096
    ) -> str:
        """Generate content using GPT-4o with system and user prompts."""
        payload = {
            "model": "gpt-4o-mini",
            "messages": [
                {"role": "system", "content": prompt},
                {"role": "user", "content": extracted_text}
            ],
            "max_tokens": max_tokens
        }

        return await self._send_api_request(payload)

    async def _send_api_request(self, data: Dict[str, Any]) -> str:
        """
        Send a request to the OpenAI API.

        Args:
            data: The request data

        Returns:
            Response content from the API

        Raises:
            RuntimeError: If the API request fails
        """
        # Check cache if enabled
        if self.use_cache:
            # Generate a cache key from the request data
            cache_key = self._generate_cache_key(data)

            # Try to get from cache
            cached_result = await self.cache_service.get(cache_key, "model")
            if cached_result is not None:
                self.logger.info(f"Cache hit for model request: {data.get('model', 'unknown')}")
                return cached_result

        # Set headers
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        # Send request with retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        "https://api.openai.com/v1/chat/completions",
                        headers=headers,
                        json=data
                    ) as response:
                        if response.status == 200:
                            response_json = await response.json()
                            choices = response_json.get("choices", [])
                            if choices:
                                result = choices[0]["message"]["content"]

                                # Cache the result if caching is enabled
                                if self.use_cache:
                                    cache_key = self._generate_cache_key(data)
                                    await self.cache_service.set(cache_key, result, "model", self.cache_ttl)
                                    self.logger.info(f"Cached model response for: {data.get('model', 'unknown')}")

                                return result
                            else:
                                return "No response generated"
                        elif response.status == 429:  # Rate limit hit
                            # Wait with exponential backoff
                            wait_time = (2 ** attempt) + 1
                            self.logger.warning(f"Rate limit hit, retrying in {wait_time} seconds...")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            error_text = await response.text()
                            raise RuntimeError(f"OpenAI API error (status {response.status}): {error_text}")
            except aiohttp.ClientError as e:
                # Handle network errors
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 1
                    self.logger.warning(f"Network error: {e}. Retrying in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                else:
                    raise RuntimeError(f"Network error after {max_retries} attempts: {e}")

        # If we've exhausted all retries
        raise RuntimeError("Failed to get a response after multiple attempts")

    def _generate_cache_key(self, data: Dict[str, Any]) -> str:
        """
        Generate a cache key from request data.

        Args:
            data: The request data

        Returns:
            A cache key string
        """
        # Create a stable representation of the data
        model = data.get("model", "unknown")

        # Extract messages content
        messages = data.get("messages", [])
        message_content = []
        for msg in messages:
            if isinstance(msg.get("content"), str):
                message_content.append(msg.get("content", ""))
            elif isinstance(msg.get("content"), list):
                # Handle content that contains text and images
                for item in msg.get("content", []):
                    if item.get("type") == "text":
                        message_content.append(item.get("text", ""))
                    # Skip image URLs as they're large and may change even for same image

        # Combine model and message content
        key_data = f"{model}:{':'.join(message_content)}"

        # Hash the data
        hash_obj = hashlib.sha256(key_data.encode('utf-8'))
        return hash_obj.hexdigest()

    def _encode_image(self, image_data: Union[bytes, BinaryIO, Image.Image]) -> str:
        """
        Encode image data to base64.

        Args:
            image_data: Image data in various formats

        Returns:
            Base64 encoded image string

        Raises:
            ValueError: If the image data format is invalid
        """
        try:
            # Handle different input types
            if isinstance(image_data, bytes):
                # Binary data
                img = Image.open(io.BytesIO(image_data))
                buffered = io.BytesIO()
                img.save(buffered, format="PNG")
                return base64.b64encode(buffered.getvalue()).decode('utf-8')

            elif hasattr(image_data, 'read') and callable(image_data.read):
                # File-like object
                image_data.seek(0)
                img_bytes = image_data.read()
                if isinstance(img_bytes, str):
                    # Handle text files by encoding to bytes
                    img_bytes = img_bytes.encode('utf-8')
                return base64.b64encode(img_bytes).decode('utf-8')

            elif isinstance(image_data, Image.Image):
                # PIL Image
                buffered = io.BytesIO()
                image_data.save(buffered, format="PNG")
                return base64.b64encode(buffered.getvalue()).decode('utf-8')

            else:
                raise ValueError(f"Unsupported image data type: {type(image_data)}")

        except Exception as e:
            raise ValueError(f"Failed to encode image: {e}")

    # Legacy compatibility methods

    async def chat_with_model(
        self,
        model_alias: str,
        system_prompt: str,
        user_content: str,
        max_tokens: int = 20000
    ) -> str:
        """
        Legacy compatibility method for gpt_api.py.

        Args:
            model_alias: Model identifier
            system_prompt: System prompt
            user_content: User content
            max_tokens: Maximum tokens

        Returns:
            Generated text response
        """
        return await self.process_text(model_alias, system_prompt, user_content, max_tokens)

    async def process_image_with_gpt4o(
        self,
        image,
        instruction="Describe the contents of this image"
    ) -> str:
        """
        Legacy compatibility method for gpt4o_vision.py.

        Args:
            image: Image data
            instruction: Text instruction

        Returns:
            Generated text description
        """
        return await self.process_image(image, instruction, "gpt-4o-2024-05-13", 4096)
