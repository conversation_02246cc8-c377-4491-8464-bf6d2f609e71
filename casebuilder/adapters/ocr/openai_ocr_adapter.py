import io
import os
import base64
import logging
import asyncio
import aiohttp
import hashlib
from typing import List, Optional, BinaryIO, Dict, Any, Union, Tuple
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
from PIL import Image
from pdf2image import convert_from_bytes

from casebuilder.core.interfaces.IOCRService import IOCRService
from casebuilder.core.interfaces.ICacheService import ICacheService


class OpenAIOCRAdapter(IOCRService):
    """
    OCR service adapter using OpenAI's vision capabilities.
    Implements the IOCRService interface.

    This adapter uses the GPT-4o Vision model to extract text from images and PDFs,
    with optimizations for handling large documents and parallel processing.
    """

    def __init__(self, api_key: Optional[str] = None, model_name: str = "gpt-4o-mini", cache_service: Optional[ICacheService] = None):
        """
        Initialize the OpenAI OCR adapter.

        Args:
            api_key: OpenAI API key (defaults to environment variable)
            model_name: Model name to use for OCR (defaults to gpt-4o-mini)
            cache_service: Optional cache service for caching OCR results

        Raises:
            ValueError: If no API key is provided or found in environment
        """
        # Get API key from parameter or environment
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')

        if not self.api_key:
            raise ValueError("OpenAI API key is required. Provide it as an argument or set the OPENAI_API_KEY environment variable.")

        self.model_name = model_name
        self.logger = logging.getLogger(__name__)

        # Set up cache service
        self.cache_service = cache_service
        self.use_cache = cache_service is not None
        self.cache_ttl = 86400  # 24 hours default TTL for OCR results

    def _encode_image_to_base64(self, image: Image.Image, max_width=1200, max_height=1200) -> str:
        """
        Encode a PIL Image to base64 with optimizations for OCR.

        Args:
            image: PIL Image object
            max_width: Maximum width for resizing (reduced from 1600 to 1200)
            max_height: Maximum height for resizing (reduced from 1600 to 1200)

        Returns:
            Base64 encoded image string
        """
        # Make a copy to avoid modifying the original
        img_copy = image.copy()

        # Convert to grayscale for better OCR and smaller size
        if img_copy.mode != 'L':  # 'L' is grayscale mode
            img_copy = img_copy.convert('L')

        # Enhance contrast for better OCR
        from PIL import ImageEnhance
        enhancer = ImageEnhance.Contrast(img_copy)
        img_copy = enhancer.enhance(1.5)  # Increase contrast by 50%

        # Apply additional sharpening for better OCR
        enhancer = ImageEnhance.Sharpness(img_copy)
        img_copy = enhancer.enhance(1.3)  # Increase sharpness by 30%

        # Resize image if necessary (smaller max size for faster processing)
        img_copy.thumbnail((max_width, max_height), Image.LANCZOS)

        # Convert to base64 with JPEG format (smaller than PNG for documents)
        buffered = io.BytesIO()
        img_copy.save(buffered, format="JPEG", quality=75)  # Reduced quality from 85% to 75% for faster processing
        return base64.b64encode(buffered.getvalue()).decode('utf-8')

    def _estimate_tokens(self, image: Image.Image) -> int:
        """
        Estimate token usage for an image based on its dimensions.

        Args:
            image: PIL Image object

        Returns:
            Estimated token count
        """
        width, height = image.size
        tile_size = 512
        num_tiles = (max(width // tile_size, 1)) * (max(height // tile_size, 1))
        base_tokens = 85
        tokens_per_tile = 170
        total_tokens = base_tokens + tokens_per_tile * num_tiles
        return min(total_tokens, 128000)

    async def _send_api_request(
        self,
        data: Dict[str, Any],
        session: aiohttp.ClientSession
    ) -> str:
        """
        Send a request to the OpenAI API with retry logic.

        Args:
            data: The request data payload
            session: The aiohttp ClientSession

        Returns:
            The API response content

        Raises:
            RuntimeError: If the API request fails after retries
        """
        # Set headers
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        # Implement retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                async with session.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status == 200:
                        response_json = await response.json()
                        choices = response_json.get('choices', [])
                        if choices:
                            return choices[0]['message']['content']
                        else:
                            return "No response generated"
                    elif response.status == 429:  # Rate limit or quota
                        wait_time = (2 ** attempt) + 1
                        self.logger.warning(f"Rate limit hit, retrying in {wait_time} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        error_message = await response.text()
                        raise RuntimeError(f"OpenAI API error (status {response.status}): {error_message}")
            except aiohttp.ClientError as e:
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 1
                    self.logger.warning(f"Network error: {e}. Retrying in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                else:
                    raise RuntimeError(f"Network error after {max_retries} attempts: {e}")

        # If we've exhausted all retries
        raise RuntimeError("Failed to get a response after multiple attempts")

    async def _extract_text_from_image(
        self,
        image_base64: str,
        session: aiohttp.ClientSession,
        max_tokens: int,
        prompt_text: Optional[str] = None
    ) -> str:
        """
        Extract text from a base64 encoded image.

        Args:
            image_base64: Base64 encoded image string
            session: aiohttp ClientSession
            max_tokens: Maximum tokens for completion
            prompt_text: Optional prompt text

        Returns:
            Extracted text from the image

        Raises:
            RuntimeError: If the API request fails
        """
        # Use default prompt if none provided
        if prompt_text is None:
            prompt_text = (
                "You are an AI assistant tasked with extracting textual information from legal documents.\n\n"
                "- Focus on extracting main content such as incident details, medical findings, treatments, billing items,\n"
                "  and other critical information for legal analysis.\n"
                "- Omit irrelevant sections like headers, footers, page numbers, and repeated content.\n\n"
                "Provide the extracted text in its raw form without adding any summaries or commentary."
            )

        # Build request payload
        payload = {
            "model": self.model_name,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt_text},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
                ]
            }],
            "max_tokens": max_tokens,
            "temperature": 0
        }

        # Set headers
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        try:
            self.logger.info(f"Sending request to OpenAI API with model {self.model_name}")
            async with session.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=60  # Add explicit timeout
            ) as response:
                if response.status == 200:
                    response_json = await response.json()
                    choices = response_json.get('choices', [])
                    if choices and choices[0].get('message', {}).get('content'):
                        content = choices[0]['message']['content']
                        content_length = len(content)
                        self.logger.info(f"Successfully extracted {content_length} characters from image")
                        return content
                    else:
                        self.logger.error(f"No valid content in API response: {response_json}")
                        return "Error: No valid content in response"
                elif response.status == 429:  # Rate limit or quota
                    error_message = await response.text()
                    self.logger.warning(f"Rate limit hit: {error_message}")
                    return f"Error: API rate limit exceeded. Please try again later."
                else:
                    error_message = await response.text()
                    self.logger.error(f"API Error (status {response.status}): {error_message}")
                    return f"Error: API returned status {response.status}"
        except asyncio.TimeoutError:
            self.logger.error("API request timed out")
            return "Error: API request timed out"
        except aiohttp.ClientError as e:
            self.logger.error(f"Network error: {str(e)}")
            return f"Error: Network issue - {str(e)}"
        except Exception as e:
            self.logger.error(f"Unexpected error in _extract_text_from_image: {str(e)}")
            return f"Error: {str(e)}"

    async def process_pdf(self, pdf_file: BinaryIO) -> str:
        """
        Extract text from a PDF file.

        Implements the IOCRService interface method.
        Converts PDF to images and extracts text from each page,
        combining the results.

        Args:
            pdf_file: PDF file object

        Returns:
            Extracted text from the PDF

        Raises:
            RuntimeError: If processing fails
        """
        # Get PDF content
        pdf_file.seek(0)
        pdf_content = pdf_file.read()

        # Check cache if enabled
        if self.use_cache:
            # Generate a cache key from the PDF content
            cache_key = self._generate_cache_key(pdf_content)

            # Try to get from cache
            cached_result = await self.cache_service.get(cache_key, "ocr")
            if cached_result is not None:
                self.logger.info(f"Cache hit for PDF OCR")
                return cached_result

        # Convert PDF to images with optimized resolution for OCR
        try:
            # Use simplified settings for PDF conversion to ensure compatibility
            images = convert_from_bytes(
                pdf_content,
                dpi=100,  # Lower DPI for faster processing
                thread_count=4,  # Use multiple threads for conversion
                use_cropbox=True,  # Use cropbox instead of mediabox for slightly smaller images
                grayscale=True  # Convert to grayscale to reduce size and improve OCR
            )
            self.logger.info(f"Converted PDF to {len(images)} images with optimized settings")
        except Exception as e:
            self.logger.error(f"Failed to convert PDF to images: {str(e)}")
            raise RuntimeError(f"Failed to convert PDF to images: {str(e)}")

        # Process all pages in parallel with controlled concurrency
        total_pages = len(images)
        self.logger.info(f"Processing PDF with {total_pages} pages in parallel")

        # Process each page with controlled concurrency
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
            # Dynamic concurrency based on document size
            # For smaller documents (<20 pages), use higher concurrency
            # For larger documents, use more conservative concurrency to avoid rate limits
            if total_pages < 20:
                max_concurrency = 16  # Higher concurrency for smaller documents
            elif total_pages < 50:
                max_concurrency = 12  # Medium concurrency for medium-sized documents
            else:
                max_concurrency = 10  # Lower concurrency for very large documents

            self.logger.info(f"Using concurrency of {max_concurrency} for {total_pages} pages")
            semaphore = asyncio.Semaphore(max_concurrency)

            # Create a progress tracking mechanism
            processed_pages = 0

            async def process_page(image: Image.Image, page_num: int) -> str:
                """Process a single page with semaphore control"""
                nonlocal processed_pages
                async with semaphore:
                    try:
                        self.logger.info(f"Processing page {page_num+1}/{total_pages}")
                        base64_img = self._encode_image_to_base64(image)
                        max_tokens = self._estimate_tokens(image)
                        result = await self._extract_text_from_image(base64_img, session, max_tokens)

                        # Update progress
                        processed_pages += 1
                        progress_pct = (processed_pages / total_pages) * 100
                        self.logger.info(f"Completed page {page_num+1}/{total_pages} ({progress_pct:.1f}%)")

                        return result
                    except Exception as e:
                        self.logger.error(f"Error processing page {page_num+1}: {e}")
                        # Retry once on failure
                        try:
                            self.logger.info(f"Retrying page {page_num+1}/{total_pages}")
                            # Wait a bit before retrying
                            await asyncio.sleep(2)
                            base64_img = self._encode_image_to_base64(image)
                            max_tokens = self._estimate_tokens(image)
                            result = await self._extract_text_from_image(base64_img, session, max_tokens)

                            # Update progress
                            processed_pages += 1
                            progress_pct = (processed_pages / total_pages) * 100
                            self.logger.info(f"Retry successful for page {page_num+1}/{total_pages} ({progress_pct:.1f}%)")

                            return result
                        except Exception as retry_e:
                            self.logger.error(f"Retry failed for page {page_num+1}: {retry_e}")
                            processed_pages += 1
                            return f"[Error processing page {page_num+1}]"

            # Process pages in batches for better memory management
            batch_size = 20  # Process 20 pages at a time
            all_text_segments = []

            # Create batches of pages
            page_batches = [images[i:i + batch_size] for i in range(0, len(images), batch_size)]

            for batch_idx, batch in enumerate(page_batches):
                self.logger.info(f"Processing batch {batch_idx+1}/{len(page_batches)} ({len(batch)} pages)")

                # Create tasks for current batch
                batch_tasks = [
                    process_page(image, i + (batch_idx * batch_size))
                    for i, image in enumerate(batch)
                ]

                # Process current batch in parallel
                batch_results = await asyncio.gather(*batch_tasks)
                all_text_segments.extend(batch_results)

                self.logger.info(f"Completed batch {batch_idx+1}/{len(page_batches)}")

                # Free up memory
                batch_tasks.clear()

            # Combine all segments
            final_text = ' '.join(all_text_segments)
            self.logger.info(f"PDF processing complete, extracted {len(final_text)} characters")

            # Cache the result if caching is enabled
            if self.use_cache:
                cache_key = self._generate_cache_key(pdf_content)
                await self.cache_service.set(cache_key, final_text, "ocr", self.cache_ttl)
                self.logger.info(f"Cached PDF OCR result")

        return final_text

    def _generate_cache_key(self, data: bytes, prefix: str = "") -> str:
        """
        Generate a cache key from binary data.

        Args:
            data: Binary data to hash
            prefix: Optional prefix for the key

        Returns:
            Cache key string
        """
        # Hash the data
        hash_obj = hashlib.sha256(data)
        hash_str = hash_obj.hexdigest()

        if prefix:
            return f"{prefix}_{hash_str}"
        return hash_str

    async def process_pdf_with_metadata(self, pdf_file: BinaryIO) -> str:
        """
        Extract text from a PDF file with page metadata.

        Implements the IOCRService interface method.
        Adds page number references to each extracted text section.

        Args:
            pdf_file: PDF file object

        Returns:
            Extracted text from the PDF with page references

        Raises:
            RuntimeError: If processing fails
        """
        # Get PDF content
        pdf_file.seek(0)
        pdf_content = pdf_file.read()

        # Check cache if enabled
        if self.use_cache:
            # Generate a cache key from the PDF content with metadata prefix
            cache_key = self._generate_cache_key(pdf_content, prefix="metadata")

            # Try to get from cache
            cached_result = await self.cache_service.get(cache_key, "ocr")
            if cached_result is not None:
                self.logger.info(f"Cache hit for PDF OCR with metadata")
                return cached_result

        # Convert PDF to images with optimized resolution for OCR
        try:
            # Use simplified settings for PDF conversion to ensure compatibility
            images = convert_from_bytes(
                pdf_content,
                dpi=100,  # Lower DPI for faster processing
                thread_count=4,  # Use multiple threads for conversion
                use_cropbox=True,  # Use cropbox instead of mediabox for slightly smaller images
                grayscale=True  # Convert to grayscale to reduce size and improve OCR
            )
            self.logger.info(f"Converted PDF to {len(images)} images with optimized settings for metadata extraction")
        except Exception as e:
            self.logger.error(f"Failed to convert PDF to images: {str(e)}")
            raise RuntimeError(f"Failed to convert PDF to images: {str(e)}")

        # Process pages with controlled concurrency
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
            # Dynamic concurrency based on document size
            total_pages = len(images)

            # For smaller documents (<20 pages), use higher concurrency
            # For larger documents, use more conservative concurrency to avoid rate limits
            if total_pages < 20:
                max_concurrency = 16  # Higher concurrency for smaller documents
            elif total_pages < 50:
                max_concurrency = 12  # Medium concurrency for medium-sized documents
            else:
                max_concurrency = 10  # Lower concurrency for very large documents

            self.logger.info(f"Using concurrency of {max_concurrency} for {total_pages} pages with metadata")
            semaphore = asyncio.Semaphore(max_concurrency)

            # Create a progress tracking mechanism
            processed_pages = 0

            async def process_page(page_number: int, image: Image.Image) -> Tuple[int, str]:
                """Process a single page with semaphore control"""
                nonlocal processed_pages
                async with semaphore:
                    try:
                        self.logger.info(f"Processing metadata page {page_number}/{total_pages}")
                        base64_img = self._encode_image_to_base64(image)
                        max_tokens = self._estimate_tokens(image)
                        text = await self._extract_text_from_image(base64_img, session, max_tokens)

                        # Update progress
                        processed_pages += 1
                        progress_pct = (processed_pages / total_pages) * 100
                        self.logger.info(f"Completed metadata page {page_number}/{total_pages} ({progress_pct:.1f}%)")

                        return page_number, text
                    except Exception as e:
                        self.logger.error(f"Error processing page {page_number}: {e}")
                        # Retry once on failure
                        try:
                            self.logger.info(f"Retrying metadata page {page_number}/{total_pages}")
                            # Wait a bit before retrying
                            await asyncio.sleep(2)
                            base64_img = self._encode_image_to_base64(image)
                            max_tokens = self._estimate_tokens(image)
                            text = await self._extract_text_from_image(base64_img, session, max_tokens)

                            # Update progress
                            processed_pages += 1
                            progress_pct = (processed_pages / total_pages) * 100
                            self.logger.info(f"Retry successful for metadata page {page_number}/{total_pages} ({progress_pct:.1f}%)")

                            return page_number, text
                        except Exception as retry_e:
                            self.logger.error(f"Retry failed for metadata page {page_number}: {retry_e}")
                            processed_pages += 1
                            return page_number, f"Error processing page {page_number}: {str(retry_e)}"

            # Process pages in batches for better memory management
            batch_size = 20  # Process 20 pages at a time
            all_results = []

            # Create batches of pages
            page_batches = [(i, images[i-1]) for i in range(1, len(images) + 1)]
            page_batches = [page_batches[i:i + batch_size] for i in range(0, len(page_batches), batch_size)]

            for batch_idx, batch in enumerate(page_batches):
                self.logger.info(f"Processing metadata batch {batch_idx+1}/{len(page_batches)} ({len(batch)} pages)")

                # Create tasks for current batch
                batch_tasks = [
                    process_page(page_number, image)
                    for page_number, image in batch
                ]

                # Process current batch in parallel
                batch_results = await asyncio.gather(*batch_tasks)
                all_results.extend(batch_results)

                self.logger.info(f"Completed metadata batch {batch_idx+1}/{len(page_batches)}")

                # Free up memory
                batch_tasks.clear()

            # Sort by page number and format
            all_results.sort(key=lambda x: x[0])  # Sort by page number

            # Combine results with page numbers
            all_text_segments = [f"[Page {page}] {text}" for page, text in all_results]

            # Join all segments with newlines
            final_text = "\n\n".join(all_text_segments)
            self.logger.info(f"PDF metadata processing complete, extracted {len(final_text)} characters")

            # Cache the result if caching is enabled
            if self.use_cache:
                cache_key = self._generate_cache_key(pdf_content, prefix="metadata")
                await self.cache_service.set(cache_key, final_text, "ocr", self.cache_ttl)
                self.logger.info(f"Cached PDF OCR with metadata result")

        return final_text

    async def process_image(self, image_data: bytes, prompt_text: Optional[str] = None) -> str:
        """
        Extract text from an image.

        Implements the IOCRService interface method.

        Args:
            image_data: Binary image data
            prompt_text: Optional prompt to guide the extraction

        Returns:
            Extracted text from the image

        Raises:
            RuntimeError: If processing fails
        """
        # Check cache if enabled
        if self.use_cache:
            # Generate a cache key from the image data
            cache_key = self._generate_cache_key(image_data)

            # Try to get from cache
            cached_result = await self.cache_service.get(cache_key, "ocr")
            if cached_result is not None:
                self.logger.info(f"Cache hit for image OCR")
                return cached_result

        # Convert bytes to PIL Image
        try:
            image = Image.open(io.BytesIO(image_data))
            self.logger.info(f"Processing image of size {image.size}")

            # Optimize image for OCR if it's very large
            if image.width > 2000 or image.height > 2000:
                self.logger.info(f"Image is very large, optimizing for OCR")
                # Apply additional preprocessing for large images
                from PIL import ImageFilter
                image = image.filter(ImageFilter.SHARPEN)

        except Exception as e:
            self.logger.error(f"Failed to open image: {str(e)}")
            raise RuntimeError(f"Failed to open image: {str(e)}")

        # Encode image to base64 with optimized settings
        base64_img = self._encode_image_to_base64(image)
        max_tokens = self._estimate_tokens(image)

        # Extract text with retry logic
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
            try:
                self.logger.info(f"Sending image to OCR service")
                result = await self._extract_text_from_image(base64_img, session, max_tokens, prompt_text)
                self.logger.info(f"Image processing complete, extracted {len(result)} characters")

                # Cache the result if caching is enabled
                if self.use_cache:
                    await self.cache_service.set(self._generate_cache_key(image_data), result, "ocr", self.cache_ttl)
                    self.logger.info(f"Cached image OCR result")

                return result
            except Exception as e:
                self.logger.error(f"Error processing image: {e}")

                # Retry once with different settings
                try:
                    self.logger.info(f"Retrying image processing with different settings")
                    # Wait a bit before retrying
                    await asyncio.sleep(2)

                    # Try with different image settings
                    image = Image.open(io.BytesIO(image_data))
                    # Convert to grayscale directly
                    if image.mode != 'L':
                        image = image.convert('L')
                    # Apply different enhancement
                    from PIL import ImageEnhance
                    enhancer = ImageEnhance.Contrast(image)
                    image = enhancer.enhance(2.0)  # Higher contrast for retry

                    # Encode with different settings
                    base64_img = self._encode_image_to_base64(image, max_width=1000, max_height=1000)

                    # Try again
                    result = await self._extract_text_from_image(base64_img, session, max_tokens, prompt_text)
                    self.logger.info(f"Retry successful, extracted {len(result)} characters")

                    # Cache the result if caching is enabled
                    if self.use_cache:
                        await self.cache_service.set(self._generate_cache_key(image_data), result, "ocr", self.cache_ttl)
                        self.logger.info(f"Cached image OCR result after retry")

                    return result
                except Exception as retry_e:
                    self.logger.error(f"Retry failed: {retry_e}")
                    raise RuntimeError(f"Error processing image after retry: {str(retry_e)}")

    async def process_multiple_images(self, images: List[bytes], prompt_text: Optional[str] = None) -> str:
        """
        Extract text from multiple images and combine the results.

        Implements the IOCRService interface method.
        Processes multiple images in parallel with controlled concurrency.

        Args:
            images: List of binary image data
            prompt_text: Optional prompt to guide the extraction

        Returns:
            Combined extracted text from all images

        Raises:
            RuntimeError: If processing fails
        """
        self.logger.info(f"Processing {len(images)} images")

        # No images to process
        if not images:
            return ""

        # Process images with controlled concurrency
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
            # Dynamic concurrency based on number of images
            total_images = len(images)

            # For fewer images, use higher concurrency
            if total_images < 5:
                max_concurrency = 16  # Higher concurrency for fewer images
            elif total_images < 10:
                max_concurrency = 12  # Medium concurrency for medium number of images
            else:
                max_concurrency = 8   # Lower concurrency for many images

            self.logger.info(f"Using concurrency of {max_concurrency} for {total_images} images")
            semaphore = asyncio.Semaphore(max_concurrency)

            # Create a progress tracking mechanism
            processed_images = 0

            async def process_single_image(img_data: bytes, index: int) -> str:
                """Process a single image with semaphore control"""
                nonlocal processed_images
                async with semaphore:
                    try:
                        image = Image.open(io.BytesIO(img_data))
                        base64_img = self._encode_image_to_base64(image)
                        max_tokens = self._estimate_tokens(image)
                        text = await self._extract_text_from_image(base64_img, session, max_tokens, prompt_text)

                        # Update progress
                        processed_images += 1
                        progress_pct = (processed_images / total_images) * 100
                        self.logger.info(f"Processed image {index+1}/{total_images} ({progress_pct:.1f}%)")

                        return text
                    except Exception as e:
                        self.logger.error(f"Error processing image {index+1}: {e}")
                        # Retry once on failure
                        try:
                            self.logger.info(f"Retrying image {index+1}/{total_images}")
                            # Wait a bit before retrying
                            await asyncio.sleep(2)
                            image = Image.open(io.BytesIO(img_data))
                            base64_img = self._encode_image_to_base64(image)
                            max_tokens = self._estimate_tokens(image)
                            text = await self._extract_text_from_image(base64_img, session, max_tokens, prompt_text)

                            # Update progress
                            processed_images += 1
                            progress_pct = (processed_images / total_images) * 100
                            self.logger.info(f"Retry successful for image {index+1}/{total_images} ({progress_pct:.1f}%)")

                            return text
                        except Exception as retry_e:
                            self.logger.error(f"Retry failed for image {index+1}: {retry_e}")
                            processed_images += 1
                            return f"[Error processing image {index+1}]"

            # Process images in batches for better memory management if there are many
            if total_images > 10:
                batch_size = 5  # Process 5 images at a time for large sets
                all_results = []

                # Create batches of images
                image_batches = [(i, img) for i, img in enumerate(images)]
                image_batches = [image_batches[i:i + batch_size] for i in range(0, len(image_batches), batch_size)]

                for batch_idx, batch in enumerate(image_batches):
                    self.logger.info(f"Processing image batch {batch_idx+1}/{len(image_batches)} ({len(batch)} images)")

                    # Create tasks for current batch
                    batch_tasks = [
                        process_single_image(img_data, idx)
                        for idx, img_data in batch
                    ]

                    # Process current batch in parallel
                    batch_results = await asyncio.gather(*batch_tasks)
                    all_results.extend(batch_results)

                    self.logger.info(f"Completed image batch {batch_idx+1}/{len(image_batches)}")

                    # Free up memory
                    batch_tasks.clear()

                # Join all results
                final_text = "\n\n".join(all_results)
            else:
                # For smaller sets, process all at once
                tasks = [
                    process_single_image(img_data, i)
                    for i, img_data in enumerate(images)
                ]

                # Wait for all tasks to complete
                results = await asyncio.gather(*tasks)

                # Join all results
                final_text = "\n\n".join(results)

            self.logger.info(f"Multiple image processing complete, extracted {len(final_text)} characters")

        return final_text

    # Legacy compatibility methods

    async def process_pdf_legacy(self, pdf_file) -> str:
        """
        Legacy compatibility method for original ocr_with_gpt4o.py

        Args:
            pdf_file: PDF file object

        Returns:
            Extracted text
        """
        return await self.process_pdf(pdf_file)

    async def process_pdf_with_metadata_legacy(self, pdf_file) -> str:
        """
        Legacy compatibility method for original ocr_with_gpt4o.py

        Args:
            pdf_file: PDF file object

        Returns:
            Extracted text with page references
        """
        return await self.process_pdf_with_metadata(pdf_file)