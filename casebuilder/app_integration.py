"""
Integration module for CaseBuilder.AI.
Provides compatibility layer between new architecture and existing Streamlit app.
"""

import streamlit as st
from typing import Optional
from .infrastructure.dependency_injection import ServiceLocator
from .interfaces.web.streamlit_adapter import StreamlitAdapter

# Import necessary components for AI service and text processing
import os
import logging
from .adapters.ai.openai_adapter import OpenAIAdapter
from .adapters.ocr.openai_ocr_adapter import OpenAIOCRAdapter
from .core.services.text_processor_service import TextProcessorService
from .core.services.cache_service import CacheService
from .infrastructure.mediator import Mediator

# Initialize application components
service_locator = ServiceLocator()

# Register required services
api_key = os.getenv("OPENAI_API_KEY")

# Create cache service
cache_service = CacheService()

# Create services with cache
ai_service = OpenAIAdapter(api_key=api_key, cache_service=cache_service)
ocr_service = OpenAIOCRAdapter(api_key=api_key, cache_service=cache_service)
text_processor_service = TextProcessorService(ai_service)

# Register services
service_locator.register_service("ICacheService", cache_service)
service_locator.register_service("IAIService", ai_service)
service_locator.register_service("IOCRService", ocr_service)
service_locator.register_service("ITextProcessorService", text_processor_service)

# Create a mediator
mediator = Mediator()

# Import command handlers
from .application.handlers.facts_liability_handlers import (
    FactsOfLossHandler,
    DetermineLiabilityHandler,
    FactsAndLiabilityDemandHandler
)
from .application.handlers.medical_analysis_handlers import (
    DetailedMedicalAnalysisHandler,
    MedicalExpensesAnalysisHandler,
    FutureMedicalExpensesHandler
)
from .application.handlers.demand_letter_handlers import (
    GeneralDamagesAnalysisHandler,
    ImpactOnLifestyleAnalysisHandler,
    GenerateDemandLetterHandler
)
from .application.handlers.triage_handlers import (
    TriageDocumentHandler
)
from .application.commands.triage_commands import (
    TriageDocumentCommand
)

# Import commands
from .application.commands.facts_liability_commands import (
    FactsOfLossCommand,
    DetermineLiabilityCommand,
    FactsAndLiabilityDemandCommand
)
from .application.commands.medical_analysis_commands import (
    DetailedMedicalAnalysisCommand,
    MedicalExpensesAnalysisCommand,
    FutureMedicalExpensesCommand
)
from .application.commands.demand_letter_commands import (
    GeneralDamagesAnalysisCommand,
    ImpactOnLifestyleAnalysisCommand,
    GenerateDemandLetterCommand
)

# Get required services from service locator
ai_service = service_locator.get_service("IAIService")
text_processor_service = service_locator.get_service("ITextProcessorService")

# Create handlers
# Facts and liability handlers
facts_of_loss_handler = FactsOfLossHandler(ai_service, text_processor_service)
determine_liability_handler = DetermineLiabilityHandler(ai_service, text_processor_service)
facts_and_liability_demand_handler = FactsAndLiabilityDemandHandler(ai_service, text_processor_service)

# Medical analysis handlers
detailed_medical_analysis_handler = DetailedMedicalAnalysisHandler(ai_service, text_processor_service)
medical_expenses_handler = MedicalExpensesAnalysisHandler(ai_service, text_processor_service)
future_medical_expenses_handler = FutureMedicalExpensesHandler(ai_service, text_processor_service)

# Demand letter handlers
general_damages_handler = GeneralDamagesAnalysisHandler(ai_service, text_processor_service)
impact_on_lifestyle_handler = ImpactOnLifestyleAnalysisHandler(ai_service, text_processor_service)
demand_letter_handler = GenerateDemandLetterHandler(ai_service, text_processor_service)

# Triage handlers
triage_document_handler = TriageDocumentHandler(ai_service, text_processor_service)

# Register handlers with mediator
# Facts and liability commands
mediator.register_command_handler(FactsOfLossCommand, facts_of_loss_handler)
mediator.register_command_handler(DetermineLiabilityCommand, determine_liability_handler)
mediator.register_command_handler(FactsAndLiabilityDemandCommand, facts_and_liability_demand_handler)

# Medical analysis commands
mediator.register_command_handler(DetailedMedicalAnalysisCommand, detailed_medical_analysis_handler)
mediator.register_command_handler(MedicalExpensesAnalysisCommand, medical_expenses_handler)
mediator.register_command_handler(FutureMedicalExpensesCommand, future_medical_expenses_handler)

# Demand letter commands
mediator.register_command_handler(GeneralDamagesAnalysisCommand, general_damages_handler)
mediator.register_command_handler(ImpactOnLifestyleAnalysisCommand, impact_on_lifestyle_handler)
mediator.register_command_handler(GenerateDemandLetterCommand, demand_letter_handler)

# Triage commands
mediator.register_command_handler(TriageDocumentCommand, triage_document_handler)

# Initialize the application with the mediator
streamlit_adapter = StreamlitAdapter(mediator)

async def extract_facts_and_liability() -> str:
    return await streamlit_adapter.extract_facts_and_liability()

async def comprehensive_medical_analysis() -> str:
    return await streamlit_adapter.comprehensive_medical_analysis()

async def identify_medical_expenses() -> str:
    return await streamlit_adapter.identify_medical_expenses()

async def identify_future_treatments() -> str:
    return await streamlit_adapter.identify_future_treatments()

async def impact_on_lifestyle() -> str:
    return await streamlit_adapter.impact_on_lifestyle()

async def identify_general_damages() -> str:
    return await streamlit_adapter.identify_general_damages()

async def create_demand_letter() -> str:
    return await streamlit_adapter.create_demand_letter()

def show_progress_tracker() -> None:
    """Display progress tracker in sidebar."""
    streamlit_adapter.show_progress_sidebar()
