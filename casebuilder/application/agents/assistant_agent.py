"""
AssistantAgent for CaseBuilder chat assistant.

This agent analyzes user queries and provides intelligent responses based on
the application code and predefined answers.
"""

import logging
import re
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent

# Configure logging
logger = logging.getLogger(__name__)

class AssistantAgent(BaseAgent):
    """
    Agent specialized in responding to user queries about the CaseBuilder application.

    This agent is designed to:
    - Analyze user queries about the application
    - Provide intelligent responses based on application code
    - Maintain compatibility with predefined answers
    - <PERSON>le common questions about features, usage, and functionality
    """
    DEFAULT_ASSISTANT_MODEL = "gpt-4o"

    def __init__(self, ai_service, text_processor, predefined_answers=None):
        """
        Initialize the AssistantAgent.

        Args:
            ai_service: The AI service to use for processing
            text_processor: The text processor service
            predefined_answers: Optional dictionary of predefined answers
        """
        super().__init__(ai_service, text_processor)
        self.predefined_answers = predefined_answers or {}
        self.question_patterns = self._get_default_question_patterns()

    async def process(self, content: str, context: Dict[str, Any]) -> str:
        """
        Process a user query and generate a response.

        Args:
            content: The user query
            context: Additional context for the query, including:
                - app_state: Current application state (optional)
                - user_info: User information (optional)
                - chat_history: Previous chat messages (optional)

        Returns:
            Response to the user query
        """
        # Check if this is a technical question that should use codebase knowledge
        is_technical_question = self._is_technical_question(content)

        # For non-technical questions, check predefined answers first
        predefined_response = None
        if not is_technical_question:
            predefined_response = self._check_predefined_answers(content)
            if predefined_response:
                return predefined_response

        # Search the codebase for relevant information
        codebase_info = await self._search_codebase(content)

        # For technical questions, if we found relevant codebase info, use it
        # Otherwise, check for predefined answers as a fallback
        if is_technical_question:
            if codebase_info and codebase_info != "No specific information found in the codebase.":
                # We have relevant technical information, so use it
                pass
            else:
                # No relevant technical information found, check predefined answers
                predefined_response = self._check_predefined_answers(content)
                if predefined_response:
                    return predefined_response

        # Generate a response using the AI model with codebase information
        prompt = self._get_extraction_prompt(context, codebase_info)

        # Process with AI model
        try:
            result = await self.text_processor.process_text_in_segments(
                model_alias=self.DEFAULT_ASSISTANT_MODEL,
                text=content,
                prompt=prompt
            )
            return result
        except Exception as e:
            logger.error(f"Error generating assistant response: {str(e)}")
            # Fallback to generic response
            return "I'm here to help with questions about CaseBuilder. You can ask about document uploads, analysis features, demand letters, or check the FAQ section in the sidebar for more information."

    def _is_technical_question(self, query: str) -> bool:
        """
        Determine if a query is a technical question that should prioritize codebase knowledge.

        Args:
            query: The user query

        Returns:
            True if the query is a technical question, False otherwise
        """
        # Convert query to lowercase for case-insensitive matching
        query_lower = query.lower()

        # List of terms that indicate a technical question
        technical_terms = [
            "store", "save", "keep", "delete", "remove", "retain",
            "security", "secure", "protect", "encrypt", "safe",
            "privacy", "private", "confidential",
            "process", "workflow", "handle", "data", "document",
            "pdf", "file", "upload", "storage", "token", "tokens"
        ]

        # Check if any technical terms are in the query
        for term in technical_terms:
            if term in query_lower:
                return True

        # Check for question patterns that indicate technical questions
        technical_patterns = [
            # Demand letter token patterns - these must come first
            r"how many tokens (to|do) (build|generate|create|do|make|for) a demand letter",
            r"how many tokens i need to do a demand letter",
            r"how many tokens (do|does|would|will) (i|it) (need|take|cost|require) (to|for) (do|make|create|generate|build) (a|the) demand letter",
            r"how many tokens to build a demand letter",
            r"(how many|number of) tokens (for|to) (build|generate|create) (a|the) demand letter",
            r"(how many|number of) tokens (does|do) (a|the) demand letter (cost|use|need|require)",
            r"tokens (for|to) (build|generate|create) (a|the) demand letter",
            r"(how many|number of) tokens (for|to|does|do) (a|the) demand letter",

            # Other technical patterns
            r"(do|does|are) you (store|save|keep)",
            r"(where|how) (are|do) (you|casebuilder) (store|save)",
            r"(what happens|happen) (to|with) (my|the) (documents|files|pdfs)",
            r"(are|is) (my|the) (documents|files|pdfs) (stored|saved)",
            r"(how|is) (it|data|information) (secure|protected|safe)",
            r"(how|do) you (handle|use|protect) (my|client|clients) (data|information)"
        ]

        for pattern in technical_patterns:
            if re.search(pattern, query_lower):
                return True

        return False

    async def _search_codebase(self, query: str) -> str:
        """
        Search the codebase for information relevant to the query.

        Args:
            query: The user query

        Returns:
            Relevant information from the codebase
        """
        try:
            # Extract key terms from the query for better search
            search_terms = self._extract_search_terms(query)

            # Combine the original query with extracted terms for a more comprehensive search
            search_query = f"{query} {' '.join(search_terms)}"

            # Create a knowledge base of common questions and answers about CaseBuilder
            knowledge_base = self._get_codebase_knowledge_base()

            # Find relevant information in the knowledge base
            relevant_info = []
            for topic, info in knowledge_base.items():
                # Check if any of the search terms are in the topic
                if any(term.lower() in topic.lower() for term in search_terms + [query]):
                    relevant_info.append(f"TOPIC: {topic}\n{info}")

            # If we found relevant information, return it
            if relevant_info:
                return "\n\n".join(relevant_info)

            # Otherwise, use the AI service to generate a response based on its knowledge
            search_prompt = f"""
            You are a codebase search assistant for the CaseBuilder application.

            Your task is to search the CaseBuilder codebase and provide relevant information about:
            {search_query}

            Focus on:
            1. How the application handles documents and data
            2. Security and privacy features
            3. User interface components and workflows
            4. Core functionality and features

            Based on your knowledge of the CaseBuilder codebase, provide specific information about:
            - How documents are stored and processed
            - What happens to uploaded files
            - Security measures for user data
            - Application workflow and features

            Return only factual information that you are certain is in the codebase.
            Include specific details about implementation when relevant.
            """

            # Use a more capable model for the search to improve accuracy
            search_result = await self.ai_service.process_text(
                model_alias="gpt-4o-mini",
                system_prompt=search_prompt,
                user_content=f"Find information in the CaseBuilder codebase about: {search_query}",
                max_tokens=1000
            )

            return search_result
        except Exception as e:
            logger.error(f"Error searching codebase: {str(e)}")
            return "No specific information found in the codebase."

    def _get_codebase_knowledge_base(self) -> Dict[str, str]:
        """
        Get a knowledge base of common questions and answers about the CaseBuilder codebase.

        Returns:
            Dictionary mapping topics to information
        """
        return {
            "Document Storage": """
            CaseBuilder does not permanently store uploaded documents on the server.

            When a user uploads a document (PDF or image), it is temporarily stored in memory for processing.
            The document is analyzed by the appropriate agent (TriageAgent, PoliceReportAgent, MedicalRecordAgent, etc.)
            and the extracted information is used to generate the analysis or demand letter.

            Once the analysis is complete, the document is discarded from memory. No permanent storage of
            the original documents occurs on the server. This approach enhances privacy and security by
            minimizing data retention.

            The relevant code for document handling is in the document_processor.py and upload_handlers.py files.
            """,

            "Security and Privacy": """
            CaseBuilder implements several security and privacy measures:

            1. No permanent storage of uploaded documents - files are processed in memory and then discarded
            2. Authentication system using secure tokens
            3. HTTPS encryption for all data transmission
            4. Input validation and sanitization to prevent injection attacks
            5. Rate limiting to prevent abuse
            6. Regular security audits and updates

            The application follows privacy-by-design principles, collecting only the minimum necessary
            information to provide the service. User data is protected according to industry best practices.
            """,

            "Document Processing Workflow": """
            The document processing workflow in CaseBuilder follows these steps:

            1. User uploads a document (PDF or image)
            2. The TriageAgent analyzes the document to determine its type (police report, medical record, etc.)
            3. The document is routed to the appropriate specialized agent for detailed analysis
            4. The agent extracts relevant information using AI models
            5. The extracted information is formatted according to user preferences
            6. The analysis is presented to the user
            7. If requested, a demand letter is generated based on the analyzed information

            This workflow is implemented using a mediator pattern with commands and handlers to ensure
            separation of concerns and maintainability.
            """,

            "AI Models Used": """
            CaseBuilder uses different AI models for different tasks to optimize performance and cost:

            - o4-mini: Used for TriageAgent and initial analysis
            - gpt-4o-2024-05-13: Used for image analysis
            - gpt-4o-mini: Used for medical analysis and OCR
            - gpt-4o: Used for liability analysis and demand letter generation

            The models are accessed through the OpenAI adapter, which provides a consistent interface
            for all AI operations in the application.
            """,

            "Performance Optimization": """
            CaseBuilder implements several performance optimizations:

            1. Parallel processing of documents when possible
            2. Batch processing for efficiency
            3. Image optimization before analysis
            4. Caching to avoid repeated analysis
            5. Progressive loading to show partial results while processing continues

            These optimizations help reduce processing time and improve the user experience.
            """,

            "User Interface Components": """
            The CaseBuilder UI is built with Streamlit and includes:

            1. Document uploaders for PDFs and images
            2. Analysis preference controls (detail level, format options)
            3. Demand letter customization options (tone, content emphasis, length)
            4. Results display with formatted output
            5. Sidebar with help and FAQ sections
            6. CaseBuilder Assistant chat feature

            The UI is designed to be intuitive and responsive, with clear feedback on processing status.
            """,

            "CaseBuilder Assistant": """
            The CaseBuilder Assistant is an intelligent agent that:

            1. Responds to user queries about the application
            2. Provides help on using features
            3. Explains document analysis capabilities
            4. Guides users on generating demand letters

            The assistant uses a combination of predefined answers and AI-generated responses
            based on the context of the query and information from the codebase.
            """,

            "Error Handling": """
            CaseBuilder implements comprehensive error handling:

            1. Input validation to prevent invalid uploads
            2. Graceful degradation when services are unavailable
            3. Informative error messages for users
            4. Detailed logging for debugging
            5. Automatic retry for transient errors

            Errors are logged and monitored to identify and fix issues quickly.
            """,

            "Token System": """
            CaseBuilder uses a token system to manage usage:

            1. Different actions consume different numbers of tokens:
               - Medical Record Analysis: 1 token
               - Police Report Analysis: 1 token
               - Demand Letter Generation: 5 tokens (this is the exact cost to generate a demand letter)
               - Image Analysis: 1 token per image

            2. Users can purchase token packages through their account settings
            3. Token usage is tracked and displayed to users in the sidebar
            4. Some actions may be limited based on available tokens
            5. The CaseBuilder Assistant chat feature is completely free and doesn't consume tokens

            The token system helps ensure fair usage and sustainability of the service.
            """,

            "Demand Letter Tokens": """
            Generating a demand letter in CaseBuilder costs exactly 5 tokens.

            This is a fixed cost regardless of the length or complexity of the letter.
            The demand letter generation process combines all the analyzed information from
            uploaded documents into a comprehensive, professional document.

            Other actions in CaseBuilder have different token costs:
            - Medical Record Analysis: 1 token
            - Police Report Analysis: 1 token
            - Image Analysis: 1 token per image

            Users can see their remaining tokens in the sidebar and purchase more through
            their account settings if needed.
            """,

            "Getting Started": """
            Getting started with CaseBuilder is easy and follows a simple 3-step process:

            1. Set your preferences
               - Use the sidebar to adjust case settings (state, case type, liability type)
               - Configure analysis preferences (detail level, format style)
               - Set demand letter preferences (tone, content emphasis, length)

            2. Upload your documents
               - Drag and drop PDFs (medical records, police reports) into the upload area
               - Upload images of injuries or accident scenes if available
               - The system will automatically detect document types

            3. Generate analysis and demand letters
               - Click the analysis buttons that appear based on your uploaded documents
               - For medical records: medical analysis, expense identification, future treatments
               - For police reports: facts extraction, liability analysis
               - For images: injury analysis, property damage assessment
               - When ready, generate a comprehensive demand letter

            The Quick Start Guide in the sidebar provides a concise reference for these steps.
            """,

            "Image Analysis and Demand Letters": """
            CaseBuilder fully supports image analysis and includes images in demand letters:

            1. Image Upload:
               - You can upload JPG and PNG images alongside police reports and medical records
               - Simply drag and drop all files together - the system automatically detects each document type
               - Images can be of injuries, accident scenes, property damage, or other relevant evidence

            2. Image Analysis:
               - For injury photos: Provides detailed descriptions of visible injuries and relates them to case context
               - For accident scenes: Analyzes vehicle positions, damages, and environmental factors
               - For property damage: Assesses extent and nature of damage
               - Each image analysis costs 1 token per image

            3. Integration with Demand Letters:
               - Images are automatically included in demand letters as exhibits when uploaded
               - The AI analysis of images is incorporated into the demand letter text
               - Image descriptions strengthen case presentation and provide visual evidence
               - The demand letter generation (5 tokens) includes all uploaded and analyzed content

            4. Workflow:
               - Upload images alongside other documents
               - Analyze images using the image analysis button that appears
               - Generate demand letter - images will be included as exhibits with detailed descriptions

            This integration ensures that visual evidence is properly documented and presented in your demand letters.
            """
        }

    def _extract_search_terms(self, query: str) -> List[str]:
        """
        Extract key terms from the query for better codebase search.

        Args:
            query: The user query

        Returns:
            List of key terms
        """
        # List of common technical terms in the CaseBuilder application
        technical_terms = [
            "document", "pdf", "image", "upload", "analysis", "police report",
            "medical record", "demand letter", "security", "privacy", "data",
            "storage", "token", "tokens", "authentication", "user interface", "sidebar",
            "preference", "customization", "feature", "liability", "medical",
            "expense", "treatment", "injury", "accident", "case", "client",
            "workflow", "process", "generate", "AI", "model", "GPT", "Claude",
            "OpenAI", "API", "service", "adapter", "handler", "command", "query",
            "agent", "extraction", "triage", "OCR", "text processing", "cost",
            "price", "pricing", "fee", "subscription", "payment", "purchase"
        ]

        # Convert query to lowercase for case-insensitive matching
        query_lower = query.lower()

        # Find matching terms
        matching_terms = []
        for term in technical_terms:
            if term.lower() in query_lower:
                matching_terms.append(term)

        # Map common questions to relevant topics
        question_topic_map = {
            # Document storage and privacy questions
            "store": ["Document Storage", "Security and Privacy"],
            "save": ["Document Storage", "Security and Privacy"],
            "keep": ["Document Storage", "Security and Privacy"],
            "delete": ["Document Storage", "Security and Privacy"],
            "remove": ["Document Storage", "Security and Privacy"],
            "retain": ["Document Storage", "Security and Privacy"],
            "where": ["Document Storage", "Security and Privacy"],

            # Security questions
            "secure": ["Security and Privacy"],
            "protect": ["Security and Privacy"],
            "encrypt": ["Security and Privacy"],
            "safe": ["Security and Privacy"],
            "privacy": ["Security and Privacy"],
            "confidential": ["Security and Privacy"],

            # Document processing questions
            "upload": ["Document Processing Workflow", "Document Storage", "Image Analysis and Demand Letters"],
            "file": ["Document Processing Workflow", "Document Storage"],
            "document": ["Document Processing Workflow", "Document Storage"],
            "pdf": ["Document Processing Workflow", "Document Storage"],
            "image": ["Document Processing Workflow", "Document Storage", "Image Analysis and Demand Letters"],
            "images": ["Image Analysis and Demand Letters"],
            "photo": ["Image Analysis and Demand Letters"],
            "photos": ["Image Analysis and Demand Letters"],
            "picture": ["Image Analysis and Demand Letters"],
            "pictures": ["Image Analysis and Demand Letters"],
            "process": ["Document Processing Workflow"],
            "analyze": ["Document Processing Workflow", "AI Models Used", "Image Analysis and Demand Letters"],

            # AI and models questions
            "ai": ["AI Models Used"],
            "model": ["AI Models Used"],
            "gpt": ["AI Models Used"],
            "claude": ["AI Models Used"],
            "openai": ["AI Models Used"],

            # Performance questions
            "performance": ["Performance Optimization"],
            "speed": ["Performance Optimization"],
            "fast": ["Performance Optimization"],
            "slow": ["Performance Optimization"],
            "optimize": ["Performance Optimization"],
            "efficient": ["Performance Optimization"],

            # UI questions
            "interface": ["User Interface Components"],
            "ui": ["User Interface Components"],
            "button": ["User Interface Components"],
            "sidebar": ["User Interface Components"],
            "display": ["User Interface Components"],
            "show": ["User Interface Components"],

            # Assistant questions
            "assistant": ["CaseBuilder Assistant"],
            "bot": ["CaseBuilder Assistant"],
            "chat": ["CaseBuilder Assistant"],
            "help": ["CaseBuilder Assistant"],

            # Error questions
            "error": ["Error Handling"],
            "problem": ["Error Handling"],
            "issue": ["Error Handling"],
            "fail": ["Error Handling"],
            "crash": ["Error Handling"],

            # Token questions
            "token": ["Token System"],
            "tokens": ["Token System"],
            "cost": ["Token System"],
            "price": ["Token System"],
            "pay": ["Token System"],
            "purchase": ["Token System"],
            "demand letter": ["Demand Letter Tokens", "Token System", "Document Processing Workflow", "Image Analysis and Demand Letters"],
            "how many": ["Demand Letter Tokens", "Token System"],
            "demand letter tokens": ["Demand Letter Tokens"],
            "tokens for demand letter": ["Demand Letter Tokens"],
            "tokens to build": ["Demand Letter Tokens"],
            "how many tokens": ["Demand Letter Tokens"],
            "taking into account": ["Image Analysis and Demand Letters"],
            "taken into account": ["Image Analysis and Demand Letters"],
            "included": ["Image Analysis and Demand Letters"],
            "alongside": ["Image Analysis and Demand Letters"],
            "with": ["Image Analysis and Demand Letters"],
            "together with": ["Image Analysis and Demand Letters"],

            # Getting started questions
            "get started": ["Getting Started"],
            "begin": ["Getting Started"],
            "start using": ["Getting Started"],
            "how to use": ["Getting Started"],
            "first time": ["Getting Started"],
            "new user": ["Getting Started"],
            "tutorial": ["Getting Started"],
            "guide": ["Getting Started"]
        }

        # Add topics based on query content
        for word, topics in question_topic_map.items():
            if word in query_lower:
                matching_terms.extend(topics)

        # Remove duplicates while preserving order
        seen = set()
        unique_terms = []
        for term in matching_terms:
            if term not in seen:
                seen.add(term)
                unique_terms.append(term)

        return unique_terms

    async def validate(self, result: str) -> bool:
        """
        Validate the assistant response.

        Args:
            result: The response to validate

        Returns:
            True if the result is valid, False otherwise
        """
        # Basic validation - check if result is not empty
        if not result or len(result.strip()) < 10:
            return False

        # Check that the response doesn't contain any harmful content
        harmful_patterns = [
            r"(password|api key|secret|token)",
            r"(hack|exploit|vulnerability)",
            r"(personal data|private information)"
        ]

        for pattern in harmful_patterns:
            if re.search(pattern, result.lower()):
                return False

        return True

    def _get_extraction_prompt(self, context: Dict[str, Any], codebase_info: str = "") -> str:
        """
        Generate the prompt for the AI model based on context and codebase information.

        Args:
            context: The context for the query
            codebase_info: Information retrieved from the codebase

        Returns:
            Prompt for the AI service
        """
        chat_history = context.get("chat_history", [])

        # Format chat history for context
        chat_context = ""
        if chat_history:
            # Include up to 5 most recent messages for context
            recent_messages = chat_history[-5:]
            chat_context = "Recent conversation:\n" + "\n".join([
                f"User: {msg['content']}" if msg['role'] == 'user' else f"Assistant: {msg['content']}"
                for msg in recent_messages
            ])

        # Build the system prompt
        prompt = """
        You are the CaseBuilder Assistant, an AI helper for the CaseBuilder application.

        ABOUT CASEBUILDER:
        CaseBuilder is an AI-powered platform for legal professionals to analyze personal injury cases and generate demand letters.
        It supports uploading and analyzing police reports, medical records, and images.
        It can generate comprehensive analyses and demand letters based on these documents.

        YOUR ROLE:
        - Answer questions about CaseBuilder's features and functionality
        - Provide help on how to use the application
        - Explain document analysis capabilities
        - Guide users on generating demand letters
        - Respond to questions about tokens, pricing, and account features

        GUIDELINES:
        1. Be concise and helpful in your responses
        2. Focus only on CaseBuilder functionality
        3. For security or privacy questions, refer to official policies
        4. Provide accurate technical details based on the codebase information provided
        5. If you don't know an answer, suggest checking the FAQ or contacting support
        6. Maintain a professional, friendly tone

        FEATURES YOU CAN DISCUSS:
        - Document upload and analysis (PDF files for medical records and police reports, images for injuries)
        - Medical record analysis (narrative, chronological, bulleted, or tabular formats)
        - Police report analysis (facts and liability extraction)
        - Demand letter generation (customization options, content included)
        - Token system (costs for different actions)
        - Case settings and preferences

        RESPONSE FORMAT:
        Provide direct, helpful responses without unnecessary preamble.
        """

        # Add codebase information if available
        if codebase_info and codebase_info != "No specific information found in the codebase.":
            prompt += f"""

            CODEBASE INFORMATION:
            The following information was found in the CaseBuilder codebase that may be relevant to this query:

            {codebase_info}

            Use this information to provide accurate and specific answers about how CaseBuilder works.
            """

        # Add chat history context if available
        if chat_context:
            prompt += f"\n\n{chat_context}"

        return prompt

    def _check_predefined_answers(self, query: str) -> Optional[str]:
        """
        Check if there's a predefined answer for the query.

        Args:
            query: The user query

        Returns:
            Predefined answer if available, None otherwise
        """
        if not self.predefined_answers:
            return None

        # Special case for exact match from app.py
        if "exact_match" in self.predefined_answers:
            response = self.predefined_answers["exact_match"]
            if isinstance(response, dict) and "content" in response:
                return response["content"]
            return response

        # Convert query to lowercase for matching
        query_lower = query.lower()

        # First, check for specific image-related questions that need priority handling
        image_demand_letter_patterns = [
            r"(may|can) (i|we) upload (pictures|images|photos) (alongside|with|together with)",
            r"(are|is) (pictures|images|photos) (taken into account|taking into account|included|considered)",
            r"(my|the) question (was|is) about (the|a) (picture|image|photo)"
        ]

        for pattern in image_demand_letter_patterns:
            if re.search(pattern, query_lower):
                if "upload" in query_lower and ("alongside" in query_lower or "with" in query_lower):
                    return "Yes, absolutely! You can upload images alongside police reports and medical records. CaseBuilder accepts JPG and PNG images of injuries or accident scenes. Simply drag and drop all your files together - the system will automatically detect each document type and provide the appropriate analysis options."
                elif "taken into account" in query_lower or "taking into account" in query_lower or "included" in query_lower or "considered" in query_lower:
                    return "Yes, images are fully analyzed and taken into account. For injury photos, the AI provides detailed descriptions of visible injuries. For accident scenes, it analyzes vehicle positions and damages. This analysis is then incorporated into your demand letter as supporting evidence."
                elif "question" in query_lower and ("picture" in query_lower or "image" in query_lower or "photo" in query_lower):
                    return "Yes, images are included in demand letters as exhibits. The AI analyzes your photos and incorporates detailed descriptions into the demand letter, making them a powerful part of your case presentation. Image analysis costs 1 token per image."

        # Check for specific patterns about demand letter tokens
        demand_letter_token_patterns = [
            r"how many tokens (to|do) (build|generate|create|do|make|for) a demand letter",
            r"how many tokens i need to do a demand letter",
            r"how many tokens (do|does|would|will) (i|it) (need|take|cost|require) (to|for) (do|make|create|generate|build) (a|the) demand letter"
        ]

        # Check for specific getting started patterns
        getting_started_patterns = [
            r"how to use",
            r"how can i get started",
            r"how do i start"
        ]

        for pattern in getting_started_patterns:
            if re.search(pattern, query_lower):
                return "Getting started with CaseBuilder is easy! Just follow these 3 steps: 1) Set your preferences in the sidebar (case settings, summary styles), 2) Upload your documents (drag and drop PDFs or images), and 3) Click the analysis buttons that appear. For medical records, you'll see medical analysis options. For police reports, you'll see liability analysis options. When you're ready, generate a demand letter with a single click."

        for pattern in demand_letter_token_patterns:
            if re.search(pattern, query_lower):
                # Return a direct answer about demand letter tokens
                return "5 tokens. Generating a demand letter requires 5 tokens in CaseBuilder."

        # Check for pattern matches
        for pattern, keyword in self.question_patterns.items():
            if re.search(pattern, query_lower):
                response = self.predefined_answers.get(keyword)
                if response:
                    # Return the content directly
                    if isinstance(response, dict) and "content" in response:
                        return response["content"]
                    return response

        # Check for exact keyword matches
        for keyword, response in self.predefined_answers.items():
            if keyword in query_lower:
                # Return the content directly
                if isinstance(response, dict) and "content" in response:
                    return response["content"]
                return response

        # Handle common conversational patterns
        if self._is_greeting(query_lower):
            return "Hello! How can I help you with CaseBuilder today?"

        if self._is_thanks(query_lower):
            return "You're welcome! Is there anything else I can help you with?"

        if self._is_goodbye(query_lower):
            return "Goodbye! Feel free to ask if you have more questions about CaseBuilder in the future."

        # No predefined answer found
        return None

    def _is_greeting(self, query: str) -> bool:
        """Check if the query is a greeting."""
        greetings = ["hi", "hello", "hey", "greetings", "howdy"]
        return any(greeting == query or greeting in query.split() for greeting in greetings)

    def _is_thanks(self, query: str) -> bool:
        """Check if the query is expressing thanks."""
        thanks = ["thank", "thanks", "appreciate", "grateful"]
        return any(thank in query for thank in thanks)

    def _is_goodbye(self, query: str) -> bool:
        """Check if the query is a goodbye."""
        goodbyes = ["bye", "goodbye", "see you", "farewell"]
        return any(goodbye in query for goodbye in goodbyes)

    def _get_default_question_patterns(self) -> Dict[str, str]:
        """Get default question patterns for matching."""
        return {
            # Image and demand letter specific questions - these must come first for priority matching
            r"(may|can) (i|we) upload (pictures|images|photos) (alongside|with|together with) (police|medical) (and|records|reports)": "images_with_documents",
            r"upload (pictures|images|photos) (alongside|with|together with) (documents|records|reports)": "images_with_documents",
            r"(can|may) (i|we) (upload|add|include) (pictures|images|photos) (with|alongside) (other|the) (documents|files)": "images_with_documents",
            r"(are|is) (pictures|images|photos) (taken into account|included|considered) (in|for) (the|a) demand letter": "images_in_demand_letter",
            r"(does|do|will) (the|a) demand letter (include|contain|use) (pictures|images|photos)": "images_in_demand_letter",
            r"(are|is) (pictures|images|photos) (part of|included in) (the|a) demand letter": "images_in_demand_letter",
            r"(how|what) (about|are) (the|my) (pictures|images|photos)": "image_analysis",
            r"(what happens|happen) (to|with) (the|my) (pictures|images|photos)": "image_analysis",

            # Upload related questions
            r"(can|how) (i|to) upload": "upload",
            r"upload (multiple|several) (documents|files)": "upload",
            r"(multiple|several) (documents|files) at once": "upload",
            r"(how|can) (i|to) (add|submit) (documents|files)": "upload",

            # Security related questions
            r"(how|is) (it|data|information) (secure|protected|safe)": "security",
            r"(keep|protect) (my|client|clients|customer) data (secure|safe)": "security",
            r"(data|information) security": "security",
            r"secure": "security",
            r"(how|is) (my|the) (data|information) (handled|processed)": "security",

            # Privacy related questions
            r"(privacy|private|confidential)": "privacy",
            r"(how|do) you (handle|use|protect) (my|client|clients) (data|information)": "privacy",
            r"(what|how) (about|is) (my|client) (privacy|confidentiality)": "privacy",

            # Storage related questions
            r"(do|does|are) you (store|save|keep) (the|my) (documents|files|pdfs|images)": "store",
            r"(where|how) (are|do) (you|casebuilder) (store|save) (the|my) (documents|files|pdfs)": "store",
            r"(what happens|happen) (to|with) (my|the) (documents|files|pdfs) (after|when)": "store",
            r"(are|is) (my|the) (documents|files|pdfs) (stored|saved)": "store",
            r"(storage|retention) (of|for) (documents|files|pdfs)": "store",

            # Medical records related questions
            r"(analyze|analysis) (of|for) medical records": "medical_analysis",
            r"medical records (only|analysis)": "medical_analysis",
            r"(can|how) (i|to) (analyze|use) (this|it) (for|with) medical records": "medical_analysis",
            r"(what|how) (about|does) (the|your) medical (record|analysis) (work|function)": "medical_analysis",
            r"(medical records|medical analysis)": "medical_analysis",

            # Document types
            r"(what|which) (documents|files) (can|types|supported)": "document",
            r"(supported|acceptable) (document|file) (types|formats)": "document",
            r"(what|which) (kind|type) of (documents|files) (can|do) (i|you) (use|accept)": "document",
            r"(does|can|will) (it|the system|casebuilder) (recognize|detect|identify) (document|file) types": "document",
            r"(automatic|automatically) (recognize|detect|identify) (document|file) types": "document",
            r"(document|file) (types|formats)": "document",

            # Analysis types
            r"(what|which|types of) analysis": "analysis",
            r"(can|how) (i|to) (analyze|get) (analysis)": "analysis",
            r"(what|how) (can|does) (casebuilder|it|the system) analyze": "analysis",

            # Demand letter
            r"(demand letter|generate letter)": "demand letter",
            r"(create|make|generate) (a|the) (demand|letter)": "demand letter",
            r"(how|what) (about|is) (the|a) demand letter": "demand letter",
            r"(what|how) (is|does) (included|contain) (in|the) (demand|the) letter": "demand letter",

            # Cost and tokens
            r"(cost|price|pricing|token|tokens)": "cost",
            r"(how much|fee|subscription)": "cost",
            r"(is|are) (there|any) (cost|fee|charge) (for|to) (chat|talking|using) (with|to) you": "cost",
            r"(do|does) (i|it) (cost|pay) (to|for) (use|using) (this|the) (chat|assistant)": "cost",

            # Chat related questions
            r"(what|who) (are|is) you": "chat",
            r"(how|what) (does|can) (this|the) (chat|assistant) (do|help with)": "chat",
            r"(tell me|explain) (about|what) (you|this chat) (can do|are)": "chat",

            # Help and support
            r"(help|support|assistance)": "help",
            r"(how|where) (can|to) (i|get) help": "help",
            r"(where|how) (can|do) (i|to) (find|get) (more|additional) (help|information)": "help",

            # Getting started
            r"(how|where) (can|do) (i|to) get started": "getting_started",
            r"(how|where) (should|to|do) (i|we) begin": "getting_started",
            r"(what|how) (are|is) the (first|initial) steps": "getting_started",
            r"(i am|i'm) (new|beginner)": "getting_started",
            r"(how|where) (to|do i|can i) (start|use) (this|the) (app|application|system)": "getting_started",
            r"(guide|tutorial) (for|on) (using|getting started)": "getting_started",

            # Feedback
            r"(feedback|suggest|suggestion)": "feedback",
            r"(report|bug|issue)": "feedback",
            r"(how|where) (can|do) (i|to) (provide|give|send) feedback": "feedback"
        }
