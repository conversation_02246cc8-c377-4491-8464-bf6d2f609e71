from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

class BaseAgent(ABC):
    """Base class for specialized agents"""
    
    def __init__(self, ai_service, text_processor):
        self.ai_service = ai_service
        self.text_processor = text_processor
        
    @abstractmethod
    async def process(self, content: str, context: Dict[str, Any]) -> str:
        """Process content with agent-specific logic"""
        pass

    @abstractmethod
    async def validate(self, result: str) -> bool:
        """Validate agent output"""
        pass