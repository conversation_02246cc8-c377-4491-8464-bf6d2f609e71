class ExtractionAgent(BaseAgent):
    """Agent specialized in extracting relevant information from documents"""

    async def process(self, content: str, context: Dict[str, Any]) -> str:
        result = await self.text_processor.process_text_in_segments(
            model_alias="gpt4omini",
            text=content,
            prompt=self._get_extraction_prompt(context)
        )
        return result

    async def validate(self, result: str) -> bool:
        # Implement validation logic
        return True

    def _get_extraction_prompt(self, context: Dict[str, Any]) -> str:
        # Generate appropriate extraction prompt based on context
        pass