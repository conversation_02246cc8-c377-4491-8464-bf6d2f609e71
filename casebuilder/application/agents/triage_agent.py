"""
TriageAgent for document type detection and classification.

This agent analyzes document content to determine its type and provides
metadata about the document for further processing.
"""

import logging
from typing import Dict, Any
from .base_agent import BaseAgent
from ...core.models.Document import DocumentType

# Configure logging
logger = logging.getLogger(__name__)

class TriageAgent(BaseAgent):
    """
    Agent specialized in analyzing and classifying document content.

    This agent is designed to:
    - Determine document type from content
    - Extract key metadata from documents
    - Identify if documents contain images
    - Provide document classification for routing to appropriate processors
    """
    DEFAULT_TRIAGE_MODEL = "o4-mini"

    async def process(self, content: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process content to determine document type and extract metadata

        Args:
            content: The document content to analyze
            context: Additional context for the triage, including:
                - file_name: Original file name (optional)
                - file_type: File type/extension (optional)
                - contains_images: Whether the document contains images (optional)

        Returns:
            Dictionary containing document classification and metadata:
            {
                "document_type": DocumentType enum value,
                "confidence": float between 0-1,
                "metadata": {
                    "key_entities": List of identified entities,
                    "contains_medical_info": bool,
                    "contains_incident_info": bool,
                    "contains_legal_references": bool,
                    "suggested_processors": List of suggested processor types
                }
            }
        """
        # Process content directly with heuristic parsing
        # No need for model or prompt in this implementation

        # Process the content directly with heuristic parsing
        # Skip the JSON parsing attempt which is causing errors
        try:
            # Get a comprehensive sample of the document content
            sample_content = self._get_content_sample(content)

            # Instead of trying to parse JSON, use our heuristic method directly
            result_lower = sample_content.lower()

            # Initialize variables
            doc_type = DocumentType.OTHER
            contains_medical = False
            contains_incident = False
            contains_multiple_types = False
            confidence = 0.5

            # Check for police report indicators - specific terms that are almost exclusively in police reports
            # These are highly specific terms that strongly indicate a police report
            police_exclusive_keywords = ["police report", "incident report", "accident report", "officer",
                                       "badge number", "law enforcement", "citation", "patrol car",
                                       "police department", "traffic collision", "traffic accident",
                                       "reporting officer", "case number", "incident number", "police narrative",
                                       "responding officer", "dispatch", "patrol unit", "traffic unit",
                                       "investigating officer", "police investigation", "traffic report",
                                       "police officer", "sheriff", "deputy", "highway patrol", "state trooper"]

            # More general police/accident related terms that might appear in other documents too
            police_general_keywords = ["collision", "traffic", "vehicle", "driver", "passenger",
                                     "license plate", "intersection", "road", "highway", "accident scene",
                                     "crash", "impact", "vehicle damage", "traffic signal", "traffic light",
                                     "speed limit", "lane", "roadway", "traffic violation", "citation"]

            # Calculate weighted score - exclusive keywords count more
            # Use exact phrase matching for more accuracy
            police_exclusive_score = sum(4 for keyword in police_exclusive_keywords if f" {keyword} " in f" {result_lower} ")
            police_general_score = sum(1 for keyword in police_general_keywords if f" {keyword} " in f" {result_lower} ")
            # Add bonus points for very strong police indicators
            strong_police_indicators = ["badge number", "reporting officer", "investigating officer", "police department"]
            police_strong_score = sum(3 for keyword in strong_police_indicators if f" {keyword} " in f" {result_lower} ")
            police_score = police_exclusive_score + police_general_score + police_strong_score

            # Check for medical record indicators - specific medical terminology
            # These are highly specific terms that strongly indicate a medical record
            medical_exclusive_keywords = ["medical record", "patient", "diagnosis", "prognosis", "physician",
                                        "hospital", "clinic", "prescription", "examination", "medical history",
                                        "chart notes", "soap notes", "assessment", "treatment plan", "icd", "cpt",
                                        "medical provider", "healthcare", "medical examination", "vitals",
                                        "patient name", "date of birth", "medical facility", "attending physician",
                                        "chief complaint", "medical assessment", "discharge summary", "medical report",
                                        "clinical findings", "medical diagnosis", "patient history", "medical chart",
                                        "doctor's notes", "nurse", "medical center", "health center", "medical office"]

            # More general medical terms that might appear in other documents too
            medical_general_keywords = ["treatment", "doctor", "medication", "therapy",
                                      "surgery", "symptoms", "recovery", "follow-up", "specialist",
                                      "physical therapy", "rehabilitation", "medical care",
                                      "prescription", "dosage", "medical procedure", "laboratory",
                                      "x-ray", "mri", "ct scan", "blood test", "outpatient", "inpatient"]

            # Terms that might appear in both police reports and medical records
            # These should be excluded from scoring to avoid false positives
            common_terms = ["injury", "pain", "accident", "emergency", "hospital visit",
                           "hospital", "ambulance", "paramedic", "ems"]

            # Calculate weighted score - exclusive keywords count more
            # Use exact phrase matching for more accuracy
            medical_exclusive_score = sum(4 for keyword in medical_exclusive_keywords if f" {keyword} " in f" {result_lower} ")
            medical_general_score = sum(1 for keyword in medical_general_keywords if f" {keyword} " in f" {result_lower} ")
            # Add bonus points for very strong medical indicators
            strong_medical_indicators = ["chart notes", "soap notes", "medical diagnosis", "attending physician", "icd", "cpt"]
            medical_strong_score = sum(3 for keyword in strong_medical_indicators if f" {keyword} " in f" {result_lower} ")
            # Subtract common terms that appear in both document types
            common_terms_score = sum(1 for keyword in common_terms if f" {keyword} " in f" {result_lower} ")
            medical_score = medical_exclusive_score + medical_general_score + medical_strong_score - common_terms_score

            # Check for billing statement indicators
            billing_keywords = ["bill", "invoice", "charge", "payment", "amount due", "balance",
                               "fee", "cost", "service date", "procedure code", "billing", "statement",
                               "account number", "insurance claim", "payment due", "total amount",
                               "billing date", "service description", "amount charged", "payment method",
                               "medical bill", "healthcare bill", "hospital bill", "insurance payment",
                               "medical charges", "patient responsibility", "insurance coverage"]

            # Use exact phrase matching for more accuracy
            billing_score = sum(2 for keyword in billing_keywords if f" {keyword} " in f" {result_lower} ")

            # Add bonus points for very strong billing indicators
            strong_billing_indicators = ["amount due", "total amount", "insurance claim", "procedure code", "medical bill"]
            billing_strong_score = sum(3 for keyword in strong_billing_indicators if f" {keyword} " in f" {result_lower} ")

            billing_score = billing_score + billing_strong_score

            # Determine document type based on scores and thresholds
            # Use higher thresholds for more confident classification
            logger.info(f"Document scores - Police: {police_score}, Medical: {medical_score}, Billing: {billing_score}")

            # Check if the document name provides strong hints
            file_name_lower = context.get('file_name', '').lower()

            # Check for strong indicators in filename - use more precise matching
            is_likely_police = any(term in file_name_lower for term in ["police", "incident", "accident", "collision", "traffic", "crash"])
            is_likely_medical = any(term in file_name_lower for term in ["medical", "health", "doctor", "hospital", "clinic", "patient", "treatment"])
            is_likely_billing = any(term in file_name_lower for term in ["bill", "invoice", "payment", "statement", "charges"])

            # Check for exhibits or combined document indicators in filename
            # Be more conservative about what constitutes a combined document
            combined_indicators = ["exhibit", "combined", "compilation", "multiple", "both", "all documents",
                                  "police and medical", "medical and police", "records and report"]
            is_likely_combined = any(term in file_name_lower for term in combined_indicators)

            # Define thresholds for document type detection
            # For longer documents, we need to adjust thresholds based on document length
            document_length = len(result_lower)
            length_factor = min(1.0, max(0.5, document_length / 10000))  # Adjust factor based on document length

            # Base thresholds - increase thresholds to be more conservative
            police_threshold = int(7 * length_factor)  # Higher threshold for police reports
            medical_threshold = int(8 * length_factor)  # Higher threshold for medical records
            billing_threshold = int(7 * length_factor)  # Higher threshold for billing statements

            # Lower thresholds if filename suggests a specific type, but still keep them higher than before
            if is_likely_police:
                police_threshold = max(4, int(5 * length_factor))  # Threshold for likely police reports
            if is_likely_medical:
                medical_threshold = max(5, int(6 * length_factor))  # Threshold for likely medical records
            if is_likely_billing:
                billing_threshold = max(4, int(5 * length_factor))  # Threshold for likely billing statements

            # Ensure minimum thresholds
            police_threshold = max(2, police_threshold)
            medical_threshold = max(3, medical_threshold)
            billing_threshold = max(2, billing_threshold)

            # Check if document contains multiple types of information
            has_police_content = police_score >= police_threshold
            has_medical_content = medical_score >= medical_threshold
            has_billing_content = billing_score >= billing_threshold

            # Special case: If the document is clearly a police report (high police score),
            # require a much higher threshold for medical content to avoid false positives
            if police_score >= police_threshold * 2:
                # For very strong police reports, be extremely conservative about medical content
                has_medical_content = medical_score >= medical_threshold * 2.0
            elif police_score >= police_threshold + 5:
                # For strong police reports, require higher medical threshold
                has_medical_content = medical_score >= medical_threshold + 4

            # Special case: If the document is clearly a medical record (high medical score),
            # require a much higher threshold for police content to avoid false positives
            if medical_score >= medical_threshold * 2:
                # For very strong medical records, be extremely conservative about police content
                has_police_content = police_score >= police_threshold * 2.0
            elif medical_score >= medical_threshold + 5:
                # For strong medical records, require higher police threshold
                has_police_content = police_score >= police_threshold + 4

            # For long documents (like police reports with 11+ pages), if police score is significant,
            # it's likely just a police report even if it mentions some medical terms
            if document_length > 5000 and police_score >= 10:
                has_medical_content = False

            # For long medical documents, if medical score is significant,
            # it's likely just a medical record even if it mentions some accident terms
            if document_length > 5000 and medical_score >= 12:
                has_police_content = False

            # Determine if document contains multiple types
            document_types_present = sum([
                1 if has_police_content else 0,
                1 if has_medical_content else 0,
                1 if has_billing_content else 0
            ])

            # More strict multi-type detection:
            # Only consider it a multi-type document if:
            # 1. More than one type is detected AND
            # 2. The filename suggests it's a combined document OR
            # 3. Both scores are very high (indicating substantial content of both types)
            has_multiple_strong_scores = (has_police_content and has_medical_content and
                                         police_score >= police_threshold * 1.5 and
                                         medical_score >= medical_threshold * 1.5)

            contains_multiple_types = document_types_present > 1 and (is_likely_combined or has_multiple_strong_scores)

            # Special override for any document that's not explicitly marked as combined
            if not is_likely_combined:
                # If one score is significantly higher than the other, it's likely just that type
                if has_police_content and has_medical_content:
                    if police_score > medical_score * 2:
                        has_medical_content = False
                        contains_multiple_types = False
                    elif medical_score > police_score * 2:
                        has_police_content = False
                        contains_multiple_types = False

            # Set flags for content types
            contains_incident = has_police_content or any(keyword in result_lower for keyword in ["incident", "accident", "collision", "crash", "impact"])
            contains_medical = has_medical_content or has_billing_content or any(keyword in result_lower for keyword in ["diagnosis", "treatment", "injury", "pain", "medication"])

            # Make classification decision based on scores, filename hints, and multiple types
            if contains_multiple_types:
                # If multiple types are detected, prioritize based on the highest score
                max_score = max(police_score, medical_score, billing_score)

                if max_score == police_score and has_police_content:
                    doc_type = DocumentType.POLICE_REPORT
                    logger.info("Classified as POLICE_REPORT (multiple types detected, police dominant)")
                elif max_score == medical_score and has_medical_content:
                    doc_type = DocumentType.MEDICAL_RECORD
                    logger.info("Classified as MEDICAL_RECORD (multiple types detected, medical dominant)")
                elif max_score == billing_score and has_billing_content:
                    doc_type = DocumentType.BILLING_STATEMENT
                    logger.info("Classified as BILLING_STATEMENT (multiple types detected, billing dominant)")
                else:
                    doc_type = DocumentType.OTHER
                    logger.info("Classified as OTHER (multiple types detected, no clear dominant type)")
            else:
                # Single type classification
                if has_police_content:
                    doc_type = DocumentType.POLICE_REPORT
                    logger.info("Classified as POLICE_REPORT")
                elif has_medical_content:
                    doc_type = DocumentType.MEDICAL_RECORD
                    logger.info("Classified as MEDICAL_RECORD")
                elif has_billing_content:
                    doc_type = DocumentType.BILLING_STATEMENT
                    logger.info("Classified as BILLING_STATEMENT")
                else:
                    # If no strong classification, use the highest score if it's above a minimum threshold
                    max_score = max(police_score, medical_score, billing_score)
                    if max_score >= 3:
                        if max_score == police_score:
                            doc_type = DocumentType.POLICE_REPORT
                            contains_incident = True
                            logger.info("Classified as POLICE_REPORT (highest score)")
                        elif max_score == medical_score:
                            doc_type = DocumentType.MEDICAL_RECORD
                            contains_medical = True
                            logger.info("Classified as MEDICAL_RECORD (highest score)")
                        elif max_score == billing_score:
                            doc_type = DocumentType.BILLING_STATEMENT
                            contains_medical = True
                            logger.info("Classified as BILLING_STATEMENT (highest score)")
                    else:
                        logger.info("No strong classification found, using OTHER")

            # Calculate confidence based on keyword matches and multiple types
            max_score = max(police_score, medical_score, billing_score)
            base_confidence = min(0.9, 0.4 + (max_score * 0.1))  # Scale confidence based on keyword matches

            # Adjust confidence if multiple types are detected
            if contains_multiple_types:
                # Lower confidence slightly for multiple types
                confidence = max(0.4, base_confidence - 0.1)
                logger.info("Confidence adjusted for multiple document types")
            else:
                confidence = base_confidence

            # Log detailed classification information for debugging
            logger.info(f"Document classification details:")
            logger.info(f"  Document length: {document_length}")
            logger.info(f"  Police score: {police_score}, threshold: {police_threshold}, has_police_content: {has_police_content}")
            logger.info(f"  Medical score: {medical_score}, threshold: {medical_threshold}, has_medical_content: {has_medical_content}")
            logger.info(f"  Billing score: {billing_score}, threshold: {billing_threshold}, has_billing_content: {has_billing_content}")
            logger.info(f"  Is likely combined: {is_likely_combined}, has_multiple_strong_scores: {has_multiple_strong_scores}")
            logger.info(f"  Contains multiple types: {contains_multiple_types}")
            logger.info(f"  Final document type: {doc_type}")

            # Create the result dictionary
            parsed_result = {
                "document_type": doc_type,
                "confidence": confidence,
                "metadata": {
                    "key_entities": [],
                    "contains_medical_info": contains_medical,
                    "contains_incident_info": contains_incident,
                    "contains_multiple_types": contains_multiple_types,
                    "contains_legal_references": False,  # Removed legal references detection
                    "document_types_detected": {
                        "police_report": has_police_content,
                        "medical_record": has_medical_content,
                        "billing_statement": has_billing_content
                    },
                    "suggested_processors": []
                }
            }

            # Add suggested processors based on content
            suggested_processors = []
            if contains_incident:
                suggested_processors.append("facts_liability")
            if contains_medical:
                suggested_processors.append("medical_analysis")
                suggested_processors.append("medical_expenses")

            parsed_result["metadata"]["suggested_processors"] = suggested_processors

            return parsed_result

        except Exception as e:
            logger.error(f"Error during document triage: {str(e)}")
            # Return a default result with low confidence
            return {
                "document_type": DocumentType.OTHER,
                "confidence": 0.3,
                "metadata": {
                    "key_entities": [],
                    "contains_medical_info": False,
                    "contains_incident_info": False,
                    "contains_multiple_types": False,
                    "contains_legal_references": False,
                    "suggested_processors": []
                }
            }

    async def validate(self, result: str) -> bool:
        """
        Validate the triage result

        Args:
            result: The triage result to validate

        Returns:
            True if the result is valid, False otherwise
        """
        # Basic validation - check if result is not empty
        if not result or len(result.strip()) < 10:
            return False

        # Check for expected JSON structure or key phrases
        required_keywords = [
            "document_type",
            "medical",
            "police",
            "incident",
            "legal",
            "report",
            "multiple",
            "types"
        ]

        # Count how many required keywords are present
        keywords_present = sum(1 for keyword in required_keywords if keyword.lower() in result.lower())

        # Require at least 3 of the keywords to be present
        return keywords_present >= 3

    def _get_triage_prompt(self, context: Dict[str, Any]) -> str:
        """
        Generate a prompt for document triage based on context

        Args:
            context: Additional context for the triage

        Returns:
            Prompt for the AI service
        """
        file_name = context.get("file_name", "")
        file_type = context.get("file_type", "")
        contains_images = context.get("contains_images", False)

        prompt = """
        You are a document classification specialist for a legal case management system.
        Your task is to analyze the provided document content and determine its type and key characteristics.

        IMPORTANT GUIDELINES FOR DOCUMENT CLASSIFICATION:

        1. Be very conservative when identifying multiple document types. Only classify a document as containing multiple types if there is strong evidence that it contains substantial content from different document categories (e.g., a combined exhibit with both police report and medical record sections).

        2. Police reports often mention injuries and medical treatment briefly - this alone does NOT make it a medical record. Similarly, medical records may mention accident details - this alone does NOT make it a police report.

        3. For a document to be classified as a police report, it should contain official police documentation, officer information, accident details, and investigation findings.

        4. For a document to be classified as a medical record, it should contain detailed medical information, diagnoses, treatment plans, and be authored by medical professionals.

        5. Only set "contains_multiple_types" to true if the document is clearly a compilation of different document types (e.g., exhibits for a legal case that include both police and medical documentation).

        Respond with a JSON object containing the following fields:
        - document_type: One of ["police_report", "medical_record", "billing_statement", "demand_letter", "evidence", "correspondence", "other"]
          (Use the dominant document type if multiple types are detected)
        - confidence: A number between 0 and 1 indicating your confidence in the classification
        - metadata: An object containing:
          - key_entities: Array of important entities mentioned (people, organizations, locations)
          - contains_medical_info: Boolean indicating if the document contains medical information
          - contains_incident_info: Boolean indicating if the document contains incident/accident information
          - contains_legal_references: Boolean indicating if the document contains legal references
          - contains_multiple_types: Boolean indicating if the document contains multiple types of information (be very conservative with this flag)
          - document_types_detected: Object with boolean values for each type detected {"police_report": true/false, "medical_record": true/false, "billing_statement": true/false}
          - suggested_processors: Array of suggested processor types ["facts_liability", "medical_analysis", "damages_analysis", "image_analysis"]

        Base your analysis on the document content, looking for key indicators such as:
        - Document structure and formatting
        - Terminology and jargon specific to document types
        - Presence of specific data fields (dates, reference numbers, medical codes)
        - Named entities and their relationships
        """

        if file_name:
            prompt += f"\nThe original file name is: {file_name}"

        if file_type:
            prompt += f"\nThe file type is: {file_type}"

        if contains_images:
            prompt += "\nThe document contains images which may provide additional context."

        return prompt

    def _get_content_sample(self, content: str) -> str:
        """
        Extract a representative sample from the document content.
        For large documents, this method takes multiple samples throughout the document
        to ensure we capture different sections that might contain different types of information.

        Args:
            content: The full document content

        Returns:
            A sample of the content for classification
        """
        # If content is short, use it all
        if len(content) < 4000:
            return content

        # For larger documents, take multiple samples throughout the document
        # to better capture different sections (medical records, police reports, etc.)

        # Calculate total number of samples based on document size
        doc_length = len(content)

        # For very large documents, take more samples with larger sample sizes
        # This helps ensure we capture enough context for accurate classification
        if doc_length > 50000:
            num_samples = 7  # Beginning, 1/6, 2/6, middle, 4/6, 5/6, end
            sample_size = 1500  # Increased sample size
        elif doc_length > 20000:
            num_samples = 5  # Beginning, 1/4, middle, 3/4, end
            sample_size = 1800  # Increased sample size
        else:
            num_samples = 3  # Beginning, middle, end
            sample_size = 2000  # Increased sample size

        samples = []

        # Always include beginning (first pages often have important metadata)
        # Take a larger sample from the beginning
        beginning_sample_size = min(sample_size * 2, doc_length // 3)
        samples.append(content[:beginning_sample_size])

        # Add intermediate samples
        for i in range(1, num_samples-1):
            position = (doc_length * i) // (num_samples - 1)
            start = max(0, position - (sample_size // 2))
            samples.append(content[start:start + sample_size])

        # Always include end
        end_start = max(0, doc_length - sample_size)
        samples.append(content[end_start:])

        # Join samples with markers
        return "\n\n[...]\n\n".join(samples)

    def _parse_triage_result(self, result: str) -> Dict[str, Any]:
        """
        Parse the triage result into a structured format

        Args:
            result: The raw triage result from the AI service

        Returns:
            Structured triage result
        """
        try:
            # Try to parse as JSON first
            import json
            parsed = json.loads(result)

            # Convert string document_type to enum
            if "document_type" in parsed:
                try:
                    doc_type_str = parsed["document_type"]
                    parsed["document_type"] = DocumentType(doc_type_str)
                except (ValueError, KeyError):
                    parsed["document_type"] = DocumentType.OTHER
            else:
                parsed["document_type"] = DocumentType.OTHER

            # Ensure confidence is present and valid
            if "confidence" not in parsed or not isinstance(parsed["confidence"], (int, float)):
                parsed["confidence"] = 0.5

            # Ensure metadata is present
            if "metadata" not in parsed or not isinstance(parsed["metadata"], dict):
                parsed["metadata"] = {
                    "key_entities": [],
                    "contains_medical_info": False,
                    "contains_incident_info": False,
                    "contains_multiple_types": False,
                    "contains_legal_references": False,
                    "document_types_detected": {
                        "police_report": False,
                        "medical_record": False,
                        "billing_statement": False
                    },
                    "suggested_processors": []
                }

            return parsed

        except json.JSONDecodeError:
            # If JSON parsing fails, extract information using heuristics
            logger.warning("Failed to parse triage result as JSON, using heuristic parsing")

            result_lower = result.lower()

            # Determine document type based on keywords
            doc_type = DocumentType.OTHER
            contains_medical = False
            contains_incident = False
            contains_legal = False
            contains_multiple_types = False

            # Check for police report indicators
            police_keywords = ["police", "incident report", "accident report", "collision", "officer",
                              "badge", "law enforcement", "traffic", "citation", "patrol",
                              "reporting officer", "case number", "incident number", "police narrative",
                              "responding officer", "dispatch", "patrol unit", "traffic unit",
                              "investigating officer", "police investigation", "traffic report"]
            police_score = sum(1 for keyword in police_keywords if keyword in result_lower)

            # Check for medical record indicators
            medical_keywords = ["medical record", "diagnosis", "patient", "doctor",
                               "hospital", "clinic", "prescription", "symptoms", "examination",
                               "chart", "soap", "assessment", "plan", "icd", "cpt",
                               "patient name", "date of birth", "medical facility", "attending physician",
                               "chief complaint", "medical assessment", "discharge summary", "medical report",
                               "clinical findings", "medical diagnosis", "patient history", "medical chart"]
            medical_score = sum(1 for keyword in medical_keywords if keyword in result_lower)

            # Terms that might appear in both police reports and medical records
            # These should be excluded from scoring to avoid false positives
            common_terms = ["injury", "pain", "accident", "emergency", "hospital visit", "treatment"]
            common_terms_score = sum(1 for keyword in common_terms if keyword in result_lower)

            # Adjust scores to account for common terms
            if police_score > medical_score:
                medical_score = max(0, medical_score - common_terms_score)
            else:
                police_score = max(0, police_score - common_terms_score)

            # Check for billing statement indicators
            billing_keywords = ["bill", "invoice", "charge", "payment", "amount", "due",
                               "balance", "fee", "cost", "service date", "procedure code",
                               "billing statement", "account number", "payment due", "total amount",
                               "billing date", "service description", "amount charged", "payment method"]
            billing_score = sum(1 for keyword in billing_keywords if keyword in result_lower)

            # Check for legal document indicators
            legal_keywords = ["legal", "statute", "law", "code", "regulation", "precedent",
                             "case law", "jurisdiction", "court", "plaintiff", "defendant",
                             "liability", "negligence", "tort", "damages", "compensation",
                             "settlement", "claim", "attorney", "counsel", "litigation"]
            legal_score = sum(1 for keyword in legal_keywords if keyword in result_lower)

            # Check for exhibits or combined document indicators
            combined_keywords = ["exhibit", "combined", "all documents", "compilation", "attachments"]
            combined_score = sum(1 for keyword in combined_keywords if keyword in result_lower)

            # Define thresholds for document type detection
            # For longer documents, we need to adjust thresholds based on document length
            document_length = len(result_lower)
            length_factor = min(1.0, max(0.5, document_length / 10000))  # Adjust factor based on document length

            # Base thresholds
            police_threshold = max(2, int(3 * length_factor))  # Adjusted threshold for police reports
            medical_threshold = max(3, int(4 * length_factor))  # Adjusted threshold for medical records
            billing_threshold = max(2, int(3 * length_factor))  # Adjusted threshold for billing statements

            # Check if document contains multiple types of information
            has_police_content = police_score >= police_threshold
            has_medical_content = medical_score >= medical_threshold
            has_billing_content = billing_score >= billing_threshold

            # Special case: If the document is clearly a police report (high police score),
            # require a much higher threshold for medical content to avoid false positives
            if police_score >= police_threshold * 2:
                # For very strong police reports, be extremely conservative about medical content
                has_medical_content = medical_score >= medical_threshold * 1.5
            elif police_score >= police_threshold + 3:
                # For strong police reports, require higher medical threshold
                has_medical_content = medical_score >= medical_threshold + 2

            # Special case: If the document is clearly a medical record (high medical score),
            # require a much higher threshold for police content to avoid false positives
            if medical_score >= medical_threshold * 2:
                # For very strong medical records, be extremely conservative about police content
                has_police_content = police_score >= police_threshold * 1.5
            elif medical_score >= medical_threshold + 3:
                # For strong medical records, require higher police threshold
                has_police_content = police_score >= police_threshold + 2

            # For long documents (like police reports with 11+ pages), if police score is significant,
            # it's likely just a police report even if it mentions some medical terms
            if document_length > 5000 and police_score >= 10:
                has_medical_content = False

            # Determine if document contains multiple types
            document_types_present = sum([
                1 if has_police_content else 0,
                1 if has_medical_content else 0,
                1 if has_billing_content else 0
            ])

            # Simplified multi-type detection: any document with more than one detected type
            contains_multiple_types = document_types_present > 1

            # Special override for long police reports
            if document_length > 5000 and police_score >= 10 and combined_score < 2:
                contains_multiple_types = False

            # Set flags for content types
            contains_incident = has_police_content or any(keyword in result_lower for keyword in ["incident", "accident", "collision", "crash", "impact"])
            contains_medical = has_medical_content or has_billing_content or any(keyword in result_lower for keyword in ["diagnosis", "treatment", "injury", "pain", "medication"])

            # Check for legal content regardless of document type
            if legal_score >= 2:
                contains_legal = True

            # Determine document type based on scores and multiple types
            if contains_multiple_types:
                # If multiple types are detected, prioritize based on the highest score
                max_type_score = max(police_score, medical_score, billing_score)

                if max_type_score == police_score and has_police_content:
                    doc_type = DocumentType.POLICE_REPORT
                    logger.info("Heuristic classified as POLICE_REPORT (multiple types detected, police dominant)")
                elif max_type_score == medical_score and has_medical_content:
                    doc_type = DocumentType.MEDICAL_RECORD
                    logger.info("Heuristic classified as MEDICAL_RECORD (multiple types detected, medical dominant)")
                elif max_type_score == billing_score and has_billing_content:
                    doc_type = DocumentType.BILLING_STATEMENT
                    logger.info("Heuristic classified as BILLING_STATEMENT (multiple types detected, billing dominant)")
                else:
                    doc_type = DocumentType.OTHER
                    logger.info("Heuristic classified as OTHER (multiple types detected, no clear dominant type)")
            else:
                # Single type classification
                if has_police_content:
                    doc_type = DocumentType.POLICE_REPORT
                elif has_medical_content:
                    doc_type = DocumentType.MEDICAL_RECORD
                elif has_billing_content:
                    doc_type = DocumentType.BILLING_STATEMENT

            # Additional checks for content types
            if not contains_legal:
                contains_legal = any(keyword in result_lower for keyword in ["legal", "statute", "law", "regulation", "liability"])

            # Determine suggested processors based on content
            suggested_processors = []
            if contains_incident:
                suggested_processors.append("facts_liability")
            if contains_medical:
                suggested_processors.append("medical_analysis")
                suggested_processors.append("medical_expenses")
            if contains_legal:
                suggested_processors.append("legal_framework")
                suggested_processors.append("case_law")
            if "damage" in result_lower or "injury" in result_lower:
                suggested_processors.append("damages_analysis")

            # Calculate confidence based on keyword matches and multiple types
            max_score = max(police_score, medical_score, billing_score, legal_score)
            base_confidence = min(0.9, 0.4 + (max_score * 0.1))  # Scale confidence based on keyword matches

            # Adjust confidence if multiple types are detected
            if contains_multiple_types:
                # Lower confidence slightly for multiple types
                confidence = max(0.4, base_confidence - 0.1)
            else:
                confidence = base_confidence

            return {
                "document_type": doc_type,
                "confidence": confidence,
                "metadata": {
                    "key_entities": [],
                    "contains_medical_info": contains_medical,
                    "contains_incident_info": contains_incident,
                    "contains_multiple_types": contains_multiple_types,
                    "contains_legal_references": contains_legal,
                    "document_types_detected": {
                        "police_report": has_police_content,
                        "medical_record": has_medical_content,
                        "billing_statement": has_billing_content
                    },
                    "suggested_processors": suggested_processors
                }
            }
