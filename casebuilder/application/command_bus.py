from typing import Dict, Type, TypeVar, Generic, Optional
from .commands.command_base import Command, TResult
from .handlers.command_handler import CommandHandler

class CommandBus:
    """
    A command bus that routes commands to their appropriate handlers.
    Acts as a mediator between commands and their handlers.
    """
    
    def __init__(self):
        self._handlers: Dict[Type[Command], CommandHandler] = {}
    
    def register_handler(self, command_type: Type[Command], handler: CommandHandler) -> None:
        """
        Register a handler for a specific command type.
        
        Args:
            command_type: The type of command to register a handler for
            handler: The handler to register
        """
        self._handlers[command_type] = handler
    
    async def dispatch(self, command: Command[TResult]) -> TResult:
        """
        Dispatch a command to its registered handler.
        
        Args:
            command: The command to dispatch
            
        Returns:
            The result of the command execution
            
        Raises:
            ValueError: If no handler is registered for the command type
        """
        handler = self._handlers.get(type(command))
        
        if handler is None:
            raise ValueError(f"No handler registered for command type {type(command).__name__}")
        
        return await handler.handle(command)
