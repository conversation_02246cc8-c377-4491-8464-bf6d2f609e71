"""
Decorators for command handlers to add cross-cutting concerns
like validation, logging, and error handling.
"""

import functools
import logging
import time
import traceback
from typing import Type, Callable, Any

from .validation import Validator, CommandValidator
from .errors import ApplicationError, ValidationError

# Set up logging
logger = logging.getLogger(__name__)

def validate_command(validator: Callable[[Any], None] = None):
    """
    Decorator to validate a command before handling it.
    
    Args:
        validator: Optional specific validator function for the command
        
    Returns:
        A decorator function
    """
    def decorator(handler_method):
        @functools.wraps(handler_method)
        async def wrapper(self, command):
            # Choose the appropriate validator
            # If a specific validator is provided, use it
            # Otherwise, use generic command validation
            if validator:
                validator(command)
            else:
                Validator.validate_command(command)
            
            # If validation passes, proceed with handling the command
            return await handler_method(self, command)
        
        return wrapper
    
    return decorator

def log_command_execution(handler_method):
    """
    Decorator to log command execution details.
    
    Args:
        handler_method: The handler method to decorate
        
    Returns:
        A decorated handler method
    """
    @functools.wraps(handler_method)
    async def wrapper(self, command):
        command_name = command.__class__.__name__
        command_id = getattr(command, 'id', None) or id(command)
        
        start_time = time.time()
        
        logger.info(f"Executing command {command_name} (ID: {command_id})")
        
        try:
            result = await handler_method(self, command)
            
            execution_time = time.time() - start_time
            logger.info(
                f"Command {command_name} (ID: {command_id}) completed successfully "
                f"in {execution_time:.2f}s"
            )
            
            return result
            
        except Exception as ex:
            execution_time = time.time() - start_time
            logger.error(
                f"Command {command_name} (ID: {command_id}) failed "
                f"after {execution_time:.2f}s: {str(ex)}"
            )
            
            # Re-raise the exception for higher-level handling
            raise
    
    return wrapper

def handle_errors(error_handlers=None):
    """
    Decorator to handle errors that occur during command execution.
    
    Args:
        error_handlers: Optional dictionary mapping exception types to handler functions
        
    Returns:
        A decorator function
    """
    if error_handlers is None:
        error_handlers = {}
    
    def decorator(handler_method):
        @functools.wraps(handler_method)
        async def wrapper(self, command):
            try:
                return await handler_method(self, command)
                
            except ValidationError as ex:
                logger.warning(f"Validation error: {str(ex)}")
                
                handler = error_handlers.get(ValidationError)
                if handler:
                    return handler(ex, command)
                raise
                
            except ApplicationError as ex:
                logger.error(f"Application error: {str(ex)}")
                
                # Check for a specific handler for this error type
                handler = error_handlers.get(type(ex))
                if handler:
                    return handler(ex, command)
                
                # Check for a general ApplicationError handler
                handler = error_handlers.get(ApplicationError)
                if handler:
                    return handler(ex, command)
                
                raise
                
            except Exception as ex:
                logger.error(
                    f"Unhandled exception in command handler: {str(ex)}\n"
                    f"{traceback.format_exc()}"
                )
                
                handler = error_handlers.get(Exception)
                if handler:
                    return handler(ex, command)
                
                raise
        
        return wrapper
    
    return decorator

def with_transaction(unit_of_work_provider):
    """
    Decorator to execute a command handler within a transaction.
    
    Args:
        unit_of_work_provider: Function that provides a unit of work
        
    Returns:
        A decorator function
    """
    def decorator(handler_method):
        @functools.wraps(handler_method)
        async def wrapper(self, command):
            async with unit_of_work_provider() as uow:
                try:
                    result = await handler_method(self, command)
                    await uow.commit()
                    return result
                except Exception:
                    await uow.rollback()
                    raise
        
        return wrapper
    
    return decorator