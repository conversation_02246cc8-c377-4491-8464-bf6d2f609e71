"""
Commands for the CaseBuilder Assistant.

This module defines the command structure for interacting with the Assistant<PERSON>gent.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from ..commands.command_base import Command

@dataclass
class AssistantQueryCommand(Command[str]):
    """
    Command for querying the CaseBuilder Assistant.
    
    Attributes:
        query_text: The user's query text
        chat_history: Optional list of previous chat messages
        app_state: Optional dictionary containing current application state
        user_info: Optional dictionary containing user information
    """
    query_text: str
    chat_history: Optional[List[Dict[str, Any]]] = None
    app_state: Optional[Dict[str, Any]] = None
    user_info: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize default values for optional fields."""
        if self.chat_history is None:
            self.chat_history = []
        if self.app_state is None:
            self.app_state = {}
        if self.user_info is None:
            self.user_info = {}
