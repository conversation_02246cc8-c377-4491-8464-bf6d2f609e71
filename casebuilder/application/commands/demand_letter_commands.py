from dataclasses import dataclass
from typing import Optional, Dict, Any, List

from .command_base import Command

@dataclass
class GeneralDamagesAnalysisCommand(Command[str]):
    """
    Command to generate a general damages analysis from case information.
    Returns a general damages analysis as a formatted string.
    """
    document_text: str
    client_name: Optional[str] = None
    additional_notes: Optional[str] = None
    medical_analysis: Optional[str] = None
    medical_expenses: Optional[str] = None
    future_expenses: Optional[str] = None

@dataclass
class ImpactOnLifestyleAnalysisCommand(Command[str]):
    """
    Command to analyze the impact on lifestyle from case information.
    Returns a lifestyle impact analysis as a formatted string.
    """
    document_text: str
    client_name: Optional[str] = None
    additional_notes: Optional[str] = None
    medical_analysis: Optional[str] = None

@dataclass
class GenerateDemandLetterCommand(Command[str]):
    """
    Command to generate a complete demand letter based on all case information.
    Returns a complete demand letter as a formatted string.
    """
    # Required components
    client_name: str
    selected_state: str
    liability_type: str

    # Optional components with default values
    demand_amount: str = "Policy Limit"
    due_date: Optional[str] = None
    loss_of_income: Optional[str] = None
    liability_accepted: bool = False
    liability_percentage: int = 0
    um_amount: Optional[str] = None
    additional_notes: Optional[str] = None

    # Analysis components
    facts_and_liability: Optional[str] = None
    medical_analysis: Optional[str] = None
    current_medical_expenses: Optional[str] = None
    future_medical_expenses: Optional[str] = None
    general_damages: Optional[str] = None
    impact_on_lifestyle: Optional[str] = None
    legal_framework: Optional[str] = None

    # Image analysis components
    image_analysis_result: Optional[str] = None
    accident_scene_analysis: Optional[str] = None
    property_damage_analysis: Optional[str] = None

    # Tone and style preferences
    tone: str = "firm"  # Options: firm, neutral, conciliatory, aggressive
    emphasis: Optional[Dict[str, int]] = None  # Keys: medical_damages, quality_of_life, economic_losses, liability
    
    # New customization options
    length: str = "standard"  # Options: basic, standard, large

    # Raw document text (used if any analysis components are missing)
    document_text: Optional[str] = None
