from dataclasses import dataclass
from typing import Optional, Dict, Any

from .command_base import Command

@dataclass
class FactsOfLossCommand(Command[str]):
    """
    Command to generate a facts of loss analysis from police report or incident documents.
    Returns a narrative of the facts of loss as a string.
    """
    document_text: str
    client_name: Optional[str] = None
    additional_notes: Optional[str] = None
    summary_only: Optional[bool] = False
    detail_level: str = "facts_liability"  # Options: brief, facts_liability, comprehensive
    style: str = "narrative"  # Options: chronological, narrative, tabular
    emphasis: Optional[Dict[str, int]] = None  # Emphasis on different aspects
    include_witness: bool = False  # Whether to include witness statements

@dataclass
class DetermineLiabilityCommand(Command[str]):
    """
    Command to analyze liability based on provided documents.
    Returns a liability analysis as a string.
    """
    document_text: str
    client_name: Optional[str] = None
    additional_notes: Optional[str] = None
    in_depth: Optional[bool] = False
    detail_level: str = "facts_liability"  # Options: brief, facts_liability, comprehensive
    style: str = "narrative"  # Options: chronological, narrative, tabular
    emphasis: Optional[Dict[str, int]] = None  # Emphasis on different aspects
    include_witness: bool = False  # Whether to include witness statements

@dataclass
class FactsAndLiabilityDemandCommand(Command[str]):
    """
    Command to generate a facts and liability narrative suitable for a demand letter.
    Returns a formatted facts and liability section as a string.
    """
    document_text: str
    client_name: Optional[str] = None
    additional_notes: Optional[str] = None
    liability_type: Optional[str] = None
    # Optional existing facts and liability analysis to use as a base
    existing_facts_and_liability: Optional[str] = None