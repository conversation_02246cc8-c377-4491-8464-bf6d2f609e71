from dataclasses import dataclass
from typing import Optional, List, Dict, Any

from .command_base import Command

@dataclass
class DetailedMedicalAnalysisCommand(Command[str]):
    """
    Command to generate a detailed medical analysis from provided medical documents.
    Returns the analysis as a string.
    """
    document_text: str
    client_name: Optional[str] = None
    additional_notes: Optional[str] = None
    style: str = "narrative"  # Options: narrative, chronological, bulleted, tabular
    detail_level: str = "moderate"  # Options: brief, moderate, comprehensive
    include_icd: bool = False  # Include ICD diagnosis codes in analysis

@dataclass
class MedicalExpensesAnalysisCommand(Command[str]):
    """
    Command to identify and compile medical expenses from provided documents.
    Returns the expenses analysis as a string with a table format.
    """
    document_text: str
    client_name: Optional[str] = None
    additional_notes: Optional[str] = None
    style: str = "tabular"       # Presentation style: narrative, chronological, bulleted, tabular
    detail_level: str = "moderate"  # Options: brief, moderate, comprehensive
    include_icd: bool = False       # Include ICD diagnosis codes in analysis

@dataclass
class FutureMedicalExpensesCommand(Command[str]):
    """
    Command to identify future medical expenses based on the medical records.
    Returns the future expenses projection as a string with a table format.
    """
    document_text: str
    client_name: Optional[str] = None
    additional_notes: Optional[str] = None
    existing_medical_analysis: Optional[str] = None
    style: str = "tabular"       # Presentation style: narrative, chronological, bulleted, tabular
    detail_level: str = "moderate"  # Options: brief, moderate, comprehensive
    include_icd: bool = False       # Include ICD diagnosis codes in analysis
