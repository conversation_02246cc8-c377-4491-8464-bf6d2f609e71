"""
Commands for document triage and classification.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from .command_base import Command

@dataclass
class TriageDocumentCommand(Command[Dict[str, Any]]):
    """
    Command to triage and classify a document.
    
    Attributes:
        document_text: The text content of the document
        file_name: Original file name (optional)
        file_type: File type/extension (optional)
        contains_images: Whether the document contains images (optional)
    """
    document_text: str
    file_name: Optional[str] = None
    file_type: Optional[str] = None
    contains_images: bool = False
    
    def validate(self) -> None:
        """
        Validate the command.
        
        Raises:
            ValidationError: If the command is invalid
        """
        if not self.document_text or not isinstance(self.document_text, str):
            from ...application.errors import ValidationError
            raise ValidationError("Document text is required", "document_text")
