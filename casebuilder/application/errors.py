"""
Application error definitions for CaseBuilder.AI.
Provides a hierarchy of application-specific exceptions.
"""

class ApplicationError(Exception):
    """Base class for all application errors."""
    
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)

class ValidationError(ApplicationError):
    """Error raised when input validation fails."""
    
    def __init__(self, message: str, field: str = None):
        self.field = field
        formatted_message = f"Validation error: {message}"
        if field:
            formatted_message = f"Validation error in '{field}': {message}"
        super().__init__(formatted_message)

class NotFoundError(ApplicationError):
    """Error raised when a requested resource is not found."""
    
    def __init__(self, resource_type: str, resource_id: str):
        self.resource_type = resource_type
        self.resource_id = resource_id
        super().__init__(f"{resource_type} with ID '{resource_id}' not found")

class AuthorizationError(ApplicationError):
    """Error raised when a user is not authorized to perform an action."""
    
    def __init__(self, message: str):
        super().__init__(f"Authorization error: {message}")

class ExternalServiceError(ApplicationError):
    """Error raised when an external service (API) call fails."""
    
    def __init__(self, service_name: str, message: str, original_exception=None):
        self.service_name = service_name
        self.original_exception = original_exception
        super().__init__(f"Error in {service_name}: {message}")
        
class RateLimitError(ExternalServiceError):
    """Error raised when an external service rate limit is exceeded."""
    
    def __init__(self, service_name: str, retry_after: int = None):
        self.retry_after = retry_after
        message = f"Rate limit exceeded"
        if retry_after:
            message = f"Rate limit exceeded. Try again in {retry_after} seconds"
        super().__init__(service_name, message)

class TokenBudgetExceededError(ApplicationError):
    """Error raised when a token budget is exceeded."""
    
    def __init__(self, available_tokens: int, required_tokens: int):
        self.available_tokens = available_tokens
        self.required_tokens = required_tokens
        super().__init__(
            f"Token budget exceeded. Available: {available_tokens}, Required: {required_tokens}"
        )

class DocumentProcessingError(ApplicationError):
    """Error raised when document processing fails."""
    
    def __init__(self, document_id: str, message: str):
        self.document_id = document_id
        super().__init__(f"Error processing document {document_id}: {message}")

class ModelProcessingError(ExternalServiceError):
    """Error raised when AI model processing fails."""
    
    def __init__(self, model_name: str, message: str):
        super().__init__(f"AI Model {model_name}", message)