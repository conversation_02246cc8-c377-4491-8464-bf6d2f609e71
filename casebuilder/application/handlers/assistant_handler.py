"""
Handler for the CaseBuilder Assistant.

This module defines the handler for processing assistant queries using the AssistantAgent.
"""

import logging
from typing import Dict, Any, Optional

from ..commands.assistant_commands import Assistant<PERSON><PERSON><PERSON><PERSON>ommand
from ..agents.assistant_agent import AssistantAgent
from ...core.interfaces.ITextProcessorService import ITextProcessorService
from ...core.interfaces.IAIService import IAIService

# Configure logging
logger = logging.getLogger(__name__)

class AssistantQueryHandler:
    """
    Handler for processing assistant queries.
    
    This handler uses the AssistantAgent to process user queries and generate responses.
    It maintains compatibility with predefined answers while providing more dynamic
    responses based on the application code.
    """
    
    def __init__(
        self, 
        ai_service: IAIService,
        text_processor: ITextProcessorService,
        predefined_answers: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the AssistantQueryHandler.
        
        Args:
            ai_service: The AI service to use for processing
            text_processor: The text processor service
            predefined_answers: Optional dictionary of predefined answers
        """
        self.assistant_agent = AssistantAgent(
            ai_service=ai_service,
            text_processor=text_processor,
            predefined_answers=predefined_answers
        )
        
    async def handle(self, command: AssistantQ<PERSON>yCommand) -> str:
        """
        Process the assistant query command.
        
        Args:
            command: The assistant query command
            
        Returns:
            Generated response to the user query
        """
        # Create context from command
        context = {
            "chat_history": command.chat_history,
            "app_state": command.app_state,
            "user_info": command.user_info
        }
        
        # Process the query using the assistant agent
        try:
            result = await self.assistant_agent.process(command.query_text, context)
            
            # Validate the result
            if await self.assistant_agent.validate(result):
                return result
            else:
                logger.warning("Assistant generated an invalid response")
                return self._get_fallback_response()
                
        except Exception as e:
            logger.error(f"Error processing assistant query: {str(e)}")
            return self._get_fallback_response()
            
    def _get_fallback_response(self) -> str:
        """Get a fallback response when processing fails."""
        return "I'm here to help with questions about CaseBuilder. You can ask about document uploads, analysis features, demand letters, or check the FAQ section in the sidebar for more information."
