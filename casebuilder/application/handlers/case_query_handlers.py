from typing import Dict, Any, List, Optional

from ..queries.case_queries import (
    GetCaseAnalysisQuery,
    GetDocumentAnalysisQuery,
    GetAvailablePromptTemplatesQuery,
    GetCaseDocumentsQuery
)
from ..queries.query_base import Query
from .query_handler import QueryHandler
from ...core.models.Document import Document
from ...core.interfaces.ICaseRepository import ICaseRepository
from ...core.interfaces.IDocumentRepository import IDocumentRepository

class GetCaseAnalysisHandler(QueryHandler[Dict[str, Any]]):
    """
    Handler for retrieving the full analysis for a specific case.
    """
    
    def __init__(self, case_repository: ICaseRepository):
        self.case_repository = case_repository
    
    async def handle(self, query: GetCaseAnalysisQuery) -> Dict[str, Any]:
        """
        Process the query to retrieve case analysis.
        
        Args:
            query: The case analysis query with case ID
            
        Returns:
            A dictionary containing all analysis components
            
        Raises:
            ValueError: If the case is not found
        """
        # Retrieve the case by ID
        case = await self.case_repository.get_by_id(query.case_id)
        
        if not case:
            raise ValueError(f"Case with ID {query.case_id} not found")
        
        # Extract and return all analysis components
        return {
            "case_id": case.id,
            "case_name": case.name,
            "facts_and_liability": case.facts_and_liability,
            "medical_analysis": case.medical_analysis,
            "current_medical_expenses": case.medical_expenses,
            "future_medical_expenses": case.future_medical_expenses,
            "general_damages": case.general_damages,
            "impact_on_lifestyle": case.impact_on_lifestyle,
            "legal_framework": case.legal_framework,
            "demand_letter": case.demand_letter
        }

class GetDocumentAnalysisHandler(QueryHandler[Dict[str, Any]]):
    """
    Handler for retrieving the analysis for a specific document.
    """
    
    def __init__(self, document_repository: IDocumentRepository):
        self.document_repository = document_repository
    
    async def handle(self, query: GetDocumentAnalysisQuery) -> Dict[str, Any]:
        """
        Process the query to retrieve document analysis.
        
        Args:
            query: The document analysis query with document ID
            
        Returns:
            A dictionary containing the document analysis
            
        Raises:
            ValueError: If the document is not found
        """
        # Retrieve the document by ID
        document = await self.document_repository.get_by_id(query.document_id)
        
        if not document:
            raise ValueError(f"Document with ID {query.document_id} not found")
        
        # Extract and return the document analysis
        return {
            "document_id": document.id,
            "document_name": document.name,
            "document_type": document.document_type.name if document.document_type else "Unknown",
            "analysis": document.analysis,
            "metadata": document.metadata
        }

class GetAvailablePromptTemplatesHandler(QueryHandler[Dict[str, Dict[str, str]]]):
    """
    Handler for retrieving all available prompt templates.
    This handler implements the prompts logic from case_builder.py
    but provides it through a query interface.
    """
    
    def __init__(self):
        # This handler doesn't need any repository dependencies
        # as the prompt templates are defined in the code
        pass
    
    async def handle(self, query: GetAvailablePromptTemplatesQuery) -> Dict[str, Dict[str, str]]:
        """
        Process the query to retrieve all available prompt templates.
        
        Args:
            query: The prompt templates query
            
        Returns:
            A dictionary mapping task names to their templates
        """
        # Return a simplified version of the prompts dictionary from case_builder.py
        # In a real implementation, this could be loaded from a database or configuration file
        return {
            'police_report_summary': {
                'instruction': "You are an AI legal expert specialized in personal injury cases...",
                'summary': "Provide a concise but comprehensive summary of the police report..."
            },
            'facts_of_loss': {
                'instruction': "You are an AI legal expert specializing in personal injury cases...",
                'summary': "Produce a cohesive, fact-based narrative integrating all relevant details..."
            },
            'determine_liability': {
                'instruction': "You are an AI legal expert...",
                'summary': "Provide a concise statement of who is at fault, strictly grounded in the reported facts..."
            }
            # Additional prompts would be included here
        }

class GetCaseDocumentsHandler(QueryHandler[List[Document]]):
    """
    Handler for retrieving all documents associated with a case.
    """
    
    def __init__(self, document_repository: IDocumentRepository):
        self.document_repository = document_repository
    
    async def handle(self, query: GetCaseDocumentsQuery) -> List[Document]:
        """
        Process the query to retrieve case documents.
        
        Args:
            query: The case documents query with case ID
            
        Returns:
            A list of Document objects
            
        Raises:
            ValueError: If the case is not found (No documents returned)
        """
        # Retrieve all documents for the case
        documents = await self.document_repository.get_by_case_id(query.case_id)
        
        if not documents:
            # Not raising an error here since it's valid to have a case with no documents yet
            return []
        
        return documents