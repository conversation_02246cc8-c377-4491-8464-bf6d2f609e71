from abc import ABC, abstractmethod
from typing import Generic, TypeVar
from ..commands.command_base import Command, TResult

class CommandHandler(Generic[TResult], ABC):
    """
    Base command handler interface that all command handlers should implement.
    The generic type parameter TResult specifies the expected result type.
    """
    
    @abstractmethod
    async def handle(self, command: Command[TResult]) -> TResult:
        """
        Handle the given command and return a result of type TResult.
        
        Args:
            command: The command to handle
            
        Returns:
            The result of the command execution
        """
        pass
