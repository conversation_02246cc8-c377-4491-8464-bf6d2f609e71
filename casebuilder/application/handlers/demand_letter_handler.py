from typing import Dict, Any
from ..commands import GenerateDemandLetterCommand
from ..agents.extraction_agent import ExtractionAgent
from ...core.interfaces.text_processor_service import ITextProcessorService
from ..errors import ValidationError

class GenerateDemandLetterHandler:
    """Handler for generating demand letters using specialized agents"""
    
    def __init__(self, text_processor: ITextProcessorService):
        self.text_processor = text_processor
        self.extraction_agent = ExtractionAgent(text_processor)

    async def handle(self, command: GenerateDemandLetterCommand) -> str:
        """
        Process the command using specialized agents for extraction and generation
        
        Args:
            command: The command containing demand letter generation parameters
            
        Returns:
            Generated demand letter text
            
        Raises:
            ValidationError: If extraction or generation fails
        """
        # Extract relevant information
        extracted_info = await self.extraction_agent.process(
            command.document_text,
            {
                "type": "demand_letter",
                "client_name": command.client_name,
                "case_type": command.case_type
            }
        )
        
        # Validate extraction
        if not await self.extraction_agent.validate(extracted_info):
            raise ValidationError("Failed to extract valid information from document")

        # Generate final letter
        generation_prompt = self._get_generation_prompt(command, extracted_info)
        
        result = await self.text_processor.process_text_in_segments(
            model_alias="gpt4",
            text=extracted_info,
            prompt=generation_prompt
        )

        return result
        
    def _get_generation_prompt(self, command: GenerateDemandLetterCommand, extracted_info: str) -> str:
        """Generate the prompt for demand letter generation"""
        return f"""
        Generate a professional demand letter using the following information:
        Client Name: {command.client_name}
        Case Type: {command.case_type}
        
        Extracted Information:
        {extracted_info}
        
        Format as a formal legal demand letter with appropriate structure and tone.
        """