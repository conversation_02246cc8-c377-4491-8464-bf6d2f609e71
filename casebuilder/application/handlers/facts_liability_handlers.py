from typing import Dict, Any, Optional

from ..commands.facts_liability_commands import (
    FactsOfLossCommand,
    DetermineLiabilityCommand,
    FactsAndLiabilityDemandCommand
)
from .command_handler import CommandHandler
from ...core.interfaces.IAIService import IAIService
from ...core.interfaces.ITextProcessorService import ITextProcessorService

class FactsOfLossHandler(CommandHandler[str]):
    """
    Handler for generating facts of loss analysis from police report or incident documents.
    """

    def __init__(self, ai_service: IAIService, text_processor: ITextProcessorService):
        self.ai_service = ai_service
        self.text_processor = text_processor

    async def handle(self, command: FactsOfLossCommand) -> str:
        """
        Process the provided document text to generate a facts of loss analysis.

        Args:
            command: The facts of loss command with document text and optional parameters

        Returns:
            A facts of loss analysis as a formatted string
        """
        # Generate the prompt for analysis
        prompt = self._create_facts_prompt(command)

        # Process text through text processor to handle large documents
        result = await self.text_processor.process_text_in_segments(
            model_alias="gpt4o",  # Using gpt-4o
            text=command.document_text,
            prompt=prompt
        )

        return result

    def _create_facts_prompt(self, command: FactsOfLossCommand) -> str:
        """
        Create the prompt for facts of loss analysis based on the command.

        Args:
            command: The facts of loss command

        Returns:
            A formatted prompt string
        """
        client_name = command.client_name or 'the client'
        additional_notes = command.additional_notes or ''
        summary_only = command.summary_only
        detail_level = command.detail_level
        style = command.style
        include_witness = command.include_witness

        # Get emphasis values with defaults
        emphasis = command.emphasis or {'liability': 3, 'property_damage': 3}
        liability_emphasis = emphasis.get('liability', 3)
        property_damage_emphasis = emphasis.get('property_damage', 3)

        # Create style instructions based on selected style
        style_instructions = {
            "chronological": "Present the analysis as an Event Timeline - structured in chronological order with time-stamped events.",
            "narrative": "Present the analysis as a Narrative-Ready Summary - flowing legal-style summary, suitable for pasting into demand letters."
        }.get(style, "Present the analysis as a Narrative-Ready Summary - flowing legal-style summary, suitable for pasting into demand letters.")

        # Create detail level instructions
        detail_instructions = {
            "brief": "Provide a concise summary of only the most important information about the accident.",
            "facts_liability": "Provide a balanced analysis with essential details about the accident and liability determination.",
            "comprehensive": "Provide an exhaustive analysis with all available details about the accident, parties involved, and liability."
        }.get(detail_level, "Provide a balanced analysis with essential details.")

        # Create emphasis instructions
        liability_emphasis_instruction = ""
        if liability_emphasis > 3:
            liability_emphasis_instruction = f"Place strong emphasis on liability determination and fault analysis (emphasis level: {liability_emphasis}/5)."
        elif liability_emphasis < 3:
            liability_emphasis_instruction = f"Place less emphasis on liability determination and fault analysis (emphasis level: {liability_emphasis}/5)."

        property_damage_emphasis_instruction = ""
        if property_damage_emphasis > 3:
            property_damage_emphasis_instruction = f"Place strong emphasis on property damage details and assessment (emphasis level: {property_damage_emphasis}/5)."
        elif property_damage_emphasis < 3:
            property_damage_emphasis_instruction = f"Place less emphasis on property damage details and assessment (emphasis level: {property_damage_emphasis}/5)."

        # Witness statements instruction
        witness_instruction = "Include detailed analysis of witness statements in the report. IMPORTANT: A witness is defined as any third-party observer who is NOT directly involved in the accident (not the drivers or passengers of the vehicles involved)." if include_witness else "Do not focus on witness statements in the report."

        if summary_only:
            # Prompt for a concise police report summary
            prompt = f"""
                You are an AI legal expert specializing in personal injury cases.
                Your task is to produce a clear, concise summary of the police report for a traffic collision.

                {style_instructions}
                {detail_instructions}
                {liability_emphasis_instruction}
                {property_damage_emphasis_instruction}
                {witness_instruction}

                Format your response as "Traffic Collision Summary" with the following structured sections:

                1. **Date, Time, and Location of the Accident:**
                   - Date: [Extract exact date]
                   - Time: [Extract exact time]
                   - Location: [Extract exact location]

                2. **Parties Involved:**
                   - Party 1 (P-1): [Name, role (driver/passenger), vehicle details]
                     - Injuries: [List any injuries mentioned]
                     - Transported to: [Hospital/medical facility if applicable]
                   - Party 2 (P-2): [Name, role, vehicle details]
                     - Vehicle Condition: [Damage reported]
                     - Insurance: [Insurance company if mentioned]

                3. **Brief Description of How the Accident Occurred:**
                   - [Provide chronological sequence of events]
                   - [Include specific actions of each party]

                4. **Weather and Road Conditions:**
                   - Weather: [Weather conditions]
                   - Road Conditions: [Road surface, visibility, etc.]

                5. **Reported Injuries:**
                   - [List all injuries sustained by any parties]

                6. **Citations Issued:**
                   - [List any citations with specific vehicle codes]

                7. **Officer's Determination of Fault/Liability:**
                   - Officer's Opinion: [Officer's stated conclusion]
                   - Note: The officer's opinion on fault is not a legal determination and may not reflect the actual legal liability in this case.

                **Additional Notes:**

                **Additional Notes (if applicable):**
                {additional_notes}
            """
        else:
            # Standard prompt for detailed facts of loss analysis
            prompt = f"""
                You are an AI legal expert specializing in personal injury cases.
                Your task is to produce a clear, objective narrative of the circumstances
                leading to the traffic collision based solely on the provided police report.

                {style_instructions}
                {detail_instructions}
                {liability_emphasis_instruction}
                {property_damage_emphasis_instruction}
                {witness_instruction}

                1. Analyze the facts in the report and form a concise, fact-based account of how the collision occurred.
                2. **Before finalizing**, review your narrative to ensure it does not include:
                - Witness statements or testimonies that are not explicitly documented in the report.
                  IMPORTANT: A witness is defined as any third-party observer who is NOT directly involved
                  in the accident (not the drivers or passengers of the vehicles involved).
                - Any other detail (e.g., injuries, road conditions) that the report does not mention.
                3. If the report omits a detail (e.g., no witnesses, no injuries), omit that detail in your final narrative.
                4. Provide only the final, corrected version in your response, without showing the revision process.

                The goal is a factual summary suitable for legal professionals,
                strictly reflecting the content of the police report,
                and free of any "invented" information.

                FORMATTING REQUIREMENTS:
                - Use minimal markdown formatting (avoid **, ##, ###, etc.)
                - Keep formatting clean and copy-paste friendly
                - Avoid excessive headers, subheaders, or nested formatting
                - Use simple text with minimal bold formatting only when absolutely necessary

                Additional Notes (if applicable):
                {additional_notes}
            """

        return prompt


class DetermineLiabilityHandler(CommandHandler[str]):
    """
    Handler for analyzing liability based on provided documents.
    """

    def __init__(self, ai_service: IAIService, text_processor: ITextProcessorService):
        self.ai_service = ai_service
        self.text_processor = text_processor

    async def handle(self, command: DetermineLiabilityCommand) -> str:
        """
        Process the provided document text to analyze liability.

        Args:
            command: The determine liability command with document text and optional parameters

        Returns:
            A liability analysis as a formatted string
        """
        # Generate the prompt for analysis
        prompt = self._create_liability_prompt(command)

        # Process text through text processor to handle large documents
        result = await self.text_processor.process_text_in_segments(
            model_alias="gpt4o",  # Using gpt-4o
            text=command.document_text,
            prompt=prompt
        )

        return result

    def _create_liability_prompt(self, command: DetermineLiabilityCommand) -> str:
        """
        Create the prompt for liability analysis based on the command.

        Args:
            command: The determine liability command

        Returns:
            A formatted prompt string
        """
        client_name = command.client_name or 'the client'
        additional_notes = command.additional_notes or ''
        in_depth = command.in_depth
        detail_level = command.detail_level
        style = command.style
        include_witness = command.include_witness

        # Get emphasis values with defaults
        emphasis = command.emphasis or {'liability': 3, 'property_damage': 3}
        liability_emphasis = emphasis.get('liability', 3)
        property_damage_emphasis = emphasis.get('property_damage', 3)

        # Create style instructions based on selected style
        style_instructions = {
            "chronological": "Present the analysis as an Event Timeline - structured in chronological order with time-stamped events.",
            "narrative": "Present the analysis as a Narrative-Ready Summary - flowing legal-style summary, suitable for pasting into demand letters."
        }.get(style, "Present the analysis as a Narrative-Ready Summary - flowing legal-style summary, suitable for pasting into demand letters.")

        # Create detail level instructions
        detail_instructions = {
            "brief": "Provide a concise summary of only the most important information about liability.",
            "facts_liability": "Provide a balanced analysis with essential details about liability determination.",
            "comprehensive": "Provide an exhaustive analysis with all available details about liability, including all relevant laws and regulations."
        }.get(detail_level, "Provide a balanced analysis with essential details.")

        # Create emphasis instructions
        liability_emphasis_instruction = ""
        if liability_emphasis > 3:
            liability_emphasis_instruction = f"Place strong emphasis on liability determination and fault analysis (emphasis level: {liability_emphasis}/5)."
        elif liability_emphasis < 3:
            liability_emphasis_instruction = f"Place less emphasis on liability determination and fault analysis (emphasis level: {liability_emphasis}/5)."

        property_damage_emphasis_instruction = ""
        if property_damage_emphasis > 3:
            property_damage_emphasis_instruction = f"Place strong emphasis on property damage details and how they relate to liability (emphasis level: {property_damage_emphasis}/5)."
        elif property_damage_emphasis < 3:
            property_damage_emphasis_instruction = f"Place less emphasis on property damage details (emphasis level: {property_damage_emphasis}/5)."

        # Witness statements instruction
        witness_instruction = "Include detailed analysis of witness statements in determining liability. IMPORTANT: A witness is defined as any third-party observer who is NOT directly involved in the accident (not the drivers or passengers of the vehicles involved)." if include_witness else "Do not focus on witness statements in your analysis."

        if in_depth:
            # Prompt for in-depth liability analysis
            prompt = f"""
                You are an AI legal expert specializing in traffic accident liability analysis.

                Conduct a comprehensive, in-depth liability analysis of the provided police report for {client_name}'s case.

                {style_instructions}
                {detail_instructions}
                {liability_emphasis_instruction}
                {property_damage_emphasis_instruction}
                {witness_instruction}

                CRITICAL INSTRUCTION: You MUST COMPLETELY IGNORE the officer's opinion on fault or liability. Police officers are not legal experts and their opinions on fault are often incorrect or biased. Your task is to determine liability based SOLELY on the factual evidence and applicable traffic laws.

                IMPORTANT LEGAL PRINCIPLES:
                - In pedestrian-vehicle accidents, drivers have a heightened duty of care toward pedestrians.
                - A pedestrian in a crosswalk with a walk signal has absolute right of way.
                - Drivers must yield to pedestrians in crosswalks, even if the pedestrian entered unexpectedly.
                - Comparative negligence should ONLY be applied when there is CLEAR evidence the pedestrian violated traffic laws.
                - The fact that a collision occurred is itself evidence that the driver failed to maintain proper control of their vehicle.

                Format your response with the following structured sections:

                **Factual Sequence Analysis:** The accident occurred at [location details]. [Provide chronological analysis of events based on factual evidence only. Do not reference or consider the officer's opinion at any point. Apply the correct legal standards based on the type of accident.]

                **Witness Statement Evaluation:** [Critically evaluate witness statements for reliability and what they reveal about fault. IMPORTANT: A witness is defined as any third-party observer who is NOT directly involved in the accident (not the drivers or passengers of the vehicles involved). Only if the witness statements are unreliable, mention this fact.]

                **Traffic Law Application:** Under [applicable jurisdiction] Vehicle Code [cite specific sections], [apply relevant traffic laws to determine which party violated applicable regulations. Be specific about which laws were violated and how they apply to this case. Remember that drivers must maintain control of their vehicle at all times, maintain a safe following distance, yield right of way according to traffic laws, exercise extra caution around pedestrians, bicyclists, and in school zones, and that a driver's duty to avoid a collision supersedes most other considerations.]

                **Physical Evidence Assessment:** The report indicates that [analyze vehicle damage patterns, road conditions, and other physical evidence that indicates fault.]

                **Primary Fault Determination:** [Identify which party bears the primary responsibility for the accident.]

                IMPORTANT: Do NOT assign shared responsibility or comparative negligence unless there is CLEAR AND CONVINCING evidence that both parties violated specific traffic laws. The mere occurrence of an accident is not evidence of shared fault. In cases where one party had the right of way (especially pedestrians in crosswalks), that party should almost never be assigned any portion of fault.

                If you do find clear evidence of shared responsibility, mention this fact without assigning specific percentages.

                **Evidence Strength Assessment:** The evidence supporting [party]'s liability is [strong/moderate/weak]. [Rate the strength of evidence supporting your liability determination. Only if the evidence is weak, mention this fact.]

                **Liability Conclusion:** [Party name] is legally responsible for the accident. [Provide your final determination of liability with clear reasoning, focusing on who is legally responsible for the accident.]

                FORMATTING REQUIREMENTS:
                - Use minimal markdown formatting (avoid **, ##, ###, etc.)
                - Keep formatting clean and copy-paste friendly
                - Avoid excessive headers, subheaders, or nested formatting
                - Use simple text with minimal bold formatting only when absolutely necessary

                Additional Notes (if applicable):
                {additional_notes}
            """
        else:
            # Standard prompt for basic liability analysis
            prompt = f"""
                You are an AI legal expert.
                A traffic collision report is provided with factual details and the officer's opinion of who is at fault.

                {style_instructions}
                {detail_instructions}
                {liability_emphasis_instruction}
                {property_damage_emphasis_instruction}
                {witness_instruction}

                CRITICAL INSTRUCTION: You MUST COMPLETELY IGNORE the officer's opinion on fault or liability. Police officers are not legal experts and their opinions on fault are often incorrect or biased.

                IMPORTANT LEGAL PRINCIPLES:
                - In pedestrian-vehicle accidents, drivers have a heightened duty of care toward pedestrians.
                - A pedestrian in a crosswalk with a walk signal has absolute right of way.
                - Drivers must yield to pedestrians in crosswalks, even if the pedestrian entered unexpectedly.
                - Comparative negligence should ONLY be applied when there is CLEAR evidence the pedestrian violated traffic laws.
                - The fact that a collision occurred is itself evidence that the driver failed to maintain proper control of their vehicle.

                Determine who is liable based SOLELY on the factual evidence (e.g., descriptions of events, witness statements, physical damage) stated in the report and applicable traffic laws.
                Do not reference or consider the officer's opinion at any point in your analysis.
                If the facts are inconclusive or insufficient, state that clearly.

                Format your response with the following structured sections:

                **Facts Analysis:** [Provide a clear account of the accident based on factual evidence from the report. Include date, time, location, and sequence of events. Use only concrete details in the report such as vehicle positions, road conditions, statements from drivers, and statements from witnesses. IMPORTANT: A witness is defined as any third-party observer who is NOT directly involved in the accident (not the drivers or passengers of the vehicles involved).]

                **Traffic Law Application:** [Apply relevant traffic laws to determine which party violated applicable regulations. Be specific about which laws were violated and how they apply to this case.]

                **Liability Determination:** [Identify which party or parties appear to be at fault, relying on the factual events and any documented actions or negligence. Do not include or reference the officer's conclusion at all.]

                IMPORTANT: Do NOT assign shared responsibility or comparative negligence unless there is CLEAR AND CONVINCING evidence that both parties violated specific traffic laws. The mere occurrence of an accident is not evidence of shared fault. In cases where one party had the right of way (especially pedestrians in crosswalks), that party should almost never be assigned any portion of fault.

                **Conclusion:** [Provide a concise statement of who is at fault, strictly grounded in the reported facts. Ignore any direct or implied conclusion from the officer, and do not rely on subjective opinions. If liability cannot be determined from the facts, note that the evidence is inconclusive.]

                FORMATTING REQUIREMENTS:
                - Use minimal markdown formatting (avoid **, ##, ###, etc.)
                - Keep formatting clean and copy-paste friendly
                - Avoid excessive headers, subheaders, or nested formatting
                - Use simple text with minimal bold formatting only when absolutely necessary

                Additional Notes (if applicable):
                {additional_notes}
            """

        return prompt


class FactsAndLiabilityDemandHandler(CommandHandler[str]):
    """
    Handler for generating a facts and liability narrative suitable for a demand letter.
    """

    def __init__(self, ai_service: IAIService, text_processor: ITextProcessorService):
        self.ai_service = ai_service
        self.text_processor = text_processor

    async def handle(self, command: FactsAndLiabilityDemandCommand) -> str:
        """
        Process the provided document text to generate a facts and liability narrative for a demand letter.

        Args:
            command: The facts and liability demand command with document text and optional parameters

        Returns:
            A facts and liability narrative as a formatted string suitable for a demand letter
        """
        # Generate the prompt for analysis
        prompt = self._create_demand_prompt(command)

        # Use existing analysis if provided, otherwise use the document text
        input_text = command.existing_facts_and_liability or command.document_text

        # Process text through text processor to handle large documents
        result = await self.text_processor.process_text_in_segments(
            model_alias="gpt4o",  # Using gpt-4o
            text=input_text,
            prompt=prompt
        )

        return result

    def _create_demand_prompt(self, command: FactsAndLiabilityDemandCommand) -> str:
        """
        Create the prompt for facts and liability demand narrative based on the command.

        Args:
            command: The facts and liability demand command

        Returns:
            A formatted prompt string
        """
        client_name = command.client_name or 'the client'
        additional_notes = command.additional_notes or ''

        # This is a simplified version of the prompt from case_builder.py
        prompt = f"""
            You are an AI legal expert tasked with creating a persuasive narrative for a personal injury demand
            on behalf of {client_name}. The narrative must contain two sections:

            1. **Facts**: Provide a detailed account of the accident from our client's perspective.
            - Include the date, time, location, and the actions of all involved parties
                leading to the collision.
            - If a police report (TCR) is available, incorporate any factual observations
                (e.g., physical evidence, witness statements) but ignore the officer's subjective conclusions.
            - IMPORTANT: A witness is defined as any third-party observer who is NOT directly involved
                in the accident (not the drivers or passengers of the vehicles involved).
            - If no police report is provided, rely on other available documents or common sense
                to infer the sequence of events.

            2. **Liability**: Establish how the other party's negligence or breach of duty
            directly caused the accident and led to {client_name}'s injuries.
            - If relevant, cite laws or regulations evidently violated (e.g., failing to yield, speeding).
            - Emphasize how these violations confirm the other party's fault and support the claim
                for damages and injuries.

            Use actual names or descriptive terms for the parties (e.g., "John Smith," "our client");
            avoid impersonal labels like "Unit 1." Write with a compelling tone that favors our client's position,
            while still grounding the account in the documented facts.

            FORMATTING REQUIREMENTS:
            - Use minimal markdown formatting (avoid **, ##, ###, etc.)
            - Keep formatting clean and copy-paste friendly
            - Avoid excessive headers, subheaders, or nested formatting
            - Use simple text with minimal bold formatting only when absolutely necessary

            User Additional Notes (if any):
            {additional_notes}

            (Please incorporate any relevant points from these notes into your Facts or Liability analysis,
            ensuring they bolster our client's position or clarify disputed details.)
        """

        return prompt