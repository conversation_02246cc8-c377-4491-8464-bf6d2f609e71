from abc import ABC, abstractmethod
from typing import Generic, TypeVar
from ..queries.query_base import Query, TResult

class QueryHandler(Generic[TResult], ABC):
    """
    Base query handler interface that all query handlers should implement.
    The generic type parameter TResult specifies the expected result type.
    
    Query handlers are responsible for processing queries and returning
    the requested data without modifying application state.
    """
    
    @abstractmethod
    async def handle(self, query: Query[TResult]) -> TResult:
        """
        Handle the given query and return a result of type TResult.
        
        Args:
            query: The query to handle
            
        Returns:
            The result of the query execution
        """
        pass