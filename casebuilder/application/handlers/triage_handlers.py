"""
Handlers for document triage and classification commands.
"""

from typing import Dict, Any, Optional

from ..commands.triage_commands import TriageDocumentCommand
from ..commands.command_base import Command
from .command_handler import CommandHandler
from ...core.interfaces.IAIService import IAIService
from ...core.interfaces.ITextProcessorService import ITextProcessorService
from ..agents.triage_agent import TriageAgent

class TriageDocumentHandler(CommandHandler[Dict[str, Any]]):
    """
    Handler for triaging and classifying documents.
    """
    
    def __init__(self, ai_service: IAIService, text_processor: ITextProcessorService):
        self.ai_service = ai_service
        self.text_processor = text_processor
        self.triage_agent = TriageAgent(ai_service, text_processor)
    
    async def handle(self, command: TriageDocumentCommand) -> Dict[str, Any]:
        """
        Process the provided document to determine its type and extract metadata.
        
        Args:
            command: The triage document command with document text and optional parameters
            
        Returns:
            A dictionary containing document classification and metadata
        """
        # Create context for the triage agent
        context = {
            "file_name": command.file_name,
            "file_type": command.file_type,
            "contains_images": command.contains_images
        }
        
        # Process the document using the triage agent
        result = await self.triage_agent.process(command.document_text, context)
        
        return result
