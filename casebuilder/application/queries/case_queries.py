from dataclasses import dataclass
from typing import Optional, List, Dict, Any

from .query_base import Query
from ...core.models.Case import Case
from ...core.models.Document import Document

@dataclass
class GetCaseAnalysisQuery(Query[Dict[str, Any]]):
    """
    Query to retrieve the full analysis for a specific case.
    Returns a dictionary containing all analysis components.
    """
    case_id: str
    
@dataclass
class GetDocumentAnalysisQuery(Query[Dict[str, Any]]):
    """
    Query to retrieve the analysis for a specific document.
    Returns a dictionary containing the document analysis.
    """
    document_id: str
    
@dataclass
class GetAvailablePromptTemplatesQuery(Query[Dict[str, Dict[str, str]]]):
    """
    Query to retrieve all available prompt templates.
    Returns a dictionary mapping task names to their templates.
    """
    pass
    
@dataclass
class GetCaseDocumentsQuery(Query[List[Document]]):
    """
    Query to retrieve all documents associated with a case.
    Returns a list of Document objects.
    """
    case_id: str