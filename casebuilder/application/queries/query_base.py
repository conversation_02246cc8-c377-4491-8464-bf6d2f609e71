from typing import Generic, TypeVar
from dataclasses import dataclass

TResult = TypeVar('TResult')

@dataclass
class Query(Generic[TResult]):
    """
    Base query class that all queries should inherit from.
    The generic type parameter TResult specifies the expected result type.
    
    Queries represent read operations in the application that retrieve data
    but don't modify application state (following CQRS pattern).
    """
    pass