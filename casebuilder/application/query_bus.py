from typing import Dict, Type, TypeVar, Optional
from .queries.query_base import Query, TResult
from .handlers.query_handler import QueryHandler

class QueryBus:
    """
    A query bus that routes queries to their appropriate handlers.
    Acts as a mediator between queries and their handlers.
    
    The query bus implements the Mediator pattern to decouple
    query senders from query handlers, centralizing the dispatching logic.
    """
    
    def __init__(self):
        self._handlers: Dict[Type[Query], QueryHandler] = {}
    
    def register_handler(self, query_type: Type[Query], handler: QueryHandler) -> None:
        """
        Register a handler for a specific query type.
        
        Args:
            query_type: The type of query to register a handler for
            handler: The handler to register
        """
        self._handlers[query_type] = handler
    
    async def dispatch(self, query: Query[TResult]) -> TResult:
        """
        Dispatch a query to its registered handler.
        
        Args:
            query: The query to dispatch
            
        Returns:
            The result of the query execution
            
        Raises:
            ValueError: If no handler is registered for the query type
        """
        handler = self._handlers.get(type(query))
        
        if handler is None:
            raise ValueError(f"No handler registered for query type {type(query).__name__}")
        
        return await handler.handle(query)