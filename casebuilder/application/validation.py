"""
Validation utilities for application commands and queries.
"""

from typing import Callable, Any, Dict, List, Optional, Type
from dataclasses import fields
from .errors import ValidationError

class Validator:
    """
    Utility class for validating command and query inputs.
    """
    
    @staticmethod
    def validate_not_empty(value: Any, field_name: str) -> None:
        """
        Validate that a value is not None or empty.
        
        Args:
            value: The value to validate
            field_name: The name of the field being validated
            
        Raises:
            ValidationError: If the value is None or empty
        """
        if value is None:
            raise ValidationError(f"Value cannot be None", field_name)
        
        if isinstance(value, str) and value.strip() == "":
            raise ValidationError(f"Value cannot be empty", field_name)
        
        if isinstance(value, (list, dict)) and len(value) == 0:
            raise ValidationError(f"Value cannot be empty", field_name)
    
    @staticmethod
    def validate_min_length(value: str, min_length: int, field_name: str) -> None:
        """
        Validate that a string has at least the specified minimum length.
        
        Args:
            value: The string to validate
            min_length: The minimum allowed length
            field_name: The name of the field being validated
            
        Raises:
            ValidationError: If the string is shorter than the minimum length
        """
        if value is None:
            raise ValidationError(f"Value cannot be None", field_name)
        
        if not isinstance(value, str):
            raise ValidationError(f"Value must be a string", field_name)
        
        if len(value) < min_length:
            raise ValidationError(
                f"Value must be at least {min_length} characters long", field_name
            )
    
    @staticmethod
    def validate_max_length(value: str, max_length: int, field_name: str) -> None:
        """
        Validate that a string does not exceed the specified maximum length.
        
        Args:
            value: The string to validate
            max_length: The maximum allowed length
            field_name: The name of the field being validated
            
        Raises:
            ValidationError: If the string is longer than the maximum length
        """
        if value is None:
            return  # Allow None values (use validate_not_empty if needed)
        
        if not isinstance(value, str):
            raise ValidationError(f"Value must be a string", field_name)
        
        if len(value) > max_length:
            raise ValidationError(
                f"Value must not exceed {max_length} characters", field_name
            )
    
    @staticmethod
    def validate_object(obj: Any, validations: Dict[str, List[Callable[[Any, str], None]]]) -> None:
        """
        Validate an object using a dictionary of field validations.
        
        Args:
            obj: The object to validate
            validations: A dictionary mapping field names to lists of validation functions
            
        Raises:
            ValidationError: If any validation fails
        """
        for field_name, validators in validations.items():
            value = getattr(obj, field_name, None)
            
            for validator in validators:
                validator(value, field_name)
    
    @staticmethod
    def validate_command(command: Any) -> None:
        """
        Validate a command based on its structure and any defined validations.
        
        Args:
            command: The command to validate
            
        Raises:
            ValidationError: If the command fails validation
        """
        # Check if the command has its own validate method
        if hasattr(command, 'validate') and callable(command.validate):
            command.validate()
            return
        
        # Get command fields for basic validation
        command_fields = {field.name: field for field in fields(command)}
        
        # Perform basic validation on required fields
        for field_name, field in command_fields.items():
            if not field.default and not field.default_factory and field.type != Optional:
                value = getattr(command, field_name)
                Validator.validate_not_empty(value, field_name)

class CommandValidator:
    """
    Utility class for validating specific command types.
    Provides validation functions for different commands.
    """
    
    @staticmethod
    def validate_medical_analysis_command(command: Any) -> None:
        """
        Validate a medical analysis command.
        
        Args:
            command: The command to validate
            
        Raises:
            ValidationError: If the command fails validation
        """
        validations = {
            "document_text": [
                Validator.validate_not_empty,
                lambda v, f: Validator.validate_min_length(v, 10, f)
            ]
        }
        
        Validator.validate_object(command, validations)
    
    @staticmethod
    def validate_facts_liability_command(command: Any) -> None:
        """
        Validate a facts and liability command.
        
        Args:
            command: The command to validate
            
        Raises:
            ValidationError: If the command fails validation
        """
        validations = {
            "document_text": [
                Validator.validate_not_empty,
                lambda v, f: Validator.validate_min_length(v, 10, f)
            ]
        }
        
        Validator.validate_object(command, validations)
    
    @staticmethod
    def validate_demand_letter_command(command: Any) -> None:
        """
        Validate a demand letter command.
        
        Args:
            command: The command to validate
            
        Raises:
            ValidationError: If the command fails validation
        """
        validations = {
            "client_name": [
                Validator.validate_not_empty,
                lambda v, f: Validator.validate_min_length(v, 2, f)
            ],
            "selected_state": [
                Validator.validate_not_empty
            ],
            "liability_type": [
                Validator.validate_not_empty
            ]
        }
        
        Validator.validate_object(command, validations)