"""
Configuration for the CaseBuilderAI application.
Registers all interfaces and their implementations using dependency injection.
"""

from casebuilder.core.interfaces.IAIService import IAIService
from casebuilder.core.interfaces.IOCRService import IOCRService
from casebuilder.core.interfaces.ITextProcessorService import ITextProcessorService
from casebuilder.core.interfaces.ITokenService import ITokenService

from casebuilder.adapters.ai.openai_adapter import OpenAIAdapter
from casebuilder.adapters.ocr.openai_ocr_adapter import OpenAIOCRAdapter
from casebuilder.core.services.text_processor_service import TextProcessorService
from casebuilder.core.services.token_service import TokenService
from casebuilder.infrastructure.repositories.user_repository import UserRepository

# Import both registration styles for compatibility
from casebuilder.infrastructure.dependency_injection import (
    register, register_instance, get,
    register_service, get_service
)


def configure_services():
    """
    Configure all services for the application using dependency injection.
    This function supports both the type-based and string-based registration styles.
    """
    # Initialize basic services
    ai_service = OpenAIAdapter()
    user_repository = UserRepository()
    
    # Register instances using the type-based API
    register_instance(IAIService, ai_service)
    register_instance(UserRepository, user_repository)
    
    # Register implementations using the type-based API
    register(IOCRService, lambda: OpenAIOCRAdapter())
    register(ITextProcessorService, lambda: TextProcessorService(get(IAIService)))
    register(ITokenService, lambda: TokenService(get(UserRepository)))
    
    # Register the same services using the string-based API for new code
    register_service("IAIService", ai_service)
    register_service("UserRepository", user_repository)
    register_service("IOCRService", get(IOCRService))
    register_service("ITextProcessorService", get(ITextProcessorService))
    register_service("ITokenService", get(ITokenService))
    
    # Additional services can be registered here
    
    return {
        'ai_service': ai_service,
        'user_repository': user_repository,
        'ocr_service': get(IOCRService),
        'text_processor_service': get(ITextProcessorService),
        'token_service': get(ITokenService),
    }