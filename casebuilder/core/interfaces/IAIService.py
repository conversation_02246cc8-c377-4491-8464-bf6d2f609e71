from abc import ABC, abstractmethod
from typing import Optional


class IAIService(ABC):
    """
    Interface for AI language model services.
    Defines standard methods for interacting with various AI models.
    """
    
    @abstractmethod
    async def process_text(
        self,
        model_alias: str,
        system_prompt: str,
        user_content: str,
        max_tokens: Optional[int] = None
    ) -> str:
        """
        Process text using the specified AI model.
        
        Args:
            model_alias: Identifier for the specific model to use
            system_prompt: Text that serves as system instruction or context
            user_content: The main text to be processed by the AI
            max_tokens: Maximum number of tokens for the response
            
        Returns:
            Generated text response from the AI model
        """
        pass
    
    @abstractmethod
    async def process_image(
        self,
        image_data: bytes,
        instruction: str,
        model_alias: Optional[str] = None,
        max_tokens: Optional[int] = None
    ) -> str:
        """
        Process an image using the AI model.
        
        Args:
            image_data: Binary image data
            instruction: Text instruction describing what to do with the image
            model_alias: Identifier for the specific model to use
            max_tokens: Maximum number of tokens for the response
            
        Returns:
            Generated text response based on the image analysis
        """
        pass