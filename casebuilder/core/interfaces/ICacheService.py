"""
Interface for cache services.
"""
from abc import ABC, abstractmethod
from typing import Any, Optional


class ICacheService(ABC):
    """
    Interface for cache services.
    Defines methods for storing and retrieving cached data.
    """
    
    @abstractmethod
    async def get(self, key: str, cache_type: str) -> Optional[Any]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            cache_type: Type of cache (ocr, analysis, model)
            
        Returns:
            Cached value or None if not found or expired
        """
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, cache_type: str, ttl: Optional[int] = None) -> bool:
        """
        Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
            cache_type: Type of cache (ocr, analysis, model)
            ttl: Time to live in seconds (None for no expiration)
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def invalidate(self, key: str, cache_type: str) -> bool:
        """
        Invalidate a cache entry.
        
        Args:
            key: Cache key
            cache_type: Type of cache (ocr, analysis, model)
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    def _generate_key(self, data: Any, prefix: str = "") -> str:
        """
        Generate a cache key from input data.
        
        Args:
            data: Input data to hash
            prefix: Optional prefix for the key
            
        Returns:
            Cache key string
        """
        pass
