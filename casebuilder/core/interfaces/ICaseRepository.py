"""
Interface for case repository operations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional

from ..models.Case import Case

class ICaseRepository(ABC):
    """
    Interface for case repository operations.
    Defines methods for CRUD operations on Case entities.
    """
    
    @abstractmethod
    async def get_by_id(self, case_id: str) -> Optional[Case]:
        """
        Get a case by its ID.
        
        Args:
            case_id: The ID of the case to retrieve
            
        Returns:
            The case if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def get_by_user_id(self, user_id: str) -> List[Case]:
        """
        Get all cases for a user.
        
        Args:
            user_id: The ID of the user
            
        Returns:
            A list of cases
        """
        pass
    
    @abstractmethod
    async def save(self, case: Case) -> Case:
        """
        Save a case (create or update).
        
        Args:
            case: The case to save
            
        Returns:
            The saved case with updated ID if it was created
        """
        pass
    
    @abstractmethod
    async def delete(self, case_id: str) -> bool:
        """
        Delete a case by its ID.
        
        Args:
            case_id: The ID of the case to delete
            
        Returns:
            True if the case was deleted, False otherwise
        """
        pass