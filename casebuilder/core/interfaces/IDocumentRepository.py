"""
Interface for document repository operations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional

from ..models.Document import Document

class IDocumentRepository(ABC):
    """
    Interface for document repository operations.
    Defines methods for CRUD operations on Document entities.
    """
    
    @abstractmethod
    async def get_by_id(self, document_id: str) -> Optional[Document]:
        """
        Get a document by its ID.
        
        Args:
            document_id: The ID of the document to retrieve
            
        Returns:
            The document if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def get_by_case_id(self, case_id: str) -> List[Document]:
        """
        Get all documents for a case.
        
        Args:
            case_id: The ID of the case
            
        Returns:
            A list of documents
        """
        pass
    
    @abstractmethod
    async def save(self, document: Document) -> Document:
        """
        Save a document (create or update).
        
        Args:
            document: The document to save
            
        Returns:
            The saved document with updated ID if it was created
        """
        pass
    
    @abstractmethod
    async def delete(self, document_id: str) -> bool:
        """
        Delete a document by its ID.
        
        Args:
            document_id: The ID of the document to delete
            
        Returns:
            True if the document was deleted, False otherwise
        """
        pass