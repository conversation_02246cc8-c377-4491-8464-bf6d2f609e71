from abc import ABC, abstractmethod
from typing import Optional, List, BinaryIO


class IOCRService(ABC):
    """
    Interface for OCR services.
    Defines methods for extracting text from PDFs and images.
    """
    
    @abstractmethod
    async def process_pdf(self, pdf_file: BinaryIO) -> str:
        """
        Extract text from a PDF file.
        
        Args:
            pdf_file: A file-like object containing PDF data
            
        Returns:
            Extracted text from the PDF
        """
        pass
    
    @abstractmethod
    async def process_pdf_with_metadata(self, pdf_file: BinaryIO) -> str:
        """
        Extract text from a PDF file with page metadata.
        
        Args:
            pdf_file: A file-like object containing PDF data
            
        Returns:
            Extracted text from the PDF including page references
        """
        pass
    
    @abstractmethod
    async def process_image(self, image_data: bytes, prompt_text: Optional[str] = None) -> str:
        """
        Extract text from an image.
        
        Args:
            image_data: Binary image data
            prompt_text: Optional prompt to guide the extraction
            
        Returns:
            Extracted text from the image
        """
        pass
    
    @abstractmethod
    async def process_multiple_images(self, images: List[bytes], prompt_text: Optional[str] = None) -> str:
        """
        Extract text from multiple images and combine the results.
        
        Args:
            images: List of binary image data
            prompt_text: Optional prompt to guide the extraction
            
        Returns:
            Combined extracted text from all images
        """
        pass