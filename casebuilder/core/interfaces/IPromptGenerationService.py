from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class IPromptGenerationService(ABC):
    """Interface for prompt generation services."""

    @abstractmethod
    def get_prompt(self, task_name: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a prompt for a specific task using the provided context.

        Args:
            task_name: Identifier for the task (e.g., 'facts_of_loss', 'demand_letter').
            context: Dictionary containing context variables needed for the prompt.

        Returns:
            The generated prompt string.

        Raises:
            KeyError: If the task_name is not found.
        """
        pass
