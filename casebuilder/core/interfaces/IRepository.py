from abc import ABC, abstractmethod
from typing import Generic, TypeVar, List, Optional, Any

T = TypeVar('T')


class IRepository(Generic[T], ABC):
    """
    Generic interface for data repositories.
    Defines standard CRUD operations for entities.
    """
    
    @abstractmethod
    async def get_by_id(self, id: Any) -> Optional[T]:
        """
        Retrieve an entity by its ID.
        
        Args:
            id: The unique identifier for the entity
            
        Returns:
            The entity if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def get_all(self) -> List[T]:
        """
        Retrieve all entities.
        
        Returns:
            List of all entities
        """
        pass
    
    @abstractmethod
    async def add(self, entity: T) -> T:
        """
        Add a new entity.
        
        Args:
            entity: The entity to add
            
        Returns:
            The added entity with any generated IDs or fields
        """
        pass
    
    @abstractmethod
    async def update(self, entity: T) -> T:
        """
        Update an existing entity.
        
        Args:
            entity: The entity to update
            
        Returns:
            The updated entity
        """
        pass
    
    @abstractmethod
    async def delete(self, id: Any) -> bool:
        """
        Delete an entity by its ID.
        
        Args:
            id: The unique identifier for the entity
            
        Returns:
            True if the entity was deleted, False otherwise
        """
        pass