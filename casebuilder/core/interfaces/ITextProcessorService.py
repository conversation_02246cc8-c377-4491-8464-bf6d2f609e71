from abc import ABC, abstractmethod
from typing import List, Optional


class ITextProcessorService(ABC):
    """
    Interface for text processing services.
    Defines methods for handling and processing large text.
    """
    
    @abstractmethod
    def split_text_into_segments(self, text: str, max_tokens: int = 8000) -> List[str]:
        """
        Split a large text into smaller segments based on token count.
        
        Args:
            text: The text to split
            max_tokens: Maximum number of tokens per segment
            
        Returns:
            List of text segments
        """
        pass
    
    @abstractmethod
    async def process_text_in_segments(
        self,
        model_alias: str,
        text: str,
        prompt: str,
        max_completion_tokens: Optional[int] = None
    ) -> str:
        """
        Process a large text by splitting it into manageable segments,
        processing each segment with an AI model, and combining the results.
        
        Args:
            model_alias: Identifier for the AI model to use
            text: The text to process
            prompt: The prompt or instruction for the AI model
            max_completion_tokens: Maximum tokens for the AI completion
            
        Returns:
            Combined and processed text result
        """
        pass