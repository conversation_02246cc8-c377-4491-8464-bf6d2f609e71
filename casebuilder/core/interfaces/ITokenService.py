from abc import ABC, abstractmethod
from typing import Optional, Tuple


class ITokenService(ABC):
    """
    Interface for token management services.
    Defines methods for checking, using, and refunding tokens.
    """
    
    @abstractmethod
    async def check_available_tokens(self, user_id: str, action_type: str) -> int:
        """
        Check how many tokens a user has available for a specific action.
        
        Args:
            user_id: The identifier for the user
            action_type: The type of action being checked
            
        Returns:
            Number of tokens available for the action
        """
        pass
    
    @abstractmethod
    async def use_tokens(self, user_id: str, action_type: str, token_count: int) -> Tuple[bool, int]:
        """
        Attempt to use tokens for an action.
        
        Args:
            user_id: The identifier for the user
            action_type: The type of action being performed
            token_count: The number of tokens to use
            
        Returns:
            Tuple containing:
            - Boolean indicating if tokens were successfully used
            - Remaining token count
        """
        pass
    
    @abstractmethod
    async def refund_tokens(self, user_id: str, action_type: str, token_count: int) -> int:
        """
        Refund tokens back to a user.
        
        Args:
            user_id: The identifier for the user
            action_type: The type of action being refunded
            token_count: The number of tokens to refund
            
        Returns:
            New token count after refund
        """
        pass
    
    @abstractmethod
    async def refresh_token_count(self, user_id: str, action_type: str) -> int:
        """
        Refresh and get the current token count for a user.
        
        Args:
            user_id: The identifier for the user
            action_type: The type of action for the tokens
            
        Returns:
            Current token count
        """
        pass