from abc import abstractmethod
from typing import Optional, Dict, Any
from .base_service import IBaseService

class ITextProcessorService(IBaseService):
    @abstractmethod
    async def process_text_in_segments(
        self,
        model_alias: str,
        text: str,
        prompt: str,
        max_tokens: Optional[int] = None
    ) -> str:
        """Process text in segments using the specified model"""
        pass