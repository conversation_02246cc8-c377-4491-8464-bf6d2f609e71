from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class AnalysisType(Enum):
    """Enum for analysis types."""
    POLICE_REPORT_SUMMARY = "Police Report Summary"
    FACTS_OF_LOSS = "Facts of Loss"
    LIABILITY_ANALYSIS = "Liability Analysis"
    FACTS_LIABILITY_NARRATIVE = "Facts & Liability Narrative"
    MEDICAL_ANALYSIS = "Medical Analysis"
    MEDICAL_EXPENSES = "Medical Expenses"
    FUTURE_MEDICAL_EXPENSES = "Future Medical Expenses"
    INJURY_DETAILS = "Injury Details"
    INJURY_ANALYSIS = "Injury Analysis"
    ACCIDENT_SCENE = "Accident Scene Analysis"
    PROPERTY_DAMAGE = "Property Damage Analysis"
    ROAD_CONDITIONS = "Road Conditions Analysis"
    CASE_LAW = "Case Law References"
    GENERAL_DAMAGES = "General Damages"
    LEGAL_FRAMEWORK = "Legal Framework"
    DEMAND_LETTER = "Demand Letter"


@dataclass
class Analysis:
    """
    Model representing an analysis in the system.
    """
    analysis_type: AnalysisType
    case_id: int
    content: str
    
    # Metadata
    analysis_id: Optional[int] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    created_by: Optional[str] = None
    
    # Processing details
    model_used: Optional[str] = None
    processing_time: Optional[float] = None
    
    # Additional data
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MedicalExpense:
    """
    Model representing a medical expense entry.
    """
    provider: str
    treatment_summary: str
    treatment_start_date: Optional[datetime] = None
    treatment_end_date: Optional[datetime] = None
    total_cost: float = 0.0
    page_references: List[int] = field(default_factory=list)
    
    @property
    def treatment_period(self) -> str:
        """Get a formatted treatment period string."""
        if not self.treatment_start_date:
            return "Not specified"
        
        start_str = self.treatment_start_date.strftime("%m/%d/%Y")
        if not self.treatment_end_date or self.treatment_end_date == self.treatment_start_date:
            return start_str
        
        end_str = self.treatment_end_date.strftime("%m/%d/%Y")
        return f"{start_str} – {end_str}"


@dataclass
class FutureMedicalExpense:
    """
    Model representing a future medical expense entry.
    """
    expense_name: str
    frequency: str  # e.g., "Weekly", "Monthly"
    duration: str   # e.g., "6 months", "1 year"
    cost_per_session: float
    total_estimated_cost: float