"""
Case domain model.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from enum import Enum
from datetime import datetime
import uuid

class CaseType(Enum):
    """Enumeration of case types."""
    PERSONAL_INJURY = "personal_injury"
    MEDICAL_MALPRACTICE = "medical_malpractice"
    PRODUCT_LIABILITY = "product_liability"
    WORKERS_COMPENSATION = "workers_compensation"
    OTHER = "other"

class LiabilityType(Enum):
    """Enumeration of liability types."""
    BODILY_INJURY = "bodily_injury"
    PROPERTY_DAMAGE = "property_damage"
    UNINSURED_MOTORIST = "uninsured_motorist"
    UNDER_INSURED_MOTORIST = "under_insured_motorist"
    OTHER = "other"

@dataclass
class Case:
    """
    Represents a legal case in the system.
    Contains all case information and analysis results.
    """
    
    # Basic case information
    user_id: str
    name: str
    type: CaseType
    liability_type: LiabilityType
    client_name: str
    
    # Case analysis fields
    facts_and_liability: Optional[str] = None
    medical_analysis: Optional[str] = None
    medical_expenses: Optional[str] = None
    future_medical_expenses: Optional[str] = None
    general_damages: Optional[str] = None
    impact_on_lifestyle: Optional[str] = None
    legal_framework: Optional[str] = None
    demand_letter: Optional[str] = None
    
    # Case metadata
    demand_amount: str = "Policy Limit"
    loss_of_income: Optional[str] = None
    due_date: Optional[datetime] = None
    liability_accepted: bool = False
    liability_percentage: int = 0
    um_amount: Optional[str] = None
    selected_state: str = ""
    additional_notes: Optional[str] = None
    
    # System fields
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def update_analysis(self, component_name: str, content: str) -> None:
        """
        Update a specific analysis component.
        
        Args:
            component_name: The name of the component to update
            content: The new content for the component
        """
        if hasattr(self, component_name) and component_name not in ('id', 'user_id', 'created_at'):
            setattr(self, component_name, content)
            self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the case to a dictionary.
        
        Returns:
            A dictionary representation of the case
        """
        result = {
            "id": self.id,
            "user_id": self.user_id,
            "name": self.name,
            "type": self.type.value,
            "liability_type": self.liability_type.value,
            "client_name": self.client_name,
            "demand_amount": self.demand_amount,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
        
        # Add optional fields if they exist
        for field in [
            "facts_and_liability", "medical_analysis", "medical_expenses",
            "future_medical_expenses", "general_damages", "impact_on_lifestyle",
            "legal_framework", "demand_letter", "loss_of_income",
            "selected_state", "additional_notes", "um_amount"
        ]:
            value = getattr(self, field)
            if value is not None:
                result[field] = value
        
        # Handle datetime field
        if self.due_date:
            result["due_date"] = self.due_date.isoformat()
        
        # Add boolean fields
        result["liability_accepted"] = self.liability_accepted
        result["liability_percentage"] = self.liability_percentage
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Case':
        """
        Create a case from a dictionary.
        
        Args:
            data: The dictionary data
            
        Returns:
            A new Case instance
        """
        # Convert string type values to enum types
        case_type = CaseType(data.get("type", "personal_injury"))
        liability_type = LiabilityType(data.get("liability_type", "bodily_injury"))
        
        # Convert ISO datetime strings to datetime objects
        created_at = datetime.fromisoformat(data.get("created_at")) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data.get("updated_at")) if data.get("updated_at") else datetime.now()
        due_date = datetime.fromisoformat(data.get("due_date")) if data.get("due_date") else None
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            user_id=data.get("user_id"),
            name=data.get("name"),
            type=case_type,
            liability_type=liability_type,
            client_name=data.get("client_name"),
            facts_and_liability=data.get("facts_and_liability"),
            medical_analysis=data.get("medical_analysis"),
            medical_expenses=data.get("medical_expenses"),
            future_medical_expenses=data.get("future_medical_expenses"),
            general_damages=data.get("general_damages"),
            impact_on_lifestyle=data.get("impact_on_lifestyle"),
            legal_framework=data.get("legal_framework"),
            demand_letter=data.get("demand_letter"),
            demand_amount=data.get("demand_amount", "Policy Limit"),
            loss_of_income=data.get("loss_of_income"),
            due_date=due_date,
            liability_accepted=data.get("liability_accepted", False),
            liability_percentage=data.get("liability_percentage", 0),
            um_amount=data.get("um_amount"),
            selected_state=data.get("selected_state", ""),
            additional_notes=data.get("additional_notes"),
            created_at=created_at,
            updated_at=updated_at
        )