"""
Document domain model.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from enum import Enum
from datetime import datetime
import uuid

class DocumentType(Enum):
    """Enumeration of document types."""
    POLICE_REPORT = "police_report"
    MEDICAL_RECORD = "medical_record"
    BILLING_STATEMENT = "billing_statement"
    DEMAND_LETTER = "demand_letter"
    EVIDENCE = "evidence"
    CORRESPONDENCE = "correspondence"
    OTHER = "other"

@dataclass
class Document:
    """
    Represents a document in the system.
    Contains document metadata and content.
    """
    
    # Basic document information
    case_id: str
    name: str
    content_text: str
    
    # Document attributes
    document_type: Optional[DocumentType] = None
    analysis: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # System fields
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def update_analysis(self, analysis: str) -> None:
        """
        Update the document analysis.
        
        Args:
            analysis: The new analysis content
        """
        self.analysis = analysis
        self.updated_at = datetime.now()
    
    def add_metadata(self, key: str, value: Any) -> None:
        """
        Add or update metadata.
        
        Args:
            key: Metadata key
            value: Metadata value
        """
        self.metadata[key] = value
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the document to a dictionary.
        
        Returns:
            A dictionary representation of the document
        """
        result = {
            "id": self.id,
            "case_id": self.case_id,
            "name": self.name,
            "document_type": self.document_type.value if self.document_type else None,
            "content_text": self.content_text,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
        
        if self.analysis:
            result["analysis"] = self.analysis
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Document':
        """
        Create a document from a dictionary.
        
        Args:
            data: The dictionary data
            
        Returns:
            A new Document instance
        """
        # Convert string type value to enum type
        document_type = None
        if data.get("document_type"):
            document_type = DocumentType(data.get("document_type"))
        
        # Convert ISO datetime strings to datetime objects
        created_at = datetime.fromisoformat(data.get("created_at")) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data.get("updated_at")) if data.get("updated_at") else datetime.now()
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            case_id=data.get("case_id"),
            name=data.get("name"),
            document_type=document_type,
            content_text=data.get("content_text", ""),
            analysis=data.get("analysis"),
            metadata=data.get("metadata", {}),
            created_at=created_at,
            updated_at=updated_at
        )