from dataclasses import dataclass
from typing import Optional, Dict
from datetime import datetime


@dataclass
class User:
    """
    Model representing a user in the system.
    """
    username: str
    first_name: str
    last_name: Optional[str] = None
    email: Optional[str] = None
    user_id: Optional[int] = None
    last_login: Optional[datetime] = None

    @property
    def full_name(self) -> str:
        """Get the user's full name."""
        if self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name


@dataclass
class TokenInfo:
    """
    Model representing token information for a specific action.
    """
    button_id: int
    username: str
    button_name: str
    click_count: int
    limit_count: int

    @property
    def available_tokens(self) -> int:
        """Get the number of available tokens."""
        return max(0, self.limit_count - self.click_count)

    @property
    def is_depleted(self) -> bool:
        """Check if the tokens are depleted."""
        return self.available_tokens <= 0