"""
Cache service for storing and retrieving processed document results.
"""
import os
import json
import hashlib
import logging
from typing import Dict, Any, Optional, Union
from datetime import datetime, timedelta
import aiofiles
import asyncio

from casebuilder.core.interfaces.ICacheService import ICacheService

class CacheService(ICacheService):
    """
    Service for caching document processing results.

    This service provides:
    - Document text caching (OCR results)
    - Analysis result caching
    - Model response caching

    Cache entries include metadata like creation time and TTL.
    """

    def __init__(self, cache_dir: str = ".cache"):
        """
        Initialize the cache service.

        Args:
            cache_dir: Directory to store cache files
        """
        self.cache_dir = cache_dir
        self.logger = logging.getLogger(__name__)
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self._lock = None  # Initialize lock to None, will create it when needed

        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)

        # Create subdirectories for different cache types
        os.makedirs(os.path.join(cache_dir, "ocr"), exist_ok=True)
        os.makedirs(os.path.join(cache_dir, "analysis"), exist_ok=True)
        os.makedirs(os.path.join(cache_dir, "model"), exist_ok=True)

    async def _get_lock(self):
        """
        Get the asyncio lock, creating it if it doesn't exist.
        This ensures the lock is created within an async context where an event loop exists.

        Returns:
            asyncio.Lock: The lock object
        """
        if self._lock is None:
            self._lock = asyncio.Lock()
        return self._lock

    def _generate_key(self, data: Union[str, bytes], prefix: str = "") -> str:
        """
        Generate a cache key from input data.

        Args:
            data: Input data to hash
            prefix: Optional prefix for the key

        Returns:
            Cache key string
        """
        if isinstance(data, str):
            data_bytes = data.encode('utf-8')
        else:
            data_bytes = data

        hash_obj = hashlib.sha256(data_bytes)
        hash_str = hash_obj.hexdigest()

        if prefix:
            return f"{prefix}_{hash_str}"
        return hash_str

    def _get_cache_path(self, key: str, cache_type: str) -> str:
        """
        Get the file path for a cache key.

        Args:
            key: Cache key
            cache_type: Type of cache (ocr, analysis, model)

        Returns:
            File path for the cache entry
        """
        return os.path.join(self.cache_dir, cache_type, f"{key}.json")

    async def get(self, key: str, cache_type: str) -> Optional[Any]:
        """
        Get a value from the cache.

        Args:
            key: Cache key
            cache_type: Type of cache (ocr, analysis, model)

        Returns:
            Cached value or None if not found or expired
        """
        # Check memory cache first
        memory_key = f"{cache_type}_{key}"
        lock = await self._get_lock()
        async with lock:
            if memory_key in self.memory_cache:
                entry = self.memory_cache[memory_key]
                # Check if entry is expired
                if entry.get("expires_at") and datetime.fromisoformat(entry["expires_at"]) < datetime.now():
                    # Remove expired entry
                    del self.memory_cache[memory_key]
                    return None
                return entry.get("value")

        # Check file cache
        cache_path = self._get_cache_path(key, cache_type)
        try:
            if not os.path.exists(cache_path):
                return None

            async with aiofiles.open(cache_path, 'r') as f:
                content = await f.read()
                entry = json.loads(content)

                # Check if entry is expired
                if entry.get("expires_at") and datetime.fromisoformat(entry["expires_at"]) < datetime.now():
                    # Remove expired entry
                    os.remove(cache_path)
                    return None

                # Add to memory cache
                lock = await self._get_lock()
                async with lock:
                    self.memory_cache[memory_key] = entry

                return entry.get("value")
        except Exception as e:
            self.logger.error(f"Error reading cache: {e}")
            return None

    async def set(self, key: str, value: Any, cache_type: str, ttl: Optional[int] = None) -> bool:
        """
        Set a value in the cache.

        Args:
            key: Cache key
            value: Value to cache
            cache_type: Type of cache (ocr, analysis, model)
            ttl: Time to live in seconds (None for no expiration)

        Returns:
            True if successful, False otherwise
        """
        expires_at = None
        if ttl is not None:
            expires_at = (datetime.now() + timedelta(seconds=ttl)).isoformat()

        entry = {
            "value": value,
            "created_at": datetime.now().isoformat(),
            "expires_at": expires_at
        }

        # Add to memory cache
        memory_key = f"{cache_type}_{key}"
        lock = await self._get_lock()
        async with lock:
            self.memory_cache[memory_key] = entry

        # Write to file cache
        cache_path = self._get_cache_path(key, cache_type)
        try:
            async with aiofiles.open(cache_path, 'w') as f:
                await f.write(json.dumps(entry))
            return True
        except Exception as e:
            self.logger.error(f"Error writing cache: {e}")
            return False

    async def invalidate(self, key: str, cache_type: str) -> bool:
        """
        Invalidate a cache entry.

        Args:
            key: Cache key
            cache_type: Type of cache (ocr, analysis, model)

        Returns:
            True if successful, False otherwise
        """
        # Remove from memory cache
        memory_key = f"{cache_type}_{key}"
        lock = await self._get_lock()
        async with lock:
            if memory_key in self.memory_cache:
                del self.memory_cache[memory_key]

        # Remove from file cache
        cache_path = self._get_cache_path(key, cache_type)
        try:
            if os.path.exists(cache_path):
                os.remove(cache_path)
            return True
        except Exception as e:
            self.logger.error(f"Error invalidating cache: {e}")
            return False
