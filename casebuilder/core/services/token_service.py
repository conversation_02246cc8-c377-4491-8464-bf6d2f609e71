from typing import Tuple, Optional, Dict, Any
import asyncio
import logging
import time

from casebuilder.core.interfaces.ITokenService import ITokenService
from casebuilder.infrastructure.repositories.user_repository import UserRepository


class TokenService(ITokenService):
    """
    Service for managing token usage in the application.
    Implements the ITokenService interface.
    
    This service handles:
    - Checking available tokens for a user
    - Using tokens for actions
    - Refunding tokens
    - Monitoring token usage
    
    It provides thread-safe token operations and compatibility
    with the original token management code.
    """
    
    # Token values for different actions
    TOKEN_VALUES = {
        # Major actions (expensive)
        "compose_demand": 5,        # Demand letter generation
        "comprehensive_analysis": 3, # Full case analysis
        
        # Medium actions
        "medical_analysis": 2,      # Medical record analysis
        "liability_analysis": 2,    # Liability analysis 
        
        # Standard actions (most common)
        "default": 1,               # Default cost for other actions
        "police_report_summary": 1,
        "facts_of_loss": 1,
        "determine_liability": 1,
        "facts_and_liability_narrative": 1,
        "detailed_medical_analysis": 1,
        "medical_expenses": 1,
        "future_medical_expenses": 1,
        "injury_details": 1,
        "describe_injuries": 1,
        "accident_scene": 1,
        "property_damage": 1, 
        "road_conditions": 1,
        "case_law": 1,
        "id_general_damages": 1,
        "legal_framework": 1
    }
    
    def __init__(self, user_repository: UserRepository):
        """
        Initialize the token service.
        
        Args:
            user_repository: Repository for user-related operations
        """
        self.user_repository = user_repository
        self._locks = {}  # Dictionary to hold locks for concurrent token operations
        self.logger = logging.getLogger(__name__)
        self._usage_stats = {}  # Track token usage statistics
    
    def _get_token_value(self, action_type: str) -> int:
        """
        Get the token value for an action.
        
        Args:
            action_type: The type of action
            
        Returns:
            Number of tokens required for the action
        """
        token_value = self.TOKEN_VALUES.get(action_type, self.TOKEN_VALUES["default"])
        self.logger.debug(f"Token value for action '{action_type}': {token_value}")
        return token_value
    
    def _track_usage(self, user_id: str, action_type: str, token_count: int) -> None:
        """
        Track token usage for analytics.
        
        Args:
            user_id: The identifier for the user
            action_type: The type of action 
            token_count: Number of tokens used
        """
        timestamp = time.time()
        
        # Initialize user stats if needed
        if user_id not in self._usage_stats:
            self._usage_stats[user_id] = []
            
        # Add usage record
        self._usage_stats[user_id].append({
            'action_type': action_type,
            'token_count': token_count,
            'timestamp': timestamp
        })
        
        # Limit history size
        if len(self._usage_stats[user_id]) > 1000:
            self._usage_stats[user_id] = self._usage_stats[user_id][-1000:]
    
    def get_usage_statistics(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """
        Get token usage statistics for a user.
        
        Args:
            user_id: The identifier for the user
            days: Number of days to include in statistics
            
        Returns:
            Dictionary of usage statistics
        """
        if user_id not in self._usage_stats:
            return {
                'total_tokens_used': 0,
                'actions_performed': 0,
                'action_breakdown': {}
            }
            
        # Calculate cutoff time
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        
        # Filter records by time
        recent_usage = [
            record for record in self._usage_stats[user_id]
            if record['timestamp'] >= cutoff_time
        ]
        
        # Calculate statistics
        total_tokens = sum(record['token_count'] for record in recent_usage)
        action_counts = {}
        
        for record in recent_usage:
            action = record['action_type']
            if action not in action_counts:
                action_counts[action] = {
                    'count': 0,
                    'tokens': 0
                }
            action_counts[action]['count'] += 1
            action_counts[action]['tokens'] += record['token_count']
        
        return {
            'total_tokens_used': total_tokens,
            'actions_performed': len(recent_usage),
            'action_breakdown': action_counts
        }
    
    async def check_available_tokens(self, user_id: str, action_type: str) -> int:
        """
        Check how many tokens a user has available for a specific action.
        
        Args:
            user_id: The identifier for the user
            action_type: The type of action being checked
            
        Returns:
            Number of tokens available
            
        Raises:
            RuntimeError: If token check fails
        """
        try:
            # For compatibility with original code
            button_name = action_type if action_type == "compose_demand" else "compose_demand"
            
            # Get token info
            token_info = await self.user_repository.get_token_info(user_id, button_name)
            
            # If no token info found, return 0
            if not token_info:
                self.logger.warning(f"No token allocation found for user '{user_id}', action '{action_type}'")
                return 0
            
            available = token_info.available_tokens
            self.logger.info(f"User '{user_id}' has {available} tokens available for action '{action_type}'")
            return available
            
        except Exception as e:
            self.logger.error(f"Error checking available tokens: {e}")
            raise RuntimeError(f"Failed to check available tokens: {str(e)}")
    
    async def use_tokens(self, user_id: str, action_type: str, token_count: Optional[int] = None) -> Tuple[bool, int]:
        """
        Attempt to use tokens for an action.
        
        Args:
            user_id: The identifier for the user
            action_type: The type of action being performed
            token_count: The number of tokens to use (defaults to action type's value)
            
        Returns:
            Tuple containing:
            - Boolean indicating if tokens were successfully used
            - Remaining token count
            
        Raises:
            RuntimeError: If token usage fails
        """
        try:
            # Create a lock for this user if it doesn't exist
            if user_id not in self._locks:
                self._locks[user_id] = asyncio.Lock()
            
            # Use the token count from parameters or get default for action type
            tokens_to_use = token_count if token_count is not None else self._get_token_value(action_type)
            
            # For compatibility with original code
            button_name = action_type if action_type == "compose_demand" else "compose_demand"
            
            # Acquire lock to prevent race conditions
            async with self._locks[user_id]:
                # Get token info
                token_info = await self.user_repository.get_token_info(user_id, button_name)
                
                # If no token info found, attempt to create a new allocation
                if not token_info:
                    self.logger.info(f"Creating new token allocation for user '{user_id}'")
                    try:
                        token_info = await self.user_repository.create_token_allocation(user_id, button_name)
                    except Exception as create_error:
                        self.logger.error(f"Failed to create token allocation: {create_error}")
                        return False, 0
                
                # Check if enough tokens available
                if token_info.available_tokens < tokens_to_use:
                    self.logger.warning(
                        f"Insufficient tokens for user '{user_id}', action '{action_type}': " +
                        f"needed {tokens_to_use}, available {token_info.available_tokens}"
                    )
                    return False, token_info.available_tokens
                
                # Calculate new count
                new_count = token_info.click_count + tokens_to_use
                
                # Update token usage
                success = await self.user_repository.update_token_usage(token_info.button_id, new_count)
                
                # Calculate remaining tokens
                remaining = token_info.limit_count - new_count if success else token_info.available_tokens
                
                # Track usage if successful
                if success:
                    self._track_usage(user_id, action_type, tokens_to_use)
                    self.logger.info(
                        f"User '{user_id}' used {tokens_to_use} tokens for action '{action_type}', " +
                        f"remaining: {remaining}"
                    )
                else:
                    self.logger.error(f"Failed to update token usage for user '{user_id}'")
                
                return success, remaining
                
        except Exception as e:
            self.logger.error(f"Error using tokens: {e}")
            raise RuntimeError(f"Failed to use tokens: {str(e)}")
    
    async def refund_tokens(self, user_id: str, action_type: str, token_count: Optional[int] = None) -> int:
        """
        Refund tokens back to a user.
        
        Args:
            user_id: The identifier for the user
            action_type: The type of action being refunded
            token_count: The number of tokens to refund (defaults to action type's value)
            
        Returns:
            New token count after refund
            
        Raises:
            RuntimeError: If token refund fails
        """
        try:
            # Create a lock for this user if it doesn't exist
            if user_id not in self._locks:
                self._locks[user_id] = asyncio.Lock()
            
            # Use the token count from parameters or get default for action type
            tokens_to_refund = token_count if token_count is not None else self._get_token_value(action_type)
            
            # For compatibility with original code
            button_name = action_type if action_type == "compose_demand" else "compose_demand"
            
            # Acquire lock to prevent race conditions
            async with self._locks[user_id]:
                # Get token info
                token_info = await self.user_repository.get_token_info(user_id, button_name)
                
                # If no token info found, return failure
                if not token_info:
                    self.logger.warning(f"No token allocation found for user '{user_id}' during refund")
                    return 0
                
                # Calculate new count (ensure it doesn't go below 0)
                new_count = max(0, token_info.click_count - tokens_to_refund)
                
                # Update token usage
                success = await self.user_repository.update_token_usage(token_info.button_id, new_count)
                
                # Calculate new available tokens
                available = token_info.limit_count - new_count if success else token_info.available_tokens
                
                if success:
                    self.logger.info(
                        f"Refunded {tokens_to_refund} tokens to user '{user_id}' for action '{action_type}', " +
                        f"new available: {available}"
                    )
                else:
                    self.logger.error(f"Failed to refund tokens for user '{user_id}'")
                
                return available
                
        except Exception as e:
            self.logger.error(f"Error refunding tokens: {e}")
            raise RuntimeError(f"Failed to refund tokens: {str(e)}")
    
    async def refresh_token_count(self, user_id: str, action_type: str) -> int:
        """
        Refresh and get the current token count for a user.
        
        Args:
            user_id: The identifier for the user
            action_type: The type of action for the tokens
            
        Returns:
            Current token count
            
        Raises:
            RuntimeError: If token refresh fails
        """
        try:
            # For compatibility with original code
            button_name = action_type if action_type == "compose_demand" else "compose_demand"
            
            # Get token info
            token_info = await self.user_repository.get_token_info(user_id, button_name)
            
            # If no token info found, return 0
            if not token_info:
                self.logger.warning(f"No token allocation found for user '{user_id}' during refresh")
                return 0
            
            available = token_info.available_tokens
            self.logger.debug(f"Refreshed token count for user '{user_id}': {available}")
            return available
            
        except Exception as e:
            self.logger.error(f"Error refreshing token count: {e}")
            raise RuntimeError(f"Failed to refresh token count: {str(e)}")
    
    # Legacy compatibility methods
    
    async def click_action_button(self, user_id: str, button_name: str) -> bool:
        """
        Legacy compatibility method for original app.py click_action_button.
        
        Args:
            user_id: User's username
            button_name: The button/action name
            
        Returns:
            True if the action should proceed, False otherwise
        """
        # Use the proper interface method
        success, _ = await self.use_tokens(user_id, button_name)
        return success
    
    async def refresh_available_click_count(self, user_id: str, button_name: str) -> None:
        """
        Legacy compatibility method for original app.py refresh_available_click_count.
        
        Args:
            user_id: User's username
            button_name: The button/action name
        """
        # Use the proper interface method
        count = await self.refresh_token_count(user_id, button_name)
        return count
    
    async def get_available_click_count(self, user_id: str, button_name: str) -> int:
        """
        Legacy compatibility method for original app.py get_available_click_count.
        
        Args:
            user_id: User's username
            button_name: The button/action name
            
        Returns:
            Available click count
        """
        # Use the proper interface method
        return await self.check_available_tokens(user_id, button_name)