import os
import asyncio
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the service locator to set up dependency injection
from casebuilder.infrastructure.dependency_injection import ServiceLocator

# Import the application initializer to set up the command bus and handlers
from casebuilder.infrastructure.application_initializer import ApplicationInitializer

# Import the AI adapter for OpenAI integration
from casebuilder.adapters.ai.openai_adapter import OpenAIAdapter

# Import the text processor service for handling large documents
from casebuilder.core.services.text_processor_service import TextProcessorService

# Example of how to integrate the new architecture with the existing app.py
async def main():
    # Set up service locator with required dependencies
    service_locator = ServiceLocator()
    
    # Register services
    # Get API key from environment variables
    api_key = os.getenv("OPENAI_API_KEY")
    ai_service = OpenAIAdapter(api_key=api_key)
    text_processor_service = TextProcessorService(ai_service)
    
    service_locator.register_service("IAIService", ai_service)
    service_locator.register_service("ITextProcessorService", text_processor_service)
    
    # Initialize the application
    streamlit_adapter = ApplicationInitializer.initialize(service_locator)
    
    # Example usage in a Streamlit context
    # This would normally be part of app.py, but shows how to use the adapter
    document_text = st.session_state.get('prelit_text_buffer', '')
    
    if document_text:
        # Generate medical analysis using the new architecture
        medical_analysis = await streamlit_adapter.detailed_medical_analysis(document_text)
        st.markdown("### Detailed Medical Analysis:")
        st.write(medical_analysis)
        
        # Generate facts and liability analysis
        facts_and_liability = await streamlit_adapter.extract_facts_and_liability(document_text)
        st.markdown("### Facts and Liability Analysis:")
        st.write(facts_and_liability)
        
        # Generate a demand letter
        demand_letter = await streamlit_adapter.create_demand_letter()
        st.markdown("### Demand Letter:")
        st.write(demand_letter)
        
        # Offer download button
        st.download_button(
            label="Download Demand Letter",
            data=demand_letter,
            file_name="demand_letter.txt",
            mime="text/plain"
        )

# For running the example directly (not through Streamlit)
if __name__ == "__main__":
    print("This example is designed to be integrated with the existing Streamlit app.")
    print("See the example code for how to adapt app.py to use the new architecture.")