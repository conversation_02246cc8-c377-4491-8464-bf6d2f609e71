"""
Application initializer for CaseBuilder.AI.
Configures and initializes all components for the application.
"""

import logging
from typing import Dict, Any, Optional

from .dependency_injection import ServiceLocator
from .mediator import Mediator
from .repositories.in_memory_repository import InMemoryCaseRepository, InMemoryDocumentRepository

from ..application.command_bus import CommandBus
from ..application.query_bus import QueryBus

from ..application.commands.medical_analysis_commands import (
    DetailedMedicalAnalysisCommand,
    MedicalExpensesAnalysisCommand,
    FutureMedicalExpensesCommand
)
from ..application.commands.facts_liability_commands import (
    FactsOfLossCommand,
    DetermineLiabilityCommand,
    FactsAndLiabilityDemandCommand
)
from ..application.commands.demand_letter_commands import (
    GeneralDamagesAnalysisCommand,
    ImpactOnLifestyleAnalysisCommand,
    GenerateDemandLetterCommand
)

from ..application.handlers.medical_analysis_handlers import (
    DetailedMedicalAnalysisH<PERSON><PERSON>,
    MedicalExpensesA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    FutureMedicalExpenses<PERSON><PERSON><PERSON>
)
from ..application.handlers.facts_liability_handlers import (
    F<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    FactsAndLiability<PERSON>emand<PERSON><PERSON><PERSON>
)
from ..application.handlers.demand_letter_handlers import (
    GeneralDamagesAnalysisHandler,
    ImpactOnLifestyleAnalysisHandler,
    GenerateDemandLetterHandler
)
from ..application.handlers.triage_handlers import (
    TriageDocumentHandler
)
from ..application.commands.triage_commands import (
    TriageDocumentCommand
)

from ..application.queries.case_queries import (
    GetCaseAnalysisQuery,
    GetDocumentAnalysisQuery,
    GetAvailablePromptTemplatesQuery,
    GetCaseDocumentsQuery
)
from ..application.handlers.case_query_handlers import (
    GetCaseAnalysisHandler,
    GetDocumentAnalysisHandler,
    GetAvailablePromptTemplatesHandler,
    GetCaseDocumentsHandler
)

from ..interfaces.web.streamlit_adapter import StreamlitAdapter

from ..core.interfaces.ICaseRepository import ICaseRepository
from ..core.interfaces.IDocumentRepository import IDocumentRepository
from ..core.interfaces.IAIService import IAIService
from ..core.interfaces.ITextProcessorService import ITextProcessorService

from ..application.errors import (
    ApplicationError,
    ValidationError,
    NotFoundError,
    ExternalServiceError
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('casebuilder')

class AppInitializer:
    """
    Initializes the application components and configures dependencies.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the application.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.mediator = Mediator()
        self.service_locator = ServiceLocator()

        # Configure error handling
        self._configure_error_handlers()

    def initialize(self) -> StreamlitAdapter:
        """
        Initialize the application and return a Streamlit adapter.

        Returns:
            A configured Streamlit adapter for UI integration
        """
        logger.info("Initializing CaseBuilder.AI application")

        # Register repositories
        self._register_repositories()

        # Register services
        self._register_services()

        # Register command handlers
        self._register_command_handlers()

        # Register query handlers
        self._register_query_handlers()

        # Create Streamlit adapter
        logger.info("Creating Streamlit adapter")
        streamlit_adapter = StreamlitAdapter(self.mediator)

        logger.info("CaseBuilder.AI application initialized successfully")
        return streamlit_adapter

    def _register_repositories(self) -> None:
        """Register repositories with the service locator."""
        logger.info("Registering repositories")

        # Create repositories
        case_repository = InMemoryCaseRepository()
        document_repository = InMemoryDocumentRepository()

        # Register repositories
        self.service_locator.register_service("ICaseRepository", case_repository)
        self.service_locator.register_service("IDocumentRepository", document_repository)

    def _register_services(self) -> None:
        """Register services with the service locator."""
        logger.info("Registering services")

        # Get services from service locator (previously registered in config.py)
        ai_service = self.service_locator.get_service("IAIService")
        text_processor_service = self.service_locator.get_service("ITextProcessorService")

        # Register additional services if needed
        # ...

    def _register_command_handlers(self) -> None:
        """Register command handlers with the mediator."""
        logger.info("Registering command handlers")

        # Get services from service locator
        ai_service = self.service_locator.get_service("IAIService")
        text_processor_service = self.service_locator.get_service("ITextProcessorService")
        case_repository = self.service_locator.get_service("ICaseRepository")
        document_repository = self.service_locator.get_service("IDocumentRepository")

        # Create command handlers
        # Medical analysis handlers
        detailed_medical_analysis_handler = DetailedMedicalAnalysisHandler(
            ai_service, text_processor_service
        )
        medical_expenses_handler = MedicalExpensesAnalysisHandler(
            ai_service, text_processor_service
        )
        future_expenses_handler = FutureMedicalExpensesHandler(
            ai_service, text_processor_service
        )

        # Facts and liability handlers
        facts_of_loss_handler = FactsOfLossHandler(
            ai_service, text_processor_service
        )
        determine_liability_handler = DetermineLiabilityHandler(
            ai_service, text_processor_service
        )
        facts_and_liability_demand_handler = FactsAndLiabilityDemandHandler(
            ai_service, text_processor_service
        )

        # Demand letter handlers
        general_damages_handler = GeneralDamagesAnalysisHandler(
            ai_service, text_processor_service
        )
        impact_on_lifestyle_handler = ImpactOnLifestyleAnalysisHandler(
            ai_service, text_processor_service
        )
        demand_letter_handler = GenerateDemandLetterHandler(
            ai_service, text_processor_service
        )

        # Register command handlers with mediator
        # Medical analysis commands
        self.mediator.register_command_handler(
            DetailedMedicalAnalysisCommand, detailed_medical_analysis_handler
        )
        self.mediator.register_command_handler(
            MedicalExpensesAnalysisCommand, medical_expenses_handler
        )
        self.mediator.register_command_handler(
            FutureMedicalExpensesCommand, future_expenses_handler
        )

        # Facts and liability commands
        self.mediator.register_command_handler(
            FactsOfLossCommand, facts_of_loss_handler
        )
        self.mediator.register_command_handler(
            DetermineLiabilityCommand, determine_liability_handler
        )
        self.mediator.register_command_handler(
            FactsAndLiabilityDemandCommand, facts_and_liability_demand_handler
        )

        # Demand letter commands
        self.mediator.register_command_handler(
            GeneralDamagesAnalysisCommand, general_damages_handler
        )
        self.mediator.register_command_handler(
            ImpactOnLifestyleAnalysisCommand, impact_on_lifestyle_handler
        )
        self.mediator.register_command_handler(
            GenerateDemandLetterCommand, demand_letter_handler
        )

    def _register_query_handlers(self) -> None:
        """Register query handlers with the mediator."""
        logger.info("Registering query handlers")

        # Get repositories from service locator
        case_repository = self.service_locator.get_service("ICaseRepository")
        document_repository = self.service_locator.get_service("IDocumentRepository")

        # Create query handlers
        case_analysis_handler = GetCaseAnalysisHandler(case_repository)
        document_analysis_handler = GetDocumentAnalysisHandler(document_repository)
        prompt_templates_handler = GetAvailablePromptTemplatesHandler()
        case_documents_handler = GetCaseDocumentsHandler(document_repository)

        # Register query handlers with mediator
        self.mediator.register_query_handler(
            GetCaseAnalysisQuery, case_analysis_handler
        )
        self.mediator.register_query_handler(
            GetDocumentAnalysisQuery, document_analysis_handler
        )
        self.mediator.register_query_handler(
            GetAvailablePromptTemplatesQuery, prompt_templates_handler
        )
        self.mediator.register_query_handler(
            GetCaseDocumentsQuery, case_documents_handler
        )

    def _configure_error_handlers(self) -> None:
        """Configure error handlers for the mediator."""
        logger.info("Configuring error handlers")

        # Register error handlers with mediator
        self.mediator.register_error_handler(
            ValidationError, self._handle_validation_error
        )
        self.mediator.register_error_handler(
            NotFoundError, self._handle_not_found_error
        )
        self.mediator.register_error_handler(
            ExternalServiceError, self._handle_external_service_error
        )
        self.mediator.register_error_handler(
            Exception, self._handle_generic_error
        )

    def _handle_validation_error(self, error: ValidationError, request: Any) -> Dict[str, Any]:
        """
        Handle a validation error.

        Args:
            error: The validation error
            request: The request that caused the error

        Returns:
            A dictionary with the error details
        """
        logger.warning(f"Validation error: {str(error)}")
        return {
            "success": False,
            "error": {
                "type": "ValidationError",
                "message": str(error),
                "field": getattr(error, "field", None)
            }
        }

    def _handle_not_found_error(self, error: NotFoundError, request: Any) -> Dict[str, Any]:
        """
        Handle a not found error.

        Args:
            error: The not found error
            request: The request that caused the error

        Returns:
            A dictionary with the error details
        """
        logger.warning(f"Not found error: {str(error)}")
        return {
            "success": False,
            "error": {
                "type": "NotFoundError",
                "message": str(error),
                "resource_type": error.resource_type,
                "resource_id": error.resource_id
            }
        }

    def _handle_external_service_error(self, error: ExternalServiceError, request: Any) -> Dict[str, Any]:
        """
        Handle an external service error.

        Args:
            error: The external service error
            request: The request that caused the error

        Returns:
            A dictionary with the error details
        """
        logger.error(f"External service error: {str(error)}")
        return {
            "success": False,
            "error": {
                "type": "ExternalServiceError",
                "message": str(error),
                "service_name": error.service_name
            }
        }

    def _handle_generic_error(self, error: Exception, request: Any) -> Dict[str, Any]:
        """
        Handle a generic error.

        Args:
            error: The error
            request: The request that caused the error

        Returns:
            A dictionary with the error details
        """
        logger.error(f"Unhandled error: {str(error)}", exc_info=True)
        return {
            "success": False,
            "error": {
                "type": "InternalError",
                "message": "An internal error occurred. Please try again later."
            }
        }