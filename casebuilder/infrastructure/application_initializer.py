from ..application.command_bus import CommandBus
from ..application.commands.facts_liability_commands import (
    FactsOfLossCommand,
    DetermineLiabilityCommand,
    FactsAndLiabilityDemandCommand
)
from ..application.commands.medical_analysis_commands import (
    DetailedMedicalAnalysisCommand,
    MedicalExpensesAnalysisCommand,
    FutureMedicalExpensesCommand
)
from ..application.commands.demand_letter_commands import (
    GeneralDamagesAnalysisCommand,
    ImpactOnLifestyleAnalysisCommand,
    GenerateDemandLetterCommand
)

from ..application.handlers.facts_liability_handlers import (
    FactsOfLossHandler,
    DetermineLiabilityHandler,
    FactsAndLiabilityDemandHandler
)
from ..application.handlers.medical_analysis_handlers import (
    DetailedMedicalAnalysisHandler,
    MedicalExpensesAnalysisHandler,
    FutureMedicalExpensesHandler
)
from ..application.handlers.demand_letter_handlers import (
    GeneralDamagesAnalysisHandler,
    ImpactOnLifestyleAnalysisHandler,
    GenerateDemandLetterHandler
)
from ..application.handlers.triage_handlers import (
    TriageDocumentHandler
)
from ..application.commands.triage_commands import (
    TriageDocumentCommand
)

from ..interfaces.web.streamlit_adapter import StreamlitAdapter
from .dependency_injection import ServiceLocator

class ApplicationInitializer:
    """
    Initializes the application components and registers command handlers with the command bus.
    """

    @staticmethod
    def initialize(service_locator: ServiceLocator) -> StreamlitAdapter:
        """
        Initialize the application and return a Streamlit adapter for UI integration.

        Args:
            service_locator: The service locator for dependency injection

        Returns:
            A configured Streamlit adapter
        """
        # Create command bus
        command_bus = CommandBus()

        # Get required services from service locator
        ai_service = service_locator.get_service("IAIService")
        text_processor_service = service_locator.get_service("ITextProcessorService")

        # Create handlers
        # Facts and liability handlers
        facts_of_loss_handler = FactsOfLossHandler(ai_service, text_processor_service)
        determine_liability_handler = DetermineLiabilityHandler(ai_service, text_processor_service)
        facts_and_liability_demand_handler = FactsAndLiabilityDemandHandler(ai_service, text_processor_service)

        # Medical analysis handlers
        detailed_medical_analysis_handler = DetailedMedicalAnalysisHandler(ai_service, text_processor_service)
        medical_expenses_handler = MedicalExpensesAnalysisHandler(ai_service, text_processor_service)
        future_medical_expenses_handler = FutureMedicalExpensesHandler(ai_service, text_processor_service)

        # Demand letter handlers
        general_damages_handler = GeneralDamagesAnalysisHandler(ai_service, text_processor_service)
        impact_on_lifestyle_handler = ImpactOnLifestyleAnalysisHandler(ai_service, text_processor_service)
        demand_letter_handler = GenerateDemandLetterHandler(ai_service, text_processor_service)

        # Triage handlers
        triage_document_handler = TriageDocumentHandler(ai_service, text_processor_service)

        # Register handlers with command bus
        # Facts and liability commands
        command_bus.register_handler(FactsOfLossCommand, facts_of_loss_handler)
        command_bus.register_handler(DetermineLiabilityCommand, determine_liability_handler)
        command_bus.register_handler(FactsAndLiabilityDemandCommand, facts_and_liability_demand_handler)

        # Medical analysis commands
        command_bus.register_handler(DetailedMedicalAnalysisCommand, detailed_medical_analysis_handler)
        command_bus.register_handler(MedicalExpensesAnalysisCommand, medical_expenses_handler)
        command_bus.register_handler(FutureMedicalExpensesCommand, future_medical_expenses_handler)

        # Demand letter commands
        command_bus.register_handler(GeneralDamagesAnalysisCommand, general_damages_handler)
        command_bus.register_handler(ImpactOnLifestyleAnalysisCommand, impact_on_lifestyle_handler)
        command_bus.register_handler(GenerateDemandLetterCommand, demand_letter_handler)

        # Triage commands
        command_bus.register_handler(TriageDocumentCommand, triage_document_handler)

        # Create Streamlit adapter
        streamlit_adapter = StreamlitAdapter(command_bus)

        return streamlit_adapter