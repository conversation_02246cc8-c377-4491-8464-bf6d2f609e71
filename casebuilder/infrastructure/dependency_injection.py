"""
Dependency injection infrastructure for CaseBuilderAI.
Provides a service locator pattern implementation.
"""

from typing import Dict, Type, TypeVar, Any, Optional, cast, Callable, Union

T = TypeVar('T')

class ServiceLocator:
    """
    A flexible dependency injection container implementation.
    
    This implementation supports two registration patterns:
    1. String-based registration: register_service("ServiceName", instance)
    2. Type-based registration: register(ServiceType, instance or factory)
    
    Both patterns can be used together in the same ServiceLocator instance.
    """
    
    def __init__(self):
        """Initialize an empty service registry."""
        # Dictionary for string-based service registration
        self._name_services: Dict[str, Any] = {}
        
        # Dictionary for type-based service registration (instances)
        self._type_instances: Dict[Type, Any] = {}
        
        # Dictionary for type-based service registration (factories)
        self._type_factories: Dict[Type, Callable[[], Any]] = {}
    
    # String-based API (Preferred for new code)
    
    def register_service(self, service_name: str, instance: Any) -> None:
        """
        Register a service with a string identifier.
        
        Args:
            service_name: The name of the service
            instance: The service instance to register
        """
        self._name_services[service_name] = instance
    
    def get_service(self, service_name: str) -> Any:
        """
        Get a registered service by name.
        
        Args:
            service_name: The name of the service to retrieve
            
        Returns:
            The registered service instance
            
        Raises:
            KeyError: If the service is not registered
        """
        if service_name not in self._name_services:
            raise KeyError(f"No service registered with name: {service_name}")
        
        return self._name_services[service_name]
    
    def has_service(self, service_name: str) -> bool:
        """
        Check if a service is registered by name.
        
        Args:
            service_name: The name of the service to check
            
        Returns:
            True if the service is registered, False otherwise
        """
        return service_name in self._name_services
    
    # Type-based API (Compatible with legacy code)
    
    def register(self, interface: Type[T], implementation: Union[Type[T], Callable[[], T]]) -> None:
        """
        Register an implementation for an interface.
        
        Args:
            interface: The interface or base class
            implementation: The concrete implementation class or factory function
        """
        if callable(implementation) and not isinstance(implementation, type):
            # It's a factory function
            self._type_factories[interface] = implementation
            self._type_instances[interface] = None
        else:
            # It's a type that needs to be instantiated
            self._type_factories[interface] = lambda: implementation()
            self._type_instances[interface] = None
    
    def register_instance(self, interface: Type[T], instance: T) -> None:
        """
        Register a pre-created instance for an interface.
        
        Args:
            interface: The interface or base class
            instance: An instance implementing the interface
        """
        self._type_instances[interface] = instance
    
    def get(self, interface: Type[T]) -> T:
        """
        Get an instance for the specified interface.
        
        Args:
            interface: The interface to get an implementation for
            
        Returns:
            An instance implementing the interface
            
        Raises:
            KeyError: If the interface is not registered
        """
        if interface not in self._type_instances or self._type_instances[interface] is None:
            if interface not in self._type_factories:
                raise KeyError(f"No implementation registered for {interface.__name__}")
            
            factory = self._type_factories[interface]
            self._type_instances[interface] = factory()
        
        return cast(T, self._type_instances[interface])
    
    def has(self, interface: Type) -> bool:
        """
        Check if an interface is registered.
        
        Args:
            interface: The interface to check
            
        Returns:
            True if the interface is registered, False otherwise
        """
        return interface in self._type_instances or interface in self._type_factories
    
    # Common methods
    
    def clear(self) -> None:
        """Clear all registered services, instances, and factories."""
        self._name_services.clear()
        self._type_instances.clear()
        self._type_factories.clear()


# Global instance for convenience
_global_locator = ServiceLocator()

# Convenience functions using the global service locator instance
def register(interface: Type[T], implementation: Union[Type[T], Callable[[], T]]) -> None:
    """Register a service implementation with the global service locator."""
    _global_locator.register(interface, implementation)

def register_instance(interface: Type[T], instance: T) -> None:
    """Register a pre-created service instance with the global service locator."""
    _global_locator.register_instance(interface, instance)

def get(interface: Type[T]) -> T:
    """Get a service implementation from the global service locator."""
    return _global_locator.get(interface)

def register_service(service_name: str, instance: Any) -> None:
    """Register a service by name with the global service locator."""
    _global_locator.register_service(service_name, instance)

def get_service(service_name: str) -> Any:
    """Get a service by name from the global service locator."""
    return _global_locator.get_service(service_name)