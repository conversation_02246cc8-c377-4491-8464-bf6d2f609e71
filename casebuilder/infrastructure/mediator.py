"""
Mediator implementation that combines command and query buses.
Provides a unified interface for dispatching both commands and queries.
"""

from typing import Generic, TypeVar, Type, Dict, Any

from ..application.command_bus import CommandBus
from ..application.query_bus import QueryBus
from ..application.commands.command_base import Command
from ..application.queries.query_base import Query
from ..application.handlers.command_handler import CommandHandler
from ..application.handlers.query_handler import QueryHandler
from ..application.errors import ApplicationError, ValidationError

T = TypeVar('T')

class Mediator:
    """
    Mediator that dispatches both commands and queries to their respective handlers.
    Implements the mediator pattern to centralize dispatching logic.
    """
    
    def __init__(self):
        """Initialize the mediator with command and query buses."""
        self._command_bus = CommandBus()
        self._query_bus = QueryBus()
        self._error_handlers: Dict[Type[Exception], callable] = {}
    
    def register_command_handler(self, command_type: Type[Command], handler: CommandHandler) -> None:
        """
        Register a handler for a specific command type.
        
        Args:
            command_type: The type of command to register a handler for
            handler: The handler to register
        """
        self._command_bus.register_handler(command_type, handler)
    
    def register_query_handler(self, query_type: Type[Query], handler: QueryHandler) -> None:
        """
        Register a handler for a specific query type.
        
        Args:
            query_type: The type of query to register a handler for
            handler: The handler to register
        """
        self._query_bus.register_handler(query_type, handler)
    
    def register_error_handler(self, exception_type: Type[Exception], handler: callable) -> None:
        """
        Register a handler for a specific exception type.
        
        Args:
            exception_type: The type of exception to register a handler for
            handler: The function to handle the exception
        """
        self._error_handlers[exception_type] = handler
    
    async def send(self, command: Command[T]) -> T:
        """
        Send a command to its registered handler and handle any errors.
        
        Args:
            command: The command to send
            
        Returns:
            The result of the command execution
        """
        try:
            return await self._command_bus.dispatch(command)
        except Exception as ex:
            # Handle the exception if a handler is registered for its type
            handler = self._get_error_handler(ex)
            if handler:
                return handler(ex, command)
            
            # Re-raise the exception if no handler is found
            raise
    
    async def query(self, query: Query[T]) -> T:
        """
        Send a query to its registered handler and handle any errors.
        
        Args:
            query: The query to send
            
        Returns:
            The result of the query execution
        """
        try:
            return await self._query_bus.dispatch(query)
        except Exception as ex:
            # Handle the exception if a handler is registered for its type
            handler = self._get_error_handler(ex)
            if handler:
                return handler(ex, query)
            
            # Re-raise the exception if no handler is found
            raise
    
    def _get_error_handler(self, exception: Exception) -> callable:
        """
        Get the appropriate error handler for an exception.
        
        Args:
            exception: The exception to handle
            
        Returns:
            The handler function or None if no handler is registered
        """
        # Check for a handler for the specific exception type
        handler = self._error_handlers.get(type(exception))
        if handler:
            return handler
        
        # Check for handlers for parent exception types
        for exception_type, handler in self._error_handlers.items():
            if isinstance(exception, exception_type):
                return handler
        
        # No handler found
        return None