"""
In-memory repository implementation for testing and development.
"""

from typing import Dict, List, Optional, TypeVar, Generic, Type
import copy
import uuid
from datetime import datetime

from ...core.models.Case import Case
from ...core.models.Document import Document
from ...core.interfaces.ICaseRepository import ICaseRepository
from ...core.interfaces.IDocumentRepository import IDocumentRepository

T = TypeVar('T')

class InMemoryRepository(Generic[T]):
    """
    Generic in-memory repository.
    Used as a base class for specific repositories.
    """
    
    def __init__(self):
        """Initialize an empty repository."""
        self._items: Dict[str, T] = {}
    
    async def get_by_id(self, item_id: str) -> Optional[T]:
        """
        Get an item by its ID.
        
        Args:
            item_id: The ID of the item to retrieve
            
        Returns:
            The item if found, None otherwise
        """
        # Return a deep copy to prevent modifying the stored item directly
        return copy.deepcopy(self._items.get(item_id))
    
    async def save(self, item: T) -> T:
        """
        Save an item (create or update).
        
        Args:
            item: The item to save
            
        Returns:
            The saved item
        """
        # Ensure the item has an ID
        if not hasattr(item, 'id') or not getattr(item, 'id'):
            setattr(item, 'id', str(uuid.uuid4()))
        
        # Ensure created_at and updated_at fields are set
        if hasattr(item, 'created_at') and not getattr(item, 'created_at'):
            setattr(item, 'created_at', datetime.now())
        
        if hasattr(item, 'updated_at'):
            setattr(item, 'updated_at', datetime.now())
        
        # Store a deep copy to prevent modifying the stored item directly
        self._items[getattr(item, 'id')] = copy.deepcopy(item)
        
        # Return a deep copy of the saved item
        return copy.deepcopy(item)
    
    async def delete(self, item_id: str) -> bool:
        """
        Delete an item by its ID.
        
        Args:
            item_id: The ID of the item to delete
            
        Returns:
            True if the item was deleted, False otherwise
        """
        if item_id in self._items:
            del self._items[item_id]
            return True
        
        return False
    
    async def get_all(self) -> List[T]:
        """
        Get all items.
        
        Returns:
            A list of all items
        """
        # Return deep copies to prevent modifying the stored items directly
        return [copy.deepcopy(item) for item in self._items.values()]

class InMemoryCaseRepository(InMemoryRepository[Case], ICaseRepository):
    """
    In-memory implementation of the case repository.
    """
    
    async def get_by_user_id(self, user_id: str) -> List[Case]:
        """
        Get all cases for a user.
        
        Args:
            user_id: The ID of the user
            
        Returns:
            A list of cases
        """
        # Return deep copies to prevent modifying the stored items directly
        return [
            copy.deepcopy(case) 
            for case in self._items.values() 
            if case.user_id == user_id
        ]

class InMemoryDocumentRepository(InMemoryRepository[Document], IDocumentRepository):
    """
    In-memory implementation of the document repository.
    """
    
    async def get_by_case_id(self, case_id: str) -> List[Document]:
        """
        Get all documents for a case.
        
        Args:
            case_id: The ID of the case
            
        Returns:
            A list of documents
        """
        # Return deep copies to prevent modifying the stored items directly
        return [
            copy.deepcopy(document) 
            for document in self._items.values() 
            if document.case_id == case_id
        ]