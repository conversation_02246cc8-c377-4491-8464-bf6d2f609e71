"""
Streamlit adapter for CaseBuilder.AI.
Provides a bridge between the Streamlit UI and the application layer.
"""

import streamlit as st
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging

import base64
import os
import asyncio

# Configure logging
logger = logging.getLogger(__name__)
from ...infrastructure.mediator import Mediator
from ...application.commands.facts_liability_commands import (
    FactsOfLossCommand,
    DetermineLiabilityCommand,
    FactsAndLiabilityDemandCommand
)
from ...application.commands.medical_analysis_commands import (
    DetailedMedicalAnalysisCommand,
    MedicalExpensesAnalysisCommand,
    FutureMedicalExpensesCommand
)
from ...application.commands.demand_letter_commands import (
    GeneralDamagesAnalysisCommand,
    ImpactOnLifestyleAnalysisCommand,
    GenerateDemandLetterCommand
)
from ...application.commands.triage_commands import (
    TriageDocumentCommand
)
from ...core.models.Document import DocumentType

class StreamlitAdapter:
    def __init__(self, mediator: Mediator):
        self.mediator = mediator

    async def show_unified_uploader(self, process_pdf_func, process_pdf_with_metadata_func, process_image_func):
        """Show a unified uploader that accepts both PDFs and images.

        This uploader will:
        1. Accept both PDFs and images
        2. Analyze document content to determine its type
        3. Store documents in appropriate session state variables
        4. Display dynamic buttons based on document types

        Args:
            process_pdf_func: Function to process PDF files
            process_pdf_with_metadata_func: Function to process PDF files with metadata
            process_image_func: Function to process image files

        Returns:
            None
        """
        st.markdown("### Document Upload")
        st.info("Upload police reports, medical records, and images. The system will automatically detect document types.")

        # Initialize document tracking in session state if not present
        if 'document_types' not in st.session_state:
            st.session_state['document_types'] = set()
        if 'has_police_report' not in st.session_state:
            st.session_state['has_police_report'] = False
        if 'has_medical_records' not in st.session_state:
            st.session_state['has_medical_records'] = False
        if 'has_images' not in st.session_state:
            st.session_state['has_images'] = False
        if 'has_multi_type_document' not in st.session_state:
            st.session_state['has_multi_type_document'] = False

        # Create the unified uploader
        with st.spinner('Uploading your files...'):
            col1, col2 = st.columns(2)

            with col1:
                uploaded_pdfs = st.file_uploader("Upload Documents", type="pdf", accept_multiple_files=True, key="upload_unified_docs")

            with col2:
                uploaded_images = st.file_uploader("Upload Images", type=["jpg", "jpeg", "png"], accept_multiple_files=True, key="upload_unified_images")

        # Process PDFs if uploaded
        if uploaded_pdfs:
            st.session_state['documents_uploaded'] = True

            # Create a progress container for document processing
            doc_progress_container = st.container()

            # Show overall progress bar
            progress_bar = doc_progress_container.progress(0)
            status_text = doc_progress_container.empty()

            # Create a container for document status updates
            doc_status_container = st.container()

            # Calculate total documents
            total_docs = len(uploaded_pdfs)

            # Process each PDF and determine its type
            for i, pdf in enumerate(uploaded_pdfs):
                # Update progress
                progress = (i) / total_docs
                progress_bar.progress(progress)

                # Process document

                # Show document being processed
                extraction_status = doc_status_container.empty()
                extraction_status.info(f"⏳ Extracting text from: {pdf.name} (this may take a few minutes for large documents)")

                # Create a progress placeholder for this document
                doc_progress = doc_status_container.empty()

                # Process the PDF with a simple progress indicator
                try:
                    # Use st.spinner for better UX during processing
                    with st.spinner("OCR Processing..."):
                        # Show progress bar with simulated progress
                        import time
                        for percent_complete in [0.0, 0.25, 0.5, 0.75]:
                            doc_progress.progress(percent_complete, text="OCR processing...")
                            time.sleep(0.1)  # Short delay to show progression

                        # Process the PDF
                        pdf_text = await process_pdf_func(pdf)

                        # Show completion before clearing
                        doc_progress.progress(1.0, text="OCR complete!")
                        time.sleep(0.5)  # Brief delay to show completion
                        doc_progress.empty()  # Remove the progress bar
                        extraction_status.empty()  # Remove the extraction message

                except Exception as e:
                    # Handle any exceptions during processing
                    error_msg = f"Error during OCR processing: {str(e)}"
                    doc_progress.error(error_msg)
                    print(f"Exception during OCR for {pdf.name}: {str(e)}")
                    pdf_text = f"Error: {error_msg}"

                    # Verify we got actual text content
                    if pdf_text and len(pdf_text.strip()) > 100:  # Ensure we have meaningful content
                        pass  # OCR processing complete
                    else:
                        # Handle empty or very short results
                        error_msg = "OCR returned insufficient text. The document may be empty, corrupted, or contain only images."
                        doc_progress.error(error_msg)
                        print(f"OCR error for {pdf.name}: {error_msg}")
                        pdf_text = f"Error: {error_msg}"

                    if not pdf_text:
                        # Update document status with error
                        extraction_status.error(f"❌ Error processing: {pdf.name}")
                        continue

                except Exception as e:
                    doc_progress.error(f"Error during OCR: {str(e)}")
                    extraction_status.error(f"❌ Error processing: {pdf.name}")
                    continue

                # Triage the document to determine its type
                triage_result = await self._triage_document(pdf_text, pdf.name, "pdf")
                doc_type = triage_result.get("document_type", DocumentType.OTHER)
                metadata = triage_result.get("metadata", {})

                # Get metadata from triage result
                metadata = triage_result.get("metadata", {})
                contains_medical = metadata.get("contains_medical_info", False)
                contains_incident = metadata.get("contains_incident_info", False)

                # Check if document contains multiple types
                contains_multiple_types = metadata.get('contains_multiple_types', False)
                document_types_detected = metadata.get('document_types_detected', {})

                # Update session state based on document type and content
                if contains_multiple_types:
                    # Handle documents with multiple types
                    has_police = document_types_detected.get('police_report', False)
                    has_medical = document_types_detected.get('medical_record', False)
                    has_billing = document_types_detected.get('billing_statement', False)

                    # Update session state for all detected types
                    if has_police:
                        st.session_state['has_police_report'] = True
                        st.session_state['document_types'].add('police_report')
                        if 'tcr_text_buffer' not in st.session_state:
                            st.session_state.tcr_text_buffer = pdf_text
                        else:
                            st.session_state.tcr_text_buffer += ' ' + pdf_text

                    if has_medical or has_billing:
                        st.session_state['has_medical_records'] = True
                        st.session_state['document_types'].add('medical_record')
                        # Process with metadata for medical records
                        metadata_status = doc_status_container.empty()
                        metadata_status.info(f"⏳ Processing medical metadata: {pdf.name} (this may take a few minutes for large documents)")

                        # Create a progress placeholder for metadata processing
                        meta_progress = doc_status_container.empty()

                        # Extract text with metadata with better error handling
                        try:
                            # Use st.spinner for better UX during processing
                            with st.spinner("Metadata Extraction..."):
                                # Show progress bar with simulated progress
                                import time
                                for percent_complete in [0.0, 0.2, 0.4, 0.6, 0.8]:
                                    meta_progress.progress(percent_complete, text="Extracting metadata...")
                                    time.sleep(0.1)  # Short delay to show progression

                                # Process the PDF with metadata
                                medical_text = await process_pdf_with_metadata_func(pdf)

                                # Show completion before clearing
                                meta_progress.progress(1.0, text="Metadata extraction complete!")
                                time.sleep(0.5)  # Brief delay to show completion
                                meta_progress.empty()  # Remove the progress bar
                                metadata_status.empty()  # Remove the metadata processing message

                        except Exception as e:
                            # Handle any exceptions during processing
                            error_msg = f"Error during metadata extraction: {str(e)}"
                            meta_progress.error(error_msg)
                            print(f"Exception during metadata extraction for {pdf.name}: {str(e)}")
                            medical_text = f"Error: {error_msg}"

                        # Verify we got actual text content
                        if medical_text and len(medical_text.strip()) > 100:  # Ensure we have meaningful content
                            pass  # Metadata extraction complete
                        else:
                            # Handle empty or very short results
                            error_msg = "Metadata extraction returned insufficient text. The document may be empty or corrupted."
                            meta_progress.error(error_msg)
                            print(f"Metadata extraction error for {pdf.name}: {error_msg}")
                            medical_text = f"Error: {error_msg}"
                        if 'text_buffer' not in st.session_state:
                            st.session_state.text_buffer = medical_text
                        else:
                            st.session_state.text_buffer += ' ' + medical_text

                    # Add to combined text buffer
                    if 'combined_text_buffer' not in st.session_state:
                        st.session_state.combined_text_buffer = pdf_text
                    else:
                        st.session_state.combined_text_buffer += ' ' + pdf_text

                    # Set a flag in session state to indicate a multi-type document was detected
                    st.session_state['has_multi_type_document'] = True

                    # Show success message for multiple types
                    st.success(f"✅ Multiple document types detected in: {pdf.name}")

                    # Show detailed information about detected types
                    types_detected = []
                    if has_police:
                        types_detected.append("Police Report")
                    if has_medical:
                        types_detected.append("Medical Record")
                    if has_billing:
                        types_detected.append("Billing Statement")

                    st.info(f"📝 Document contains: {', '.join(types_detected)} information")

                elif doc_type == DocumentType.POLICE_REPORT:
                    st.session_state['has_police_report'] = True
                    if 'tcr_text_buffer' not in st.session_state:
                        st.session_state.tcr_text_buffer = pdf_text
                    else:
                        st.session_state.tcr_text_buffer += ' ' + pdf_text
                    st.session_state['document_types'].add('police_report')
                    st.success(f"✅ Police Report detected: {pdf.name}")

                    if contains_incident:
                        st.info(f"📝 Document contains incident/accident information")

                elif doc_type == DocumentType.MEDICAL_RECORD:
                    st.session_state['has_medical_records'] = True
                    # Process with metadata for medical records
                    metadata_status = doc_status_container.empty()
                    metadata_status.info(f"⏳ Processing medical metadata: {pdf.name} (this may take a few minutes for large documents)")

                    # Create a progress placeholder for metadata processing
                    meta_progress = doc_status_container.empty()

                    # Extract text with metadata with better error handling
                    try:
                        # Use st.spinner for better UX during processing
                        with st.spinner("Metadata Extraction..."):
                            # Show progress bar with simulated progress
                            import time
                            for percent_complete in [0.0, 0.2, 0.4, 0.6, 0.8]:
                                meta_progress.progress(percent_complete, text="Extracting metadata...")
                                time.sleep(0.1)  # Short delay to show progression

                            # Process the PDF with metadata
                            medical_text = await process_pdf_with_metadata_func(pdf)

                            # Show completion before clearing
                            meta_progress.progress(1.0, text="Metadata extraction complete!")
                            time.sleep(0.5)  # Brief delay to show completion
                            meta_progress.empty()  # Remove the progress bar
                            metadata_status.empty()  # Remove the metadata processing message

                    except Exception as e:
                        # Handle any exceptions during processing
                        error_msg = f"Error during metadata extraction: {str(e)}"
                        meta_progress.error(error_msg)
                        print(f"Exception during metadata extraction for {pdf.name}: {str(e)}")
                        medical_text = f"Error: {error_msg}"

                    # Verify we got actual text content
                    if medical_text and len(medical_text.strip()) > 100:  # Ensure we have meaningful content
                        pass  # Metadata extraction complete
                    else:
                        # Handle empty or very short results
                        error_msg = "Metadata extraction returned insufficient text. The document may be empty or corrupted."
                        meta_progress.error(error_msg)
                        print(f"Metadata extraction error for {pdf.name}: {error_msg}")
                        medical_text = f"Error: {error_msg}"
                    if 'text_buffer' not in st.session_state:
                        st.session_state.text_buffer = medical_text
                    else:
                        st.session_state.text_buffer += ' ' + medical_text
                    st.session_state['document_types'].add('medical_record')
                    st.success(f"✅ Medical Record detected: {pdf.name}")

                    if contains_medical:
                        st.info(f"📝 Document contains medical information")

                elif doc_type == DocumentType.BILLING_STATEMENT:
                    st.session_state['has_medical_records'] = True
                    if 'billing_text_buffer' not in st.session_state:
                        st.session_state.billing_text_buffer = pdf_text
                    else:
                        st.session_state.billing_text_buffer += ' ' + pdf_text
                    st.session_state['document_types'].add('medical_record')  # Treat as medical for button display
                    st.success(f"✅ Billing Statement detected: {pdf.name}")
                    st.info(f"📝 Document contains billing information")

                else:
                    # For other document types, add to the general prelitigation buffer
                    if 'prelit_text_buffer' not in st.session_state:
                        st.session_state.prelit_text_buffer = pdf_text
                    else:
                        st.session_state.prelit_text_buffer += ' ' + pdf_text
                    st.session_state['document_types'].add('other')
                    st.info(f"📄 Document uploaded: {pdf.name}")

                # Add all text to the combined buffer for comprehensive analysis
                if 'combined_text_buffer' not in st.session_state:
                    st.session_state.combined_text_buffer = pdf_text
                else:
                    st.session_state.combined_text_buffer += ' ' + pdf_text

            # Update progress to 100% when all documents are processed
            progress_bar.progress(1.0)
            status_text.success("✅ All documents have been processed successfully")

            # Add a small delay to show the success message
            import time
            time.sleep(1)

            # Clear the progress indicators
            progress_bar.empty()
            status_text.empty()

        # Process images if uploaded
        if uploaded_images:
            st.session_state['has_images'] = True
            st.session_state['document_types'].add('image')

            # Show warning if no documents have been uploaded yet
            if not any(doc_type in st.session_state['document_types'] for doc_type in ['police_report', 'medical_record', 'other']):
                st.warning("⚠️ Please upload relevant documents (police reports, medical records) to provide context for image analysis. This will improve the quality of the analysis.")
            else:
                st.session_state['documents_uploaded'] = True

            # Display the uploaded images
            st.markdown(
                f"""
                <style>
                .uploaded-image-container {{
                    text-align: center;
                    margin-bottom: 20px;
                }}
                .uploaded-image {{
                    max-width: 400px;
                    border: 2px solid #4caf50;
                    border-radius: 8px;
                }}
                </style>
                """,
                unsafe_allow_html=True
            )

            # Store images in session state for later processing
            if 'uploaded_images' not in st.session_state:
                st.session_state.uploaded_images = []

            for idx, image in enumerate(uploaded_images):
                # Add to session state
                if image not in st.session_state.uploaded_images:
                    st.session_state.uploaded_images.append(image)

                # Display the image
                st.markdown(
                    f"""
                    <div class="uploaded-image-container">
                        <img src="data:image/png;base64,{base64.b64encode(image.getvalue()).decode()}" class="uploaded-image">
                        <p><strong>Image {idx+1}</strong></p>
                    </div>
                    """, unsafe_allow_html=True
                )

        # Show dynamic action buttons based on document types
        has_documents = any(doc_type in st.session_state['document_types'] for doc_type in ['police_report', 'medical_record', 'other'])

        # Only show actions when we have documents
        if has_documents:
            st.session_state['documents_uploaded'] = True
            st.markdown("### Available Actions")
            self._show_dynamic_action_buttons()

    async def _triage_document(self, document_text: str, file_name: str = "", file_type: str = "") -> Dict[str, Any]:
        """Triage a document to determine its type and extract metadata.

        Args:
            document_text: The text content of the document
            file_name: Original file name (optional)
            file_type: File type/extension (optional)

        Returns:
            A dictionary containing document classification and metadata
        """
        try:
            command = TriageDocumentCommand(
                document_text=document_text,
                file_name=file_name,
                file_type=file_type,
                contains_images=False
            )

            result = await self.mediator.send(command)
            return result
        except Exception as e:
            st.error(f"Error during document triage: {str(e)}")
            # Return a default result
            return {
                "document_type": DocumentType.OTHER,
                "confidence": 0.3,
                "metadata": {}
            }

    def _show_dynamic_action_buttons(self):
        """Show dynamic action buttons based on document types in session state."""
        # Create columns for buttons
        cols = st.columns(3)
        button_idx = 0

        # Check if we have images, police reports, and medical records
        has_images = 'image' in st.session_state['document_types']
        has_police_report = 'police_report' in st.session_state['document_types']
        has_medical_records = 'medical_record' in st.session_state['document_types']
        has_documents = any(doc_type in st.session_state['document_types'] for doc_type in ['police_report', 'medical_record', 'other'])

        # If we have both police reports and medical records, or a document with multiple types,
        # only show the demand letter button
        has_multi_type_document = st.session_state.get('has_multi_type_document', False)
        if (has_police_report and has_medical_records) or has_multi_type_document:
            # Generate Documents section
            # Check if we have images
            has_images = 'image' in st.session_state['document_types']

            if has_multi_type_document:
                if has_images:
                    st.success("✅ Document with multiple types detected (Police Report and Medical Records) and Visual Evidence")
                else:
                    st.success("✅ Document with multiple types detected (Police Report and Medical Records)")
            else:
                if has_images:
                    st.success("✅ All required documents detected (Police Report, Medical Records, and Visual Evidence)")
                else:
                    st.success("✅ All required documents detected (Police Report and Medical Records)")
            st.info("📝 Ready to generate demand letter with all available information")
            if st.button("Generate Demand Letter\n\r(5✪)", key="demand_letter", use_container_width=True):
                st.session_state['action'] = 'demand_letter'
                st.session_state['action_title'] = 'Generate Demand Letter'
                st.session_state['generate_demand'] = True
            return

        # If we have both images and documents, only show image analysis buttons
        if has_images and has_documents:
            # Image Analysis Actions section
            # Add explanation for image analysis buttons
            st.info("📸 **Image Analysis Actions**: Use Analyze Injuries to document visible trauma, Describe Accident Scene for location details, or Property Damage to assess vehicle/property impacts.")

            with cols[button_idx % 3]:
                if st.button("Analyze Injuries\n\r(1✪)", key="analyze_injuries", use_container_width=True):
                    st.session_state['action'] = 'analyze_injuries'
                    st.session_state['action_title'] = 'Analyze Injuries'
            button_idx += 1

            with cols[button_idx % 3]:
                if st.button("Accident Scene\n\r(1✪)", key="accident_scene", use_container_width=True):
                    st.session_state['action'] = 'accident_scene'
                    st.session_state['action_title'] = 'Accident Scene'
            button_idx += 1

            with cols[button_idx % 3]:
                if st.button("Property Damage\n\r(1✪)", key="property_damage", use_container_width=True):
                    st.session_state['action'] = 'property_damage'
                    st.session_state['action_title'] = 'Property Damage'
            button_idx += 1

            # Reset for new row
            st.markdown("")
            button_idx = 0
        # If we only have documents (no images), show document analysis buttons
        elif has_documents and not has_images:
            # Show buttons based on document types
            if has_police_report:
                # Police Report Actions section
                # Add explanation for police report buttons
                st.info("🚔 **Police Report Actions**: Use Police Report Summary for a concise overview, Facts & Liability Analysis for detailed accident facts, or In-Depth Liability Analysis for comprehensive fault assessment.")

                with cols[button_idx % 3]:
                    if st.button("Police Report Summary\n\r(1✪)", key="police_report_summary", use_container_width=True):
                        st.session_state['action'] = 'police_report_summary'
                        st.session_state['action_title'] = 'Police Report Summary'
                button_idx += 1

                with cols[button_idx % 3]:
                    if st.button("Facts & Liability Analysis\n\r(1✪)", key="facts_liability", use_container_width=True):
                        st.session_state['action'] = 'facts_liability'
                        st.session_state['action_title'] = 'Facts & Liability Analysis'
                button_idx += 1

                with cols[button_idx % 3]:
                    if st.button("In-Depth Liability Analysis\n\r(1✪)", key="in_depth_liability", use_container_width=True):
                        st.session_state['action'] = 'in_depth_liability'
                        st.session_state['action_title'] = 'In-Depth Liability Analysis'
                button_idx += 1

                # Reset for new row
                st.markdown("")
                button_idx = 0

            if has_medical_records:
                # Medical Record Actions section
                # Add explanation for medical record buttons
                st.info("🏥 **Medical Record Actions**: Use Medical Analysis for injury details, Medical Expenses for current costs, or Future Treatment for anticipated medical needs and expenses.")

                with cols[button_idx % 3]:
                    if st.button("Medical Analysis\n\r(1✪)", key="medical_analysis", use_container_width=True):
                        st.session_state['action'] = 'medical_analysis'
                        st.session_state['action_title'] = 'Medical Analysis'
                button_idx += 1

                with cols[button_idx % 3]:
                    if st.button("Medical Expenses\n\r(1✪)", key="medical_expenses", use_container_width=True):
                        st.session_state['action'] = 'medical_expenses'
                        st.session_state['action_title'] = 'Medical Expenses'
                button_idx += 1

                with cols[button_idx % 3]:
                    if st.button("Future Treatment\n\r(1✪)", key="future_treatment", use_container_width=True):
                        st.session_state['action'] = 'future_treatment'
                        st.session_state['action_title'] = 'Future Treatment'
                button_idx += 1

                # Reset for new row
                st.markdown("")
                button_idx = 0

        # Show Demand Letter button only when medical records are present but not police reports
        # (This is for cases where we only have medical records)
        if has_medical_records and not has_police_report:
            # Generate Documents section
            # Check if we have images
            has_images = 'image' in st.session_state['document_types']

            if has_images:
                st.success("✅ Medical Records and Visual Evidence detected")
            else:
                st.success("✅ Medical Records detected")

            st.info("📝 Ready to generate demand letter with available information")

            if st.button("Generate Demand Letter\n\r(5✪)", key="demand_letter", use_container_width=True):
                st.session_state['action'] = 'demand_letter'
                st.session_state['action_title'] = 'Generate Demand Letter'
                st.session_state['generate_demand'] = True

    async def extract_facts_and_liability(self) -> str:
        """Extract facts and liability analysis from uploaded documents."""
        try:
            # Use spinner instead of info for better UX
            with st.spinner("🔍 Analyzing documents for facts of loss and liability details..."):
                # Use combined_text_buffer as fallback if prelit_text_buffer is not available
                document_text = st.session_state.get('prelit_text_buffer', '')
                if not document_text.strip():
                    document_text = st.session_state.get('combined_text_buffer', '')

                # We'll use the preferences in a future update when FactsAndLiabilityDemandCommand supports them
                command = FactsAndLiabilityDemandCommand(
                    document_text=document_text,
                    client_name=st.session_state.get('client_name'),
                    additional_notes=st.session_state.get('additional_notes', '')
                )

                result = await self.mediator.send(command)
                st.session_state['facts_liability_result'] = result
                return result
        except Exception as e:
            st.error(f"Error during facts and liability analysis: {str(e)}")
            return ""

    async def comprehensive_medical_analysis(self) -> str:
        """Perform comprehensive medical analysis."""
        try:
            # Use spinner instead of info for better UX
            with st.spinner("🏥 Analyzing medical records and treatment history..."):
                # Use text_buffer first, then combined_text_buffer as fallback
                document_text = st.session_state.get('text_buffer', '')
                if not document_text.strip():
                    document_text = st.session_state.get('combined_text_buffer', '')

                # Get medical analysis preferences
                medical_preferences = st.session_state.get('medical_analysis_preferences', {'style': 'narrative'})

                # Create command for medical analysis
                command = DetailedMedicalAnalysisCommand(
                    document_text=document_text,
                    client_name=st.session_state.get('client_name'),
                    additional_notes=st.session_state.get('additional_notes', ''),
                    style=medical_preferences.get('style', 'narrative'),
                    detail_level=medical_preferences.get('detail_level', 'moderate'),
                    include_icd=medical_preferences.get('include_icd', False)
                )

                result = await self.mediator.send(command)
                st.session_state['medical_analysis_result'] = result
                return result
        except Exception as e:
            st.error(f"Error during medical analysis: {str(e)}")
            return ""

    async def identify_medical_expenses(self) -> str:
        """Identify and analyze medical expenses."""
        try:
            # Use spinner instead of info for better UX
            with st.spinner("💰 Calculating current medical expenses..."):
                # Use text_buffer first, then combined_text_buffer as fallback
                document_text = st.session_state.get('text_buffer', '')
                if not document_text.strip():
                    document_text = st.session_state.get('combined_text_buffer', '')

                # Get user preferences for medical analysis
                prefs = st.session_state.get('medical_analysis_preferences', {'style': 'tabular', 'detail_level': 'moderate', 'include_icd': False})
                command = MedicalExpensesAnalysisCommand(
                    document_text=document_text,
                    client_name=st.session_state.get('client_name'),
                    additional_notes=st.session_state.get('additional_notes', ''),
                    style=prefs.get('style', 'tabular'),
                    detail_level=prefs.get('detail_level', 'moderate'),
                    include_icd=prefs.get('include_icd', False)
                )

                result = await self.mediator.send(command)
                st.session_state['medical_expenses_result'] = result
                return result
        except Exception as e:
            st.error(f"Error during medical expenses analysis: {str(e)}")
            return ""

    async def identify_future_treatments(self) -> str:
        """Identify and analyze future medical treatments."""
        try:
            # Use spinner instead of info for better UX
            with st.spinner("🔮 Analyzing potential future medical treatments..."):
                # Use text_buffer first, then combined_text_buffer as fallback
                document_text = st.session_state.get('text_buffer', '')
                if not document_text.strip():
                    document_text = st.session_state.get('combined_text_buffer', '')

                # Get user preferences for medical analysis
                prefs = st.session_state.get('medical_analysis_preferences', {'style': 'tabular', 'detail_level': 'moderate', 'include_icd': False})
                command = FutureMedicalExpensesCommand(
                    document_text=document_text,
                    client_name=st.session_state.get('client_name'),
                    additional_notes=st.session_state.get('additional_notes', ''),
                    existing_medical_analysis=st.session_state.get('medical_analysis_result', ''),
                    style=prefs.get('style', 'tabular'),
                    detail_level=prefs.get('detail_level', 'moderate'),
                    include_icd=prefs.get('include_icd', False)
                )

                result = await self.mediator.send(command)
                st.session_state['future_treatments_result'] = result
                return result
        except Exception as e:
            st.error(f"Error during future treatments analysis: {str(e)}")
            return ""

    async def impact_on_lifestyle(self) -> str:
        """Assess impact on client's lifestyle."""
        try:
            # Use spinner instead of info for better UX
            with st.spinner("👥 Evaluating impact on client's daily life and activities..."):
                # Use text_buffer first, then combined_text_buffer as fallback
                document_text = st.session_state.get('text_buffer', '')
                if not document_text.strip():
                    document_text = st.session_state.get('combined_text_buffer', '')

                command = ImpactOnLifestyleAnalysisCommand(
                    document_text=document_text,
                    client_name=st.session_state.get('client_name'),
                    additional_notes=st.session_state.get('additional_notes', ''),
                    medical_analysis=st.session_state.get('medical_analysis_result', '')
                )

                result = await self.mediator.send(command)
                st.session_state['lifestyle_impact_result'] = result
                return result
        except Exception as e:
            st.error(f"Error during lifestyle impact analysis: {str(e)}")
            return ""

    async def identify_general_damages(self) -> str:
        """Identify and analyze general damages."""
        try:
            # Use spinner instead of info for better UX
            with st.spinner("⚖️ Calculating and analyzing general damages..."):
                # Use text_buffer first, then combined_text_buffer as fallback
                document_text = st.session_state.get('text_buffer', '')
                if not document_text.strip():
                    document_text = st.session_state.get('combined_text_buffer', '')

                command = GeneralDamagesAnalysisCommand(
                    document_text=document_text,
                    client_name=st.session_state.get('client_name'),
                    additional_notes=st.session_state.get('additional_notes', ''),
                    medical_analysis=st.session_state.get('medical_analysis_result', ''),
                    medical_expenses=st.session_state.get('medical_expenses_result', ''),
                    future_expenses=st.session_state.get('future_treatments_result', '')
                )

                result = await self.mediator.send(command)
                st.session_state['general_damages_result'] = result
                return result
        except Exception as e:
            st.error(f"Error during general damages analysis: {str(e)}")
            return ""

    async def create_demand_letter(self) -> str:
        """Generate the final demand letter."""
        try:
            # Use spinner instead of info for better UX
            with st.spinner("📝 Drafting demand letter with all analyzed information..."):
                # Get document text from session state
                document_text = st.session_state.get('combined_text_buffer', '')
                if not document_text.strip():
                    st.error("No documents found. Please upload the necessary documents first.")
                    return ""

                # Get client name from document metadata or use empty string if not found
                client_name = ""
                facts_liability_result = st.session_state.get('facts_liability_result', '')

                # Check if facts_liability_result is a dictionary before trying to access it
                if isinstance(facts_liability_result, dict):
                    client_name = facts_liability_result.get('client_name', '')
                elif 'document_metadata' in st.session_state:
                    client_name = st.session_state.get('document_metadata', {}).get('client_name', '')

                # Get tone and emphasis preferences from session state
                preferences = st.session_state.get('demand_letter_preferences', {})
                tone = preferences.get('tone', 'firm')
                emphasis = preferences.get('emphasis', {
                    'medical_damages': 3,
                    'quality_of_life': 3,
                    'economic_losses': 3,
                    'liability': 3
                })

                # Create command with all available information
                command = GenerateDemandLetterCommand(
                    document_text=document_text,
                    client_name=client_name,
                    selected_state=st.session_state.get('selected_state', ''),
                    liability_type=st.session_state.get('liability_type', ''),
                    demand_amount=st.session_state.get('demand_amount', 'Policy Limit'),
                    due_date=st.session_state.get('due_date', datetime.now()),
                    loss_of_income=st.session_state.get('loss_of_income', 0),
                    liability_accepted=st.session_state.get('liability_accepted', False),
                    liability_percentage=st.session_state.get('liability_percentage', 100),
                    um_amount=st.session_state.get('um_amount', 0),
                    additional_notes=st.session_state.get('additional_notes', ''),
                    facts_and_liability=st.session_state.get('facts_liability_result', ''),
                    medical_analysis=st.session_state.get('medical_analysis_result', ''),
                    current_medical_expenses=st.session_state.get('medical_expenses_result', ''),
                    future_medical_expenses=st.session_state.get('future_treatments_result', ''),
                    general_damages=st.session_state.get('general_damages_result', ''),
                    impact_on_lifestyle=st.session_state.get('lifestyle_impact_result', ''),
                    image_analysis_result=st.session_state.get('image_analysis_result', ''),
                    accident_scene_analysis=st.session_state.get('accident_scene_analysis', ''),
                    property_damage_analysis=st.session_state.get('property_damage_analysis', ''),
                    tone=tone,
                    emphasis=emphasis,
                    length=preferences.get('length', 'standard')
                )

                # Check if we have image analysis results
                has_image_analysis = False

                # Only consider image analysis if we actually have images uploaded
                if 'has_images' in st.session_state and st.session_state['has_images']:
                    # Check if we have any image analysis results
                    has_image_analysis = ('image_analysis_result' in st.session_state and st.session_state.get('image_analysis_result')) or \
                                        ('accident_scene_analysis' in st.session_state and st.session_state.get('accident_scene_analysis')) or \
                                        ('property_damage_analysis' in st.session_state and st.session_state.get('property_damage_analysis'))

                    if has_image_analysis:
                        # Use nested spinner for image analysis inclusion
                        with st.spinner("📸 Including image analysis in demand letter as supporting evidence."):
                            pass

                result = await self.mediator.send(command)

                if not result or not result.strip():
                    st.error("Failed to generate demand letter. The response was empty.")
                    return ""

                # Store the result in session state
                st.session_state['final_demand_letter'] = result
                st.success("✅ Demand letter completed successfully!")
                return result

        except Exception as e:
            st.error(f"Error during demand letter generation: {str(e)}")
            logger.error(f"Demand letter generation failed: {str(e)}", exc_info=True)
            return ""

    async def police_report_summary(self) -> str:
        """Generate a concise summary of the police report."""
        try:
            # Use spinner instead of info for better UX
            with st.spinner("🚔 Generating police report summary..."):
                # Use tcr_text_buffer first, then combined_text_buffer as fallback
                document_text = st.session_state.get('tcr_text_buffer', '')
                if not document_text.strip():
                    document_text = st.session_state.get('combined_text_buffer', '')

                # Get user preferences for police report analysis
                prefs = st.session_state.get('police_report_preferences', {
                    'detail_level': 'brief',
                    'emphasis': {'liability': 3, 'property_damage': 3},
                    'include_witness': False,
                    'style': 'narrative'
                })

                # Create a command for the police report summary
                command = FactsOfLossCommand(
                    document_text=document_text,
                    client_name=st.session_state.get('client_name', ''),
                    additional_notes=st.session_state.get('additional_notes', ''),
                    summary_only=True,  # Flag to indicate we want just a summary
                    detail_level=prefs.get('detail_level', 'brief'),
                    style=prefs.get('style', 'narrative'),
                    emphasis=prefs.get('emphasis', {'liability': 3, 'property_damage': 3}),
                    include_witness=prefs.get('include_witness', False)
                )

                result = await self.mediator.send(command)
                st.session_state['police_report_summary_result'] = result
                return result
        except Exception as e:
            st.error(f"Error generating police report summary: {str(e)}")
            return ""

    async def in_depth_liability_analysis(self) -> str:
        """Perform an in-depth analysis of liability based on the police report."""
        try:
            # Use spinner instead of info for better UX
            with st.spinner("⚖️ Performing in-depth liability analysis..."):
                # Use tcr_text_buffer first, then combined_text_buffer as fallback
                document_text = st.session_state.get('tcr_text_buffer', '')
                if not document_text.strip():
                    document_text = st.session_state.get('combined_text_buffer', '')

                # Get user preferences for police report analysis
                prefs = st.session_state.get('police_report_preferences', {
                    'detail_level': 'facts_liability',
                    'emphasis': {'liability': 3, 'property_damage': 3},
                    'include_witness': False,
                    'style': 'narrative'
                })

                # Create a command for the in-depth liability analysis
                command = DetermineLiabilityCommand(
                    document_text=document_text,
                    client_name=st.session_state.get('client_name', ''),
                    additional_notes=st.session_state.get('additional_notes', ''),
                    in_depth=True,  # Flag to indicate we want an in-depth analysis
                    detail_level=prefs.get('detail_level', 'facts_liability'),
                    style=prefs.get('style', 'narrative'),
                    emphasis=prefs.get('emphasis', {'liability': 3, 'property_damage': 3}),
                    include_witness=prefs.get('include_witness', False)
                )

                result = await self.mediator.send(command)
                st.session_state['in_depth_liability_result'] = result
                return result
        except Exception as e:
            st.error(f"Error during in-depth liability analysis: {str(e)}")
            return ""

    def show_progress_sidebar(self) -> None:
        """Display progress sidebar with completed steps."""
        # This method is no longer used but kept for compatibility
        pass

    def setup_demand_letter_preferences(self):
        """Configure demand letter tone and content preferences in sidebar."""
        with st.sidebar.expander("#### 📝 Demand Letter Preferences", expanded=False):

            # Length Configuration
            st.markdown("#### Length Settings")
            length = st.radio(
                "Select Letter Length",
                options=["Basic", "Standard", "Large"],
                index=1,  # Standard as default
                help="Choose the overall length and detail level of the demand letter",
                key="demand_letter_length"  # Add unique key
            )

            # Tone Configuration
            st.markdown("#### Tone Settings")
            tone = st.radio(
                "Select Letter Tone",
                options=["Firm", "Neutral", "Conciliatory", "Aggressive"],
                index=0,  # Firm as default
                help="Choose the overall tone of the demand letter",
                key="demand_letter_tone"  # Add unique key
            )

            # Content Emphasis
            st.markdown("#### Content Emphasis")
            st.markdown("Priority Areas (Higher = More Emphasis)")

            emphasis_cols = st.columns(2)

            with emphasis_cols[0]:
                medical_emphasis = st.slider(
                    "Medical Damages",
                    min_value=1,
                    max_value=5,
                    value=3,
                    help="Emphasis on medical treatments and injuries",
                    key="medical_damages_emphasis"  # Add unique key
                )

                quality_of_life = st.slider(
                    "Quality of Life Impact",
                    min_value=1,
                    max_value=5,
                    value=3,
                    help="Emphasis on lifestyle changes and emotional impact",
                    key="quality_of_life_emphasis"  # Add unique key
                )

            with emphasis_cols[1]:
                economic_losses = st.slider(
                    "Economic Losses",
                    min_value=1,
                    max_value=5,
                    value=3,
                    help="Emphasis on financial impacts and losses",
                    key="economic_losses_emphasis"  # Add unique key
                )

                liability_emphasis = st.slider(
                    "Liability",
                    min_value=1,
                    max_value=5,
                    value=3,
                    help="Emphasis on fault and responsibility",
                    key="liability_emphasis"  # Add unique key
                )

            # Store preferences in session state
            st.session_state['demand_letter_preferences'] = {
                'length': length.lower(),
                'tone': tone.lower(),
                'emphasis': {
                    'medical_damages': medical_emphasis,
                    'quality_of_life': quality_of_life,
                    'economic_losses': economic_losses,
                    'liability': liability_emphasis
                }
            }

    def setup_police_report_preferences(self):
        """Configure police report analysis preferences in sidebar."""

        with st.sidebar.expander("#### 🚔 Police Report Preferences", expanded=False):
            # Analysis Detail Level Configuration
            st.markdown("#### Analysis Detail Level")
            detail_level = st.radio(
                "Select Detail Level",
                options=["Brief", "Facts & Liability", "Comprehensive"],
                index=1,  # Facts & Liability as default
                help="Choose the level of detail for the police report analysis",
                key="police_report_detail_level"
            )

            # Content Emphasis Configuration
            st.markdown("#### Content Emphasis")

            # Create two columns for sliders
            emphasis_cols = st.columns(2)

            with emphasis_cols[0]:
                liability_emphasis = st.slider(
                    "Liability",
                    min_value=1,
                    max_value=5,
                    value=3,
                    help="Emphasis on liability determination and fault analysis",
                    key="police_liability_emphasis"
                )

            with emphasis_cols[1]:
                property_damage = st.slider(
                    "Property Damage",
                    min_value=1,
                    max_value=5,
                    value=3,
                    help="Emphasis on property damage details and assessment",
                    key="police_property_damage_emphasis"
                )

            # Witness Statements Toggle
            include_witness = st.checkbox(
                "Include Witness Statements",
                value=False,
                help="Include detailed analysis of witness statements in the report",
                key="include_witness_statements"
            )

            # Style Preference Configuration
            st.markdown("#### Style Preference")
            style = st.radio(
                "Select Analysis Style",
                options=["Event Timeline", "Narrative-Ready Summary"],
                index=1,  # Narrative-Ready Summary as default
                help="Select how you want the police report analysis to be written",
                key="police_report_style"
            )

            # Map detail level to a value for the backend
            detail_mapping = {
                "Brief": "brief",
                "Facts & Liability": "facts_liability",
                "Comprehensive": "comprehensive"
            }

            # Map style to a value for the backend
            style_mapping = {
                "Event Timeline": "chronological",
                "Narrative-Ready Summary": "narrative"
            }

            # Store preferences in session state
            st.session_state['police_report_preferences'] = {
                'detail_level': detail_mapping[detail_level],
                'emphasis': {
                    'liability': liability_emphasis,
                    'property_damage': property_damage
                },
                'include_witness': include_witness,
                'style': style_mapping[style]
            }

    def setup_medical_analysis_preferences(self):
        """Configure medical analysis style preferences in sidebar."""

        with st.sidebar.expander("#### 🏥 Customize Medical Summary", expanded=False):
            # Style Configuration
            style = st.radio(
                "Select Analysis Style",
                options=["Narrative", "Chronological", "Bulleted", "Tabular"],
                index=0,  # Narrative as default
                help="Choose the presentation style of the medical analysis",
                key="medical_analysis_style"
            )

            # Detail Level Configuration
            detail_level = st.radio(
                "Select Detail Level",
                options=["Brief (Summary)", "Moderate (Essential Details)", "Comprehensive (All Medical Details)" ],
                index=1,  # Moderate as default
                help="Choose the level of detail for the medical analysis",
                key="medical_detail_level"
            )

            # Option to include ICD diagnosis codes in analysis
            include_icd = st.checkbox(
                "Include ICD Diagnosis Codes",
                value=False,  # Changed to False by default
                help="Include ICD codes extracted from medical records in the analysis",
                key="include_icd_codes"
            )

            # Map detail level to a value for the backend
            detail_mapping = {
                "Brief (Summary)": "brief",
                "Moderate (Essential Details)": "moderate",
                "Comprehensive (All Medical Details)": "comprehensive"
            }

            # Store preferences in session state
            st.session_state['medical_analysis_preferences'] = {
                'style': style.lower(),
                'detail_level': detail_mapping[detail_level],
                'include_icd': include_icd
            }

    def consolidate_info(self) -> str:
        """
        Consolidate all case information for comprehensive analysis.
        Compatible with the existing consolidate_info function.

        Returns:
            Consolidated information as a string
        """
        info_parts = [
            f"State: {st.session_state.get('selected_state', '')}",
            f"Liability Type: {st.session_state.get('liability_type', '')}",
            f"Loss of Income: {st.session_state.get('loss_of_income', '')}",
            f"Demand Amount: {st.session_state.get('demand_amount', 'Policy Limit')}",
            f"Due Date: {st.session_state.get('due_date', 'No Date Set')}",
            f"UM Amount Requested: ${st.session_state.get('um_amount', '')}" if st.session_state.get('liability_type', '') in ['Uninsured Motorist', 'Under-Insured Motorist'] else "",
            f"Liability Accepted: {'Yes' if st.session_state.get('liability_accepted', False) else 'No'}",
            f"Liability Percentage: {st.session_state.get('liability_percentage', 0)}%",
            "Additional Notes:",
            st.session_state.get('additional_notes', ''),
            "Facts of Loss & Liability:",
            st.session_state.get('facts_liability_result', ''),
            "Medical Information:",
            st.session_state.get('medical_analysis_result', ''),
            "Medical Expenses",
            st.session_state.get('medical_expenses_result', ''),
            "Future Treatments:",
            st.session_state.get('future_treatments_result', ''),
            "General Damages:",
            st.session_state.get('general_damages_result', ''),
            "Lifestyle Impact:",
            st.session_state.get('lifestyle_impact_result', ''),
            "Legal Framework:",
            st.session_state.get('legal_framework', '')
        ]

        consolidated_info = "\n".join(part for part in info_parts if part and isinstance(part, str) and part.strip())
        return consolidated_info

    async def get_available_prompts(self) -> Dict[str, Dict[str, str]]:
        """
        Get all available prompt templates.

        Returns:
            A dictionary mapping task names to their templates
        """
        try:
            # This is a placeholder for future implementation
            # Will be implemented when query handlers are added
            return {}
        except Exception as e:
            st.error(f"An error occurred while retrieving prompt templates: {str(e)}")
            return {}
