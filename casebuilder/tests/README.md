# CaseBuilder.AI Test Suite

Este directorio contiene pruebas para la aplicación CaseBuilder.AI. La suite de pruebas incluye pruebas unitarias para componentes individuales y pruebas de integración para verificar la interacción entre componentes.

## Estructura de Pruebas

```
tests/
├── test_command_bus.py            # Prueba básica del command bus
├── test_application_integration.py # Prueba de integración básica
├── run_all_tests.py               # Script para ejecutar todas las pruebas
├── unit/                          # Pruebas unitarias para componentes individuales (WIP)
├── README.md                      # Esta documentación
```

## Ejecutando las Pruebas

Para ejecutar todas las pruebas, utiliza el script `run_all_tests.py`:

```bash
python -m casebuilder.tests.run_all_tests
```

Para ejecutar una prueba específica, utiliza el módulo unittest de Python:

```bash
python -m casebuilder.tests.test_command_bus
```

## Pruebas Actuales

### CommandBus (test_command_bus.py)

Prueba básica del bus de comandos que verifica:
- Registro de manejadores para tipos de comandos
- Despacho de comandos a sus manejadores correspondientes

### Integración Básica (test_application_integration.py)

Prueba de integración básica que verifica:
- Creación de manejadores con servicios simulados (mocks)
- Registro de manejadores en el bus de comandos
- Despacho de comandos a través del bus
- Ejecución completa de un comando médico básico

## Componentes Simulados

Las pruebas utilizan implementaciones simuladas (mocks) de servicios principales para aislar los componentes que se están probando:

- **Servicio de IA**: Simulado para devolver respuestas predecibles
- **Procesador de Texto**: Simulado para procesar texto de manera determinista

## Pruebas Futuras

1. **Modelos de Dominio**: Pruebas para el comportamiento de los modelos de dominio del núcleo
2. **Repositorios**: Pruebas para componentes de acceso a datos
3. **Adaptadores de UI**: Pruebas para componentes de integración de UI
4. **Pruebas End-to-End**: Pruebas que utilizan servicios reales (con claves API de prueba)

## Agregando Nuevas Pruebas

Al agregar nuevas características a la aplicación, sigue estas pautas:

1. Crea pruebas unitarias para componentes individuales
2. Actualiza las pruebas de integración para verificar las interacciones entre componentes
3. Ejecuta la suite completa de pruebas antes de confirmar cambios

Las nuevas pruebas deben seguir el patrón establecido:
- Usa el prefijo `test_` para los archivos de prueba
- Cada archivo debe contener clases de prueba que hereden de `unittest.TestCase`
- Implementa métodos de prueba con prefijo `test_` para pruebas síncronas
- Para pruebas asíncronas, crea métodos auxiliares con prefijo `async_test_` y métodos wrapper con prefijo `test_` que ejecuten la versión asíncrona