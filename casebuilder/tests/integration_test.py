"""
Integration tests for CaseBuilder.AI application layer.
Tests the command bus, handlers, and services working together.
"""

import unittest
import asyncio
import os
from unittest.mock import MagicMock, patch

# Import the command bus and commands for testing
from casebuilder.application.command_bus import CommandBus
from casebuilder.application.commands.medical_analysis_commands import DetailedMedicalAnalysisCommand
from casebuilder.application.commands.facts_liability_commands import FactsAndLiabilityDemandCommand
from casebuilder.application.commands.demand_letter_commands import GenerateDemandLetterCommand

# Import infrastructure components for testing
from casebuilder.infrastructure.application_initializer import ApplicationInitializer
from casebuilder.infrastructure.dependency_injection import ServiceLocator

class IntegrationTest(unittest.TestCase):
    """
    Integration tests for the application layer.
    """
    
    def setUp(self):
        """
        Set up the test environment.
        """
        # Create a service locator and register mock services
        self.service_locator = ServiceLocator()
        
        # Create mock services
        self.ai_service = MagicMock()
        self.text_processor = MagicMock()
        
        # Configure mock behavior for text processor
        async def mock_process_text(text, prompt, model_alias="gpt4o"):
            if "medical" in prompt.lower():
                return "MEDICAL ANALYSIS: <PERSON><PERSON> has injuries that require treatment."
            elif "facts" in prompt.lower() or "liability" in prompt.lower():
                return "FACTS AND LIABILITY: The accident occurred on 2023-01-01. The defendant is liable."
            elif "demand letter" in prompt.lower():
                return "DEMAND LETTER: We demand compensation for our client's injuries."
            else:
                return f"Mock processed text response for {model_alias}"
        
        self.text_processor.process_text_in_segments = mock_process_text
        
        # Register mock services
        self.service_locator.register_service("IAIService", self.ai_service)
        self.service_locator.register_service("ITextProcessorService", self.text_processor)
        
        # Create the application initializer and get the adapter
        self.streamlit_adapter = ApplicationInitializer.initialize(self.service_locator)
        
        # Test data
        self.test_document = """
        TRAFFIC COLLISION REPORT
        Date: 2023-01-01
        Time: 14:30
        Location: Main St & Oak Ave
        
        Driver 1: John Smith
        Driver 2: Jane Doe
        
        Narrative: Driver 1 ran a red light and collided with Driver 2's vehicle.
        
        MEDICAL RECORDS
        Patient: Jane Doe
        Date: 2023-01-01
        
        Diagnosis: Cervical strain, contusion to left shoulder
        Treatment: Pain medication, physical therapy
        Cost: $2,500
        """
    
    def test_command_bus_initialization(self):
        """
        Test that the command bus is properly initialized with handlers.
        """
        # Get the command bus from the adapter
        command_bus = self.streamlit_adapter.command_bus
        
        # Check that the command bus has handlers registered
        self.assertTrue(len(command_bus._handlers) > 0)
        
        # Print registered handlers for debugging
        print("Registered handlers:")
        for handler_type in command_bus._handlers:
            print(f"  - {handler_type.__name__}")
        
        # Check that handlers are registered for key commands by name
        handler_types = [key.__name__ for key in command_bus._handlers.keys()]
        self.assertIn("DetailedMedicalAnalysisCommand", handler_types)
        self.assertIn("FactsAndLiabilityDemandCommand", handler_types)
        self.assertIn("GenerateDemandLetterCommand", handler_types)


# Allow running the tests from the command line
if __name__ == '__main__':
    unittest.main()