#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run all tests for the CaseBuilder.AI application.
"""

import unittest
import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def run_tests():
    """
    Discover and run all tests in the casebuilder/tests directory.
    """
    print("=" * 80)
    print(f"Running tests for CaseBuilder.AI")
    print(f"Project root: {project_root}")
    print("=" * 80)
    
    # Create a test loader
    loader = unittest.TestLoader()
    
    # Discover tests in the tests directory
    test_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Looking for tests in: {test_dir}")
    
    # Load tests with pattern test_*.py
    suite = loader.discover(test_dir, pattern="test_*.py")
    
    # Run the tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Return True if all tests passed, False otherwise
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    # Exit with appropriate exit code
    sys.exit(0 if success else 1)