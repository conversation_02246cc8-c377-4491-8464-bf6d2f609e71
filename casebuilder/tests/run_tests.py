#!/usr/bin/env python3
"""
Test runner for CaseBuilder.AI tests.
Discovers and runs all tests in the tests directory.
"""

import unittest
import sys
import os

if __name__ == '__main__':
    # Add the project root directory to the Python path
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, project_root)
    
    # Print paths for debugging
    print(f"Project root: {project_root}")
    print(f"Current directory: {os.getcwd()}")
    print(f"Python path: {sys.path}")
    
    # Discover and run tests
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Start directory for test discovery: {start_dir}")
    
    suite = loader.discover(start_dir, pattern="*_test.py")
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Return non-zero exit code if any tests failed
    sys.exit(not result.wasSuccessful())