import unittest
import async<PERSON>
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any

from ..application.agents.base_agent import BaseAgent
from ..application.agents.extraction_agent import ExtractionAgent
from ..core.interfaces.IAIService import IAIService
from ..core.interfaces.ITextProcessorService import ITextProcessorService

class TestExtractionAgent(unittest.TestCase):
    def setUp(self):
        # Create mocks for dependencies
        self.ai_service = Mock(spec=IAIService)
        self.text_processor = Mock(spec=ITextProcessorService)
        self.text_processor.process_text_in_segments = AsyncMock()

        # Initialize agent
        self.agent = ExtractionAgent(
            ai_service=self.ai_service,
            text_processor=self.text_processor
        )

    def test_agent_initialization(self):
        """Test that agent initializes with correct dependencies"""
        self.assertIsInstance(self.agent, BaseAgent)
        self.assertEqual(self.agent.ai_service, self.ai_service)
        self.assertEqual(self.agent.text_processor, self.text_processor)

    async def test_process_extraction(self):
        """Test the extraction process"""
        # Test data
        test_content = "Sample medical record content"
        test_context = {"type": "medical_records"}
        expected_result = "Extracted information"

        # Configure mock
        self.text_processor.process_text_in_segments.return_value = expected_result

        # Execute
        result = await self.agent.process(test_content, test_context)

        # Assert
        self.assertEqual(result, expected_result)
        self.text_processor.process_text_in_segments.assert_called_once_with(
            model_alias="gpt4omini",
            text=test_content,
            prompt=self.agent._get_extraction_prompt(test_context)
        )

    def test_validate_result(self):
        """Test validation of extraction results"""
        result = "Valid extracted information"
        self.assertTrue(self.agent.validate(result))

if __name__ == '__main__':
    unittest.main()