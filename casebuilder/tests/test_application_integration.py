#!/usr/bin/env python3
"""
Basic integration test for the application layer.
"""

import unittest
import asyncio
from unittest.mock import MagicMock, AsyncMock
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from casebuilder.application.command_bus import CommandBus
from casebuilder.application.commands.medical_analysis_commands import DetailedMedicalAnalysisCommand
from casebuilder.application.handlers.medical_analysis_handlers import DetailedMedicalAnalysisHandler
from casebuilder.infrastructure.dependency_injection import ServiceLocator

class SimpleIntegrationTest(unittest.TestCase):
    def setUp(self):
        # Create mocks for required services
        self.ai_service = MagicMock()
        self.text_processor = MagicMock()
        
        # Configure the text processor mock
        async def mock_process(model_alias, text, prompt):
            return "Mock medical analysis result"
        
        self.text_processor.process_text_in_segments = mock_process
        
        # Create the command bus
        self.command_bus = CommandBus()
        
        # Create the handler with mocked services
        self.handler = DetailedMedicalAnalysisHandler(
            ai_service=self.ai_service,
            text_processor=self.text_processor
        )
        
        # Register the handler
        self.command_bus.register_handler(DetailedMedicalAnalysisCommand, self.handler)

    def test_handler_registration(self):
        # Verify the handler is registered correctly
        self.assertIn(DetailedMedicalAnalysisCommand, self.command_bus._handlers)
        self.assertEqual(self.command_bus._handlers[DetailedMedicalAnalysisCommand], self.handler)

    async def async_test_medical_analysis(self):
        # Create a command
        command = DetailedMedicalAnalysisCommand(
            document_text="Test medical record",
            client_name="Test Client",
            additional_notes="Test notes"
        )
        
        # Dispatch the command
        result = await self.command_bus.dispatch(command)
        
        # Verify the result
        self.assertEqual(result, "Mock medical analysis result")

    def test_medical_analysis(self):
        asyncio.run(self.async_test_medical_analysis())

if __name__ == "__main__":
    unittest.main()