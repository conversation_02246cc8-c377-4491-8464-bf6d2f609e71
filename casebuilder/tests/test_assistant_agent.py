"""
Tests for the AssistantAgent.

This module contains tests for the AssistantAgent functionality.
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any

from ..application.agents.base_agent import BaseAgent
from ..application.agents.assistant_agent import AssistantAgent
from ..core.interfaces.IAIService import IAIService
from ..core.interfaces.ITextProcessorService import ITextProcessorService

class TestAssistantAgent(unittest.TestCase):
    def setUp(self):
        # Create mocks for dependencies
        self.ai_service = Mock(spec=IAIService)
        self.text_processor = Mock(spec=ITextProcessorService)
        self.text_processor.process_text_in_segments = AsyncMock()

        # Sample predefined answers
        self.predefined_answers = {
            "upload": "You can upload documents by dragging and dropping them.",
            "security": {"content": "Case<PERSON><PERSON><PERSON> takes data security very seriously."}
        }

        # Initialize agent
        self.agent = AssistantAgent(
            ai_service=self.ai_service,
            text_processor=self.text_processor,
            predefined_answers=self.predefined_answers
        )

    def test_agent_initialization(self):
        """Test that agent initializes with correct dependencies"""
        self.assertIsInstance(self.agent, BaseAgent)
        self.assertEqual(self.agent.ai_service, self.ai_service)
        self.assertEqual(self.agent.text_processor, self.text_processor)
        self.assertEqual(self.agent.predefined_answers, self.predefined_answers)

    def test_check_predefined_answers_exact_match(self):
        """Test the exact match case for predefined answers"""
        # Set up a test case with an exact match
        self.agent.predefined_answers = {"exact_match": {"content": "This is an exact match response"}}
        
        # Check that the exact match is returned
        result = self.agent._check_predefined_answers("Any query")
        self.assertEqual(result, "This is an exact match response")

    def test_check_predefined_answers_keyword_match(self):
        """Test keyword matching for predefined answers"""
        # Test with a query that contains a keyword
        result = self.agent._check_predefined_answers("How do I upload files?")
        self.assertEqual(result, "You can upload documents by dragging and dropping them.")

    def test_check_predefined_answers_pattern_match(self):
        """Test pattern matching for predefined answers"""
        # Test with a query that matches a pattern
        result = self.agent._check_predefined_answers("Is my data secure?")
        self.assertEqual(result, "CaseBuilder takes data security very seriously.")

    def test_greeting_detection(self):
        """Test greeting detection"""
        self.assertTrue(self.agent._is_greeting("hi"))
        self.assertTrue(self.agent._is_greeting("hello there"))
        self.assertFalse(self.agent._is_greeting("upload files"))

    async def test_process_with_predefined_answer(self):
        """Test processing with a predefined answer"""
        # Test with a query that has a predefined answer
        result = await self.agent.process("How do I upload files?", {})
        
        # Should return the predefined answer without calling the AI service
        self.assertEqual(result, "You can upload documents by dragging and dropping them.")
        self.text_processor.process_text_in_segments.assert_not_called()

    async def test_process_with_ai_model(self):
        """Test processing with the AI model"""
        # Configure mock to return a response
        expected_result = "This is the AI model response"
        self.text_processor.process_text_in_segments.return_value = expected_result
        
        # Test with a query that doesn't have a predefined answer
        result = await self.agent.process("Tell me about case analysis", {})
        
        # Should call the AI service and return its response
        self.assertEqual(result, expected_result)
        self.text_processor.process_text_in_segments.assert_called_once()

    async def test_validate_result(self):
        """Test validation of assistant responses"""
        # Valid response
        self.assertTrue(await self.agent.validate("This is a valid response about CaseBuilder"))
        
        # Invalid response (too short)
        self.assertFalse(await self.agent.validate(""))
        self.assertFalse(await self.agent.validate("Short"))
        
        # Invalid response (contains harmful content)
        self.assertFalse(await self.agent.validate("Here's how to exploit a vulnerability"))
        self.assertFalse(await self.agent.validate("The password is 12345"))

if __name__ == '__main__':
    unittest.main()
