#!/usr/bin/env python3
"""
Simple test for the command bus to verify test discovery.
"""

import unittest
import asyncio
from dataclasses import dataclass

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from casebuilder.application.command_bus import CommandBus
from casebuilder.application.commands.command_base import Command
from casebuilder.application.handlers.command_handler import CommandHandler

@dataclass
class SimpleCommand(Command[str]):
    value: str

class SimpleHandler(CommandHandler[str]):
    async def handle(self, command: SimpleCommand) -> str:
        return f"Processed: {command.value}"

class CommandBusBasicTest(unittest.TestCase):
    def setUp(self):
        self.bus = CommandBus()
        self.handler = SimpleHandler()

    def test_registration(self):
        self.bus.register_handler(SimpleCommand, self.handler)
        self.assertIn(SimpleCommand, self.bus._handlers)
        self.assertEqual(self.bus._handlers[SimpleCommand], self.handler)

    async def async_test_dispatch(self):
        self.bus.register_handler(SimpleCommand, self.handler)
        command = SimpleCommand(value="test")
        result = await self.bus.dispatch(command)
        self.assertEqual(result, "Processed: test")

    def test_dispatch(self):
        asyncio.run(self.async_test_dispatch())


if __name__ == "__main__":
    unittest.main()