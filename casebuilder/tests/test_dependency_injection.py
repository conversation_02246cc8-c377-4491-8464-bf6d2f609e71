#!/usr/bin/env python3
"""
Tests for the dependency injection implementation.
"""

import unittest
import sys
import os
from abc import ABC, abstractmethod
from typing import List

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from casebuilder.infrastructure.dependency_injection import (
    ServiceLocator, 
    register, register_instance, get,
    register_service, get_service
)

# Define test interfaces and implementations
class ILogger(ABC):
    @abstractmethod
    def log(self, message: str) -> None:
        pass

class ConsoleLogger(ILogger):
    def __init__(self):
        self.logs: List[str] = []
    
    def log(self, message: str) -> None:
        self.logs.append(message)

class FileLogger(ILogger):
    def __init__(self, filename: str = "test.log"):
        self.filename = filename
        self.logs: List[str] = []
    
    def log(self, message: str) -> None:
        self.logs.append(f"[FILE:{self.filename}] {message}")

class ServiceLocatorTest(unittest.TestCase):
    """
    Unit tests for the ServiceLocator class.
    """
    
    def setUp(self):
        """Set up the test environment."""
        self.locator = ServiceLocator()
    
    def test_type_based_registration(self):
        """Test registering and retrieving services using the type-based API."""
        # Register a type
        self.locator.register(ILogger, ConsoleLogger)
        
        # Get the instance
        logger = self.locator.get(ILogger)
        
        # Check that we got the right type
        self.assertIsInstance(logger, ConsoleLogger)
        
        # Check that we can use the instance
        logger.log("Test message")
        self.assertEqual(logger.logs, ["Test message"])
    
    def test_instance_registration(self):
        """Test registering and retrieving instances using the type-based API."""
        # Create an instance
        console_logger = ConsoleLogger()
        
        # Register the instance
        self.locator.register_instance(ILogger, console_logger)
        
        # Get the instance
        logger = self.locator.get(ILogger)
        
        # Check that we got the same instance
        self.assertIs(logger, console_logger)
    
    def test_string_based_registration(self):
        """Test registering and retrieving services using the string-based API."""
        # Create an instance
        console_logger = ConsoleLogger()
        
        # Register the instance
        self.locator.register_service("Logger", console_logger)
        
        # Get the instance
        logger = self.locator.get_service("Logger")
        
        # Check that we got the same instance
        self.assertIs(logger, console_logger)
    
    def test_mixed_registration(self):
        """Test using both registration styles together."""
        # Register with type-based API
        file_logger = FileLogger("app.log")
        self.locator.register_instance(ILogger, file_logger)
        
        # Register with string-based API
        console_logger = ConsoleLogger()
        self.locator.register_service("ConsoleLogger", console_logger)
        
        # Get both instances
        type_logger = self.locator.get(ILogger)
        string_logger = self.locator.get_service("ConsoleLogger")
        
        # Check that we got the right instances
        self.assertIs(type_logger, file_logger)
        self.assertIs(string_logger, console_logger)
        
        # Check that they work as expected
        type_logger.log("File log")
        string_logger.log("Console log")
        
        self.assertEqual(type_logger.logs, ["[FILE:app.log] File log"])
        self.assertEqual(string_logger.logs, ["Console log"])
    
    def test_factory_registration(self):
        """Test registering and using a factory function."""
        # Register a factory
        self.locator.register(ILogger, lambda: FileLogger("factory.log"))
        
        # Get the instance
        logger = self.locator.get(ILogger)
        
        # Check that we got the right type
        self.assertIsInstance(logger, FileLogger)
        self.assertEqual(logger.filename, "factory.log")
    
    def test_has_methods(self):
        """Test the has and has_service methods."""
        # Register services
        self.locator.register(ILogger, ConsoleLogger)
        self.locator.register_service("Logger", ConsoleLogger())
        
        # Check that they exist
        self.assertTrue(self.locator.has(ILogger))
        self.assertTrue(self.locator.has_service("Logger"))
        
        # Check that non-existent services return False
        self.assertFalse(self.locator.has(str))
        self.assertFalse(self.locator.has_service("NonExistentService"))
    
    def test_clear(self):
        """Test clearing the service registry."""
        # Register services
        self.locator.register(ILogger, ConsoleLogger)
        self.locator.register_service("Logger", ConsoleLogger())
        
        # Check that they exist
        self.assertTrue(self.locator.has(ILogger))
        self.assertTrue(self.locator.has_service("Logger"))
        
        # Clear the registry
        self.locator.clear()
        
        # Check that they no longer exist
        self.assertFalse(self.locator.has(ILogger))
        self.assertFalse(self.locator.has_service("Logger"))

class GlobalServiceLocatorTest(unittest.TestCase):
    """
    Unit tests for the global convenience functions.
    """
    
    def setUp(self):
        """Clear the global service locator before each test."""
        # Get a reference to the global locator and clear it
        from casebuilder.infrastructure.dependency_injection import _global_locator
        _global_locator.clear()
    
    def test_global_type_registration(self):
        """Test the global register and get functions."""
        # Register a type
        register(ILogger, ConsoleLogger)
        
        # Get the instance
        logger = get(ILogger)
        
        # Check that we got the right type
        self.assertIsInstance(logger, ConsoleLogger)
    
    def test_global_instance_registration(self):
        """Test the global register_instance and get functions."""
        # Create an instance
        console_logger = ConsoleLogger()
        
        # Register the instance
        register_instance(ILogger, console_logger)
        
        # Get the instance
        logger = get(ILogger)
        
        # Check that we got the same instance
        self.assertIs(logger, console_logger)
    
    def test_global_string_registration(self):
        """Test the global register_service and get_service functions."""
        # Create an instance
        console_logger = ConsoleLogger()
        
        # Register the instance
        register_service("Logger", console_logger)
        
        # Get the instance
        logger = get_service("Logger")
        
        # Check that we got the same instance
        self.assertIs(logger, console_logger)


if __name__ == "__main__":
    unittest.main()