#!/usr/bin/env python3
"""
Test for the mediator implementation.
"""

import unittest
import asyncio
from dataclasses import dataclass
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from casebuilder.infrastructure.mediator import Mediator
from casebuilder.application.commands.command_base import Command
from casebuilder.application.queries.query_base import Query
from casebuilder.application.handlers.command_handler import CommandHandler
from casebuilder.application.handlers.query_handler import QueryHandler
from casebuilder.application.errors import ValidationError, ApplicationError

@dataclass
class TestCommand(Command[str]):
    """Test command that returns a string."""
    value: str

class TestCommandHandler(CommandHandler[str]):
    """Test command handler that handles TestCommand."""
    
    async def handle(self, command: TestCommand) -> str:
        if command.value == "error":
            raise ValidationError("Invalid value")
        if command.value == "app_error":
            raise ApplicationError("Application error")
        return f"Handled: {command.value}"

@dataclass
class TestQuery(Query[str]):
    """Test query that returns a string."""
    value: str

class TestQueryHandler(QueryHandler[str]):
    """Test query handler that handles TestQuery."""
    
    async def handle(self, query: TestQuery) -> str:
        if query.value == "error":
            raise ValidationError("Invalid value")
        if query.value == "app_error":
            raise ApplicationError("Application error")
        return f"Queried: {query.value}"

class MediatorTest(unittest.TestCase):
    """
    Unit tests for the Mediator class.
    """
    
    def setUp(self):
        """Set up the test environment."""
        self.mediator = Mediator()
        self.command_handler = TestCommandHandler()
        self.query_handler = TestQueryHandler()
        
        # Register handlers
        self.mediator.register_command_handler(TestCommand, self.command_handler)
        self.mediator.register_query_handler(TestQuery, self.query_handler)
        
        # Define error handlers
        def validation_error_handler(error, request):
            return {
                "success": False,
                "error": {
                    "type": "ValidationError",
                    "message": str(error)
                }
            }
        
        def application_error_handler(error, request):
            return {
                "success": False,
                "error": {
                    "type": "ApplicationError",
                    "message": str(error)
                }
            }
        
        # Register error handlers
        self.mediator.register_error_handler(ValidationError, validation_error_handler)
        self.mediator.register_error_handler(ApplicationError, application_error_handler)
    
    async def async_test_send_command(self):
        """Test sending a command through the mediator."""
        # Create and send command
        command = TestCommand(value="test")
        result = await self.mediator.send(command)
        
        # Check result
        self.assertEqual(result, "Handled: test")
    
    def test_send_command(self):
        """Run the async test for sending a command."""
        asyncio.run(self.async_test_send_command())
    
    async def async_test_send_query(self):
        """Test sending a query through the mediator."""
        # Create and send query
        query = TestQuery(value="test")
        result = await self.mediator.query(query)
        
        # Check result
        self.assertEqual(result, "Queried: test")
    
    def test_send_query(self):
        """Run the async test for sending a query."""
        asyncio.run(self.async_test_send_query())
    
    async def async_test_error_handling_command(self):
        """Test error handling for commands."""
        # Create a command that will raise a validation error
        command = TestCommand(value="error")
        result = await self.mediator.send(command)
        
        # Check result
        self.assertFalse(result["success"])
        self.assertEqual(result["error"]["type"], "ValidationError")
        
        # Create a command that will raise an application error
        command = TestCommand(value="app_error")
        result = await self.mediator.send(command)
        
        # Check result
        self.assertFalse(result["success"])
        self.assertEqual(result["error"]["type"], "ApplicationError")
    
    def test_error_handling_command(self):
        """Run the async test for error handling in commands."""
        asyncio.run(self.async_test_error_handling_command())
    
    async def async_test_error_handling_query(self):
        """Test error handling for queries."""
        # Create a query that will raise a validation error
        query = TestQuery(value="error")
        result = await self.mediator.query(query)
        
        # Check result
        self.assertFalse(result["success"])
        self.assertEqual(result["error"]["type"], "ValidationError")
        
        # Create a query that will raise an application error
        query = TestQuery(value="app_error")
        result = await self.mediator.query(query)
        
        # Check result
        self.assertFalse(result["success"])
        self.assertEqual(result["error"]["type"], "ApplicationError")
    
    def test_error_handling_query(self):
        """Run the async test for error handling in queries."""
        asyncio.run(self.async_test_error_handling_query())


if __name__ == "__main__":
    unittest.main()