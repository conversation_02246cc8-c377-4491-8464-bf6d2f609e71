#!/usr/bin/env python3
"""
Test for the query bus implementation.
"""

import unittest
import asyncio
from dataclasses import dataclass
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from casebuilder.application.query_bus import QueryBus
from casebuilder.application.queries.query_base import Query
from casebuilder.application.handlers.query_handler import QueryHandler

@dataclass
class TestQuery(Query[str]):
    """Test query that returns a string."""
    value: str

class TestQueryHandler(QueryHandler[str]):
    """Test query handler that handles TestQuery."""
    
    async def handle(self, query: TestQuery) -> str:
        return f"Queried: {query.value}"

class QueryBusTest(unittest.TestCase):
    """
    Unit tests for the QueryBus class.
    """
    
    def setUp(self):
        """Set up the test environment."""
        self.query_bus = QueryBus()
        self.handler = TestQueryHandler()
    
    def test_register_handler(self):
        """Test registering a handler for a query type."""
        # Register handler
        self.query_bus.register_handler(TestQuery, self.handler)
        
        # Check that handler is registered
        self.assertIn(TestQuery, self.query_bus._handlers)
        self.assertEqual(self.query_bus._handlers[TestQuery], self.handler)
    
    async def async_test_dispatch_query(self):
        """Test dispatching a query to its handler."""
        # Register handler
        self.query_bus.register_handler(TestQuery, self.handler)
        
        # Create and dispatch query
        query = TestQuery(value="test")
        result = await self.query_bus.dispatch(query)
        
        # Check result
        self.assertEqual(result, "Queried: test")
    
    def test_dispatch_query(self):
        """Run the async test for dispatching a query."""
        asyncio.run(self.async_test_dispatch_query())
    
    async def async_test_dispatch_unregistered_query(self):
        """Test dispatching a query with no registered handler."""
        # Create query
        query = TestQuery(value="test")
        
        # Dispatch query
        with self.assertRaises(ValueError):
            await self.query_bus.dispatch(query)
    
    def test_dispatch_unregistered_query(self):
        """Run the async test for dispatching an unregistered query."""
        asyncio.run(self.async_test_dispatch_unregistered_query())


if __name__ == "__main__":
    unittest.main()