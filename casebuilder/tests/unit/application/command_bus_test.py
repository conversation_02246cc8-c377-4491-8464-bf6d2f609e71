"""
Unit tests for the Command Bus.
"""

import unittest
import asyncio
from dataclasses import dataclass
from typing import Generic, TypeVar

from casebuilder.application.command_bus import CommandBus
from casebuilder.application.commands.command_base import Command, TResult
from casebuilder.application.handlers.command_handler import CommandHandler

# Define test commands and handlers
@dataclass
class TestCommand(Command[str]):
    """Test command that returns a string."""
    message: str

class TestCommandHandler(CommandHandler[str]):
    """Test command handler that handles TestCommand."""
    
    async def handle(self, command: TestCommand) -> str:
        return f"Handled: {command.message}"

@dataclass
class OtherTestCommand(Command[int]):
    """Test command that returns an integer."""
    value: int

class OtherTestCommandHandler(CommandHandler[int]):
    """Test command handler that handles OtherTestCommand."""
    
    async def handle(self, command: OtherTestCommand) -> int:
        return command.value * 2


class CommandBusTest(unittest.TestCase):
    """Unit tests for the CommandBus class."""
    
    def setUp(self):
        """Set up the test environment."""
        self.command_bus = CommandBus()
        self.test_command_handler = TestCommandHandler()
        self.other_test_command_handler = OtherTestCommandHandler()
    
    def test_register_handler(self):
        """Test registering a handler for a command type."""
        # Register handlers
        self.command_bus.register_handler(TestCommand, self.test_command_handler)
        self.command_bus.register_handler(OtherTestCommand, self.other_test_command_handler)
        
        # Check that handlers are registered
        self.assertEqual(len(self.command_bus._handlers), 2)
        self.assertIn(TestCommand, self.command_bus._handlers)
        self.assertIn(OtherTestCommand, self.command_bus._handlers)
        self.assertEqual(self.command_bus._handlers[TestCommand], self.test_command_handler)
        self.assertEqual(self.command_bus._handlers[OtherTestCommand], self.other_test_command_handler)
    
    async def async_test_dispatch_command(self):
        """Async test for dispatching a command to its handler."""
        # Register handlers
        self.command_bus.register_handler(TestCommand, self.test_command_handler)
        self.command_bus.register_handler(OtherTestCommand, self.other_test_command_handler)
        
        # Create commands
        test_command = TestCommand(message="Hello, World!")
        other_test_command = OtherTestCommand(value=42)
        
        # Dispatch commands and get results
        result1 = await self.command_bus.dispatch(test_command)
        result2 = await self.command_bus.dispatch(other_test_command)
        
        # Check results
        self.assertEqual(result1, "Handled: Hello, World!")
        self.assertEqual(result2, 84)
    
    def test_dispatch_command(self):
        """Run the async test for dispatching commands."""
        asyncio.run(self.async_test_dispatch_command())
    
    async def async_test_dispatch_unregistered_command(self):
        """Async test for dispatching a command with no registered handler."""
        # Create a command
        test_command = TestCommand(message="Hello, World!")
        
        # Dispatch the command
        with self.assertRaises(ValueError):
            await self.command_bus.dispatch(test_command)
    
    def test_dispatch_unregistered_command(self):
        """Run the async test for dispatching an unregistered command."""
        asyncio.run(self.async_test_dispatch_unregistered_command())
    
    async def async_test_handler_replacement(self):
        """Async test for replacing a handler for a command type."""
        # Define a replacement handler
        class ReplacementHandler(CommandHandler[str]):
            async def handle(self, command: TestCommand) -> str:
                return f"Replaced: {command.message}"
        
        replacement_handler = ReplacementHandler()
        
        # Register handlers
        self.command_bus.register_handler(TestCommand, self.test_command_handler)
        self.command_bus.register_handler(TestCommand, replacement_handler)
        
        # Create command
        test_command = TestCommand(message="Hello, World!")
        
        # Dispatch command and get result
        result = await self.command_bus.dispatch(test_command)
        
        # Check result
        self.assertEqual(result, "Replaced: Hello, World!")
    
    def test_handler_replacement(self):
        """Run the async test for handler replacement."""
        asyncio.run(self.async_test_handler_replacement())


# Allow running the tests from the command line
if __name__ == '__main__':
    unittest.main()