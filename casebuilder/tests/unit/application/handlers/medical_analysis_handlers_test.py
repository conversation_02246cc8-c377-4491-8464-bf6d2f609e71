"""
Unit tests for the Medical Analysis Command Handlers.
"""

import unittest
import asyncio
from unittest.mock import MagicMock, patch

from casebuilder.application.commands.medical_analysis_commands import (
    DetailedMedicalAnalysisCommand,
    MedicalExpensesAnalysisCommand,
    FutureMedicalExpensesCommand
)
from casebuilder.application.handlers.medical_analysis_handlers import (
    DetailedMedicalAnalysisHandler,
    MedicalExpensesAnalysisHandler,
    FutureMedicalExpensesHandler
)


class MedicalAnalysisHandlersTest(unittest.TestCase):
    """Unit tests for the Medical Analysis Command Handlers."""
    
    def setUp(self):
        """Set up the test environment."""
        # Create mock services
        self.mock_ai_service = MagicMock()
        self.mock_text_processor = MagicMock()
        
        # Configure mock behavior
        async def mock_process_text(model_alias, text, prompt):
            if "detailed" in prompt.lower():
                return "Detailed medical analysis result"
            elif "expenses" in prompt.lower():
                return "Medical expenses analysis result"
            elif "future" in prompt.lower():
                return "Future medical expenses result"
            else:
                return "Generic result"
        
        self.mock_text_processor.process_text_in_segments = mock_process_text
        
        # Create handlers
        self.detailed_medical_handler = DetailedMedicalAnalysisHandler(
            self.mock_ai_service, self.mock_text_processor
        )
        self.medical_expenses_handler = MedicalExpensesAnalysisHandler(
            self.mock_ai_service, self.mock_text_processor
        )
        self.future_expenses_handler = FutureMedicalExpensesHandler(
            self.mock_ai_service, self.mock_text_processor
        )
        
        # Test data
        self.test_document = """
        MEDICAL RECORD
        Patient: John Doe
        Date: 2023-01-01
        
        Diagnosis: Cervical strain, contusion to left shoulder
        Treatment: Pain medication, physical therapy
        Cost: $2,500
        """
    
    async def async_test_detailed_medical_analysis_handler(self):
        """Test the detailed medical analysis handler."""
        # Create command
        command = DetailedMedicalAnalysisCommand(
            document_text=self.test_document,
            client_name="John Doe",
            additional_notes="Patient reports ongoing pain"
        )
        
        # Handle command
        result = await self.detailed_medical_handler.handle(command)
        
        # Check result
        self.assertEqual(result, "Detailed medical analysis result")
        
        # Check that prompt creation method was called
        prompt = self.detailed_medical_handler._create_analysis_prompt(command)
        self.assertIn("John Doe", prompt)
        self.assertIn("Patient reports ongoing pain", prompt)
    
    async def async_test_medical_expenses_handler(self):
        """Test the medical expenses handler."""
        # Create command
        command = MedicalExpensesAnalysisCommand(
            document_text=self.test_document,
            client_name="John Doe"
        )
        
        # Handle command
        result = await self.medical_expenses_handler.handle(command)
        
        # Check result
        self.assertEqual(result, "Medical expenses analysis result")
        
        # Check that prompt creation method was called
        prompt = self.medical_expenses_handler._create_expenses_prompt(command)
        self.assertIn("John Doe", prompt)
        self.assertIn("Medical Provider/Facility", prompt)
    
    async def async_test_future_medical_expenses_handler(self):
        """Test the future medical expenses handler."""
        # Create command
        command = FutureMedicalExpensesCommand(
            document_text=self.test_document,
            client_name="John Doe",
            existing_medical_analysis="Patient has cervical strain"
        )
        
        # Handle command
        result = await self.future_expenses_handler.handle(command)
        
        # Check result
        self.assertEqual(result, "Future medical expenses result")
        
        # Check that prompt creation method was called
        prompt = self.future_expenses_handler._create_future_expenses_prompt(command)
        self.assertIn("John Doe", prompt)
        self.assertIn("Future Medical Expenses Table", prompt)
    
    def test_detailed_medical_analysis_handler(self):
        """Run the async test for detailed medical analysis."""
        asyncio.run(self.async_test_detailed_medical_analysis_handler())
    
    def test_medical_expenses_handler(self):
        """Run the async test for medical expenses."""
        asyncio.run(self.async_test_medical_expenses_handler())
    
    def test_future_medical_expenses_handler(self):
        """Run the async test for future medical expenses."""
        asyncio.run(self.async_test_future_medical_expenses_handler())


# Allow running the tests from the command line
if __name__ == '__main__':
    unittest.main()