/**
 * Preferences configuration panel
 */

import React, { useState } from 'react';
import { Save, RotateCcw, ChevronDown, ChevronRight } from 'lucide-react';
import { usePreferencesStore } from '../../store/preferencesStore';
import { 
  AnalysisType, 
  DetailLevel, 
  ContentEmphasis, 
  AnalysisStyle,
  LetterLength,
  ToneSetting,
  DemandLetterEmphasis,
  DemandLetterSection
} from '../../types/preferences.types';
import { Button } from '../ui/Button';
import { Checkbox } from '../ui/Checkbox';

export const PreferencesPanel: React.FC = () => {
  const [expandedSections, setExpandedSections] = useState({
    analysis: true,
    demandLetter: false
  });

  const {
    preferences,
    analysisTypes,
    isLoading,
    toggleAnalysisType,
    setDetailLevel,
    setContentEmphasis,
    setAnalysisStyle,
    setDemandLetterLength,
    setDemandLetterTone,
    setDemandLetterEmphasis,
    toggleDemandLetterSection,
    updatePreferences,
    resetPreferences
  } = usePreferencesStore();

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleSave = async () => {
    try {
      await updatePreferences({
        analysis: preferences.analysis,
        demand_letter: preferences.demand_letter
      });
    } catch (error) {
      // Error is handled by the store
    }
  };

  const handleReset = async () => {
    try {
      await resetPreferences();
    } catch (error) {
      // Error is handled by the store
    }
  };

  const analysisTypeOptions = analysisTypes ? Object.entries(analysisTypes.analysis_types) : [];

  return (
    <div className="space-y-6">
      {/* Analysis Preferences */}
      <div className="border border-gray-200 rounded-lg">
        <button
          onClick={() => toggleSection('analysis')}
          className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors duration-200"
        >
          <h3 className="text-lg font-medium text-gray-900">Analysis Configuration</h3>
          {expandedSections.analysis ? (
            <ChevronDown className="w-5 h-5 text-gray-400" />
          ) : (
            <ChevronRight className="w-5 h-5 text-gray-400" />
          )}
        </button>

        {expandedSections.analysis && (
          <div className="px-4 pb-4 space-y-6">
            {/* Analysis Types Selection */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">
                Select Analysis Types
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {analysisTypeOptions.map(([type, info]) => (
                  <Checkbox
                    key={type}
                    label={info.name}
                    description={info.description}
                    checked={preferences.analysis.selected_analyses.includes(type as AnalysisType)}
                    onChange={() => toggleAnalysisType(type as AnalysisType)}
                  />
                ))}
              </div>
            </div>

            {/* Analysis Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Detail Level */}
              <div>
                <label className="form-label">Detail Level</label>
                <select
                  value={preferences.analysis.detail_level}
                  onChange={(e) => setDetailLevel(e.target.value)}
                  className="form-input"
                >
                  <option value={DetailLevel.BASIC}>Basic</option>
                  <option value={DetailLevel.STANDARD}>Standard</option>
                  <option value={DetailLevel.COMPREHENSIVE}>Comprehensive</option>
                </select>
              </div>

              {/* Content Emphasis */}
              <div>
                <label className="form-label">Content Emphasis</label>
                <select
                  value={preferences.analysis.content_emphasis}
                  onChange={(e) => setContentEmphasis(e.target.value)}
                  className="form-input"
                >
                  <option value={ContentEmphasis.FACTS}>Facts</option>
                  <option value={ContentEmphasis.LEGAL}>Legal</option>
                  <option value={ContentEmphasis.MEDICAL}>Medical</option>
                  <option value={ContentEmphasis.FINANCIAL}>Financial</option>
                </select>
              </div>

              {/* Analysis Style */}
              <div>
                <label className="form-label">Analysis Style</label>
                <select
                  value={preferences.analysis.analysis_style}
                  onChange={(e) => setAnalysisStyle(e.target.value)}
                  className="form-input"
                >
                  <option value={AnalysisStyle.NARRATIVE}>Narrative</option>
                  <option value={AnalysisStyle.CHRONOLOGICAL}>Chronological</option>
                  <option value={AnalysisStyle.BULLETED}>Bulleted</option>
                  <option value={AnalysisStyle.TABULAR}>Tabular</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Demand Letter Preferences */}
      <div className="border border-gray-200 rounded-lg">
        <button
          onClick={() => toggleSection('demandLetter')}
          className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors duration-200"
        >
          <h3 className="text-lg font-medium text-gray-900">Demand Letter Configuration</h3>
          {expandedSections.demandLetter ? (
            <ChevronDown className="w-5 h-5 text-gray-400" />
          ) : (
            <ChevronRight className="w-5 h-5 text-gray-400" />
          )}
        </button>

        {expandedSections.demandLetter && (
          <div className="px-4 pb-4 space-y-6">
            {/* Letter Sections */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">
                Include Sections
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {Object.values(DemandLetterSection).map((section) => (
                  <Checkbox
                    key={section}
                    label={section.charAt(0).toUpperCase() + section.slice(1).replace('_', ' ')}
                    checked={preferences.demand_letter.sections.includes(section)}
                    onChange={() => toggleDemandLetterSection(section)}
                  />
                ))}
              </div>
            </div>

            {/* Letter Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Letter Length */}
              <div>
                <label className="form-label">Letter Length</label>
                <select
                  value={preferences.demand_letter.length}
                  onChange={(e) => setDemandLetterLength(e.target.value)}
                  className="form-input"
                >
                  <option value={LetterLength.CONCISE}>Concise</option>
                  <option value={LetterLength.STANDARD}>Standard</option>
                  <option value={LetterLength.DETAILED}>Detailed</option>
                </select>
              </div>

              {/* Tone Setting */}
              <div>
                <label className="form-label">Tone</label>
                <select
                  value={preferences.demand_letter.tone}
                  onChange={(e) => setDemandLetterTone(e.target.value)}
                  className="form-input"
                >
                  <option value={ToneSetting.PROFESSIONAL}>Professional</option>
                  <option value={ToneSetting.ASSERTIVE}>Assertive</option>
                  <option value={ToneSetting.DIPLOMATIC}>Diplomatic</option>
                </select>
              </div>

              {/* Content Emphasis */}
              <div>
                <label className="form-label">Content Emphasis</label>
                <select
                  value={preferences.demand_letter.content_emphasis}
                  onChange={(e) => setDemandLetterEmphasis(e.target.value)}
                  className="form-input"
                >
                  <option value={DemandLetterEmphasis.LEGAL}>Legal</option>
                  <option value={DemandLetterEmphasis.MEDICAL}>Medical</option>
                  <option value={DemandLetterEmphasis.FINANCIAL}>Financial</option>
                  <option value={DemandLetterEmphasis.EMOTIONAL}>Emotional</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button
          variant="secondary"
          onClick={handleReset}
          loading={isLoading}
          icon={<RotateCcw className="w-4 h-4" />}
        >
          Reset to Defaults
        </Button>

        <Button
          variant="primary"
          onClick={handleSave}
          loading={isLoading}
          icon={<Save className="w-4 h-4" />}
        >
          Save Preferences
        </Button>
      </div>
    </div>
  );
};
