/**
 * Header component with navigation and user menu
 */

import React from 'react';
import { LogOut, User } from 'lucide-react';
import { useAuthStore } from '../../store/authStore';
import { Button } from '../ui/Button';

export const Header: React.FC = () => {
  const { user, logout, isLoading } = useAuthStore();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      // Error is handled by the store
    }
  };

  return (
    <header className="bg-white shadow-soft border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Title */}
          <div className="flex items-center">
            <div className="flex items-center">
              <div className="bg-black rounded-lg p-2 mr-3">
                <img
                  src="/logowoslogan.png"
                  alt="CaseBuilder"
                  className="h-8"
                />
              </div>
              <div>
                <p className="text-xs text-gray-500">v2.0</p>
              </div>
            </div>
          </div>

          {/* User Menu and Logout */}
          <div className="flex items-center space-x-4">
            {/* User Info */}
            <div className="flex items-center space-x-3 text-sm">
              <div className="flex items-center justify-center w-8 h-8 bg-primary-100 rounded-full">
                <User className="w-4 h-4 text-primary-600" />
              </div>
              <div className="text-left">
                <p className="font-medium text-gray-900">
                  {user?.first_name} {user?.last_name}
                </p>
                <p className="text-xs text-gray-500">@{user?.username}</p>
              </div>
            </div>

            {/* Logout Button */}
            <Button
              variant="secondary"
              size="sm"
              onClick={handleLogout}
              disabled={isLoading}
              className="flex items-center"
            >
              <LogOut className="w-4 h-4 mr-2" />
              {isLoading ? 'Signing out...' : 'Logout'}
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};
