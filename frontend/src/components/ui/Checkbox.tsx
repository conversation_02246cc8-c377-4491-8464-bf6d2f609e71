/**
 * Reusable Checkbox component
 */

import React from 'react';
import { clsx } from 'clsx';
import { Check } from 'lucide-react';

interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string;
  description?: string;
  error?: string;
  indeterminate?: boolean;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  label,
  description,
  error,
  indeterminate = false,
  className,
  id,
  ...props
}) => {
  const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className={clsx('flex items-start', className)}>
      <div className="flex items-center h-5">
        <input
          id={checkboxId}
          type="checkbox"
          className={clsx(
            'h-4 w-4 text-primary-400 border-gray-300 rounded focus:ring-primary-400 focus:ring-2 transition-colors duration-200',
            error && 'border-error-300 focus:ring-error-400',
            props.disabled && 'opacity-50 cursor-not-allowed'
          )}
          {...props}
        />
      </div>
      
      {(label || description) && (
        <div className="ml-3 text-sm">
          {label && (
            <label
              htmlFor={checkboxId}
              className={clsx(
                'font-medium text-gray-700 cursor-pointer',
                props.disabled && 'opacity-50 cursor-not-allowed'
              )}
            >
              {label}
            </label>
          )}
          
          {description && (
            <p className={clsx(
              'text-gray-500',
              props.disabled && 'opacity-50'
            )}>
              {description}
            </p>
          )}
          
          {error && (
            <p className="text-error-600 text-xs mt-1">
              {error}
            </p>
          )}
        </div>
      )}
    </div>
  );
};
