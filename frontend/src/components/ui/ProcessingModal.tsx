/**
 * Processing Modal Component
 * Shows analysis progress with animated indicators
 */

import React from 'react';
import { X, FileText, Brain, CheckCircle, Clock, AlertCircle, Loader2, Zap } from 'lucide-react';

interface ProcessingModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentStep: string;
  progress: number;
  analysisTypes: string[];
  completedAnalyses: string[];
  errorMessage?: string;
}

export const ProcessingModal: React.FC<ProcessingModalProps> = ({
  isOpen,
  onClose,
  currentStep,
  progress,
  analysisTypes,
  completedAnalyses,
  errorMessage
}) => {
  if (!isOpen) return null;

  const getAnalysisDisplayName = (type: string) => {
    const names: { [key: string]: string } = {
      'police_report_summary': 'Police Report Summary',
      'facts_liability': 'Facts & Liability Analysis',
      'in_depth_liability': 'In-Depth Liability Analysis',
      'medical_analysis': 'Medical Analysis',
      'medical_expenses': 'Medical Expenses',
      'future_treatment': 'Future Treatment',
      'analyze_injuries': 'Analyze Injuries',
      'accident_scene': 'Accident Scene',
      'property_damage': 'Property Damage'
    };
    return names[type] || type.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase());
  };

  const getStepIcon = (step: string) => {
    switch (step) {
      case 'starting':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'extracting':
        return (
          <div className="relative">
            <FileText className="w-5 h-5 text-yellow-500" />
            <div className="absolute -top-1 -right-1">
              <div className="w-2 h-2 bg-yellow-400 rounded-full animate-ping"></div>
            </div>
          </div>
        );
      case 'analyzing':
        return (
          <div className="relative">
            <Brain className="w-5 h-5 text-purple-500 animate-pulse" />
            <Zap className="w-3 h-3 text-purple-400 absolute -top-1 -right-1 animate-bounce" />
          </div>
        );
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStepMessage = (step: string) => {
    const firstAnalysisType = analysisTypes[0];
    const analysisName = getAnalysisDisplayName(firstAnalysisType);

    switch (step) {
      case 'starting':
        return `Starting ${analysisName}...`;
      case 'extracting':
        return 'Extracting text from documents...';
      case 'analyzing':
        return `Performing ${analysisName}...`;
      case 'completed':
        return `${analysisName} completed successfully!`;
      case 'error':
        return errorMessage || 'An error occurred during analysis';
      default:
        return 'Processing...';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Processing Analysis
          </h3>
          {currentStep === 'completed' || currentStep === 'error' ? (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          ) : null}
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Current Step */}
          <div className="flex items-center mb-6">
            {getStepIcon(currentStep)}
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">
                {getStepMessage(currentStep)}
              </p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span className="font-medium">Progress</span>
              <span className="font-bold text-blue-600">{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
              <div
                className={`h-3 rounded-full transition-all duration-500 ease-out relative ${
                  currentStep === 'completed'
                    ? 'bg-gradient-to-r from-green-400 to-green-600'
                    : currentStep === 'error'
                    ? 'bg-gradient-to-r from-red-400 to-red-600'
                    : 'bg-gradient-to-r from-blue-400 to-blue-600'
                }`}
                style={{ width: `${progress}%` }}
              >
                {/* Animated shine effect */}
                {currentStep !== 'completed' && currentStep !== 'error' && (
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
                )}
              </div>
            </div>

            {/* Progress Steps Indicator */}
            <div className="flex justify-between mt-3">
              <div className={`flex items-center text-xs ${
                progress >= 0 ? 'text-blue-600 font-medium' : 'text-gray-400'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  progress >= 0 ? 'bg-blue-500' : 'bg-gray-300'
                }`}></div>
                Start
              </div>
              <div className={`flex items-center text-xs ${
                progress >= 50 ? 'text-blue-600 font-medium' : 'text-gray-400'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  progress >= 50 ? 'bg-blue-500' : 'bg-gray-300'
                }`}></div>
                Processing
              </div>
              <div className={`flex items-center text-xs ${
                progress >= 100 ? 'text-green-600 font-medium' : 'text-gray-400'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  progress >= 100 ? 'bg-green-500' : 'bg-gray-300'
                }`}></div>
                Complete
              </div>
            </div>
          </div>

          {/* Analysis Types */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700 flex items-center">
              <Brain className="w-4 h-4 mr-2 text-purple-500" />
              Analysis Queue:
            </h4>
            {analysisTypes.map((type, index) => {
              const isCompleted = completedAnalyses.includes(type);
              const isCurrent = !isCompleted && completedAnalyses.length === index;
              const isPending = !isCompleted && !isCurrent;

              return (
                <div
                  key={type}
                  className={`flex items-center p-3 rounded-lg border transition-all duration-300 ${
                    isCompleted
                      ? 'bg-green-50 border-green-200 shadow-sm'
                      : isCurrent
                      ? 'bg-blue-50 border-blue-200 shadow-md ring-2 ring-blue-100'
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex-shrink-0">
                    {isCompleted ? (
                      <div className="relative">
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"></div>
                      </div>
                    ) : isCurrent ? (
                      <div className="relative">
                        <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
                        <div className="absolute inset-0 w-5 h-5 border-2 border-blue-200 rounded-full animate-pulse"></div>
                      </div>
                    ) : (
                      <Clock className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                  <div className="ml-3 flex-1">
                    <span
                      className={`text-sm block ${
                        isCompleted
                          ? 'text-green-700 font-medium'
                          : isCurrent
                          ? 'text-blue-700 font-medium'
                          : 'text-gray-600'
                      }`}
                    >
                      {getAnalysisDisplayName(type)}
                    </span>
                    {isCurrent && (
                      <span className="text-xs text-blue-500 animate-pulse">
                        Processing...
                      </span>
                    )}
                    {isCompleted && (
                      <span className="text-xs text-green-500">
                        ✓ Completed
                      </span>
                    )}
                    {isPending && (
                      <span className="text-xs text-gray-400">
                        Waiting...
                      </span>
                    )}
                  </div>

                  {/* Progress indicator for current analysis */}
                  {isCurrent && (
                    <div className="ml-2">
                      <div className="w-8 h-1 bg-blue-200 rounded-full overflow-hidden">
                        <div className="w-full h-full bg-blue-500 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Error Message */}
          {currentStep === 'error' && errorMessage && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700">{errorMessage}</p>
            </div>
          )}

          {/* Processing Animation */}
          {currentStep !== 'completed' && currentStep !== 'error' && (
            <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <p className="text-xs text-blue-700 text-center">
                <Clock className="w-3 h-3 inline mr-1" />
                Estimated time: 2-5 minutes depending on document length
              </p>
            </div>
          )}

          {/* Success Animation */}
          {currentStep === 'completed' && (
            <div className="mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <CheckCircle className="w-6 h-6 text-green-500 animate-pulse" />
              </div>
              <p className="text-xs text-green-700 text-center font-medium">
                Analysis completed successfully! Click below to view results.
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        {(currentStep === 'completed' || currentStep === 'error') && (
          <div className="px-6 py-4 border-t border-gray-200">
            <button
              onClick={onClose}
              className={`w-full px-4 py-2 rounded-lg font-medium transition-colors ${
                currentStep === 'completed'
                  ? 'bg-green-500 hover:bg-green-600 text-white'
                  : 'bg-red-500 hover:bg-red-600 text-white'
              }`}
            >
              {currentStep === 'completed' ? 'View Results' : 'Close'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
