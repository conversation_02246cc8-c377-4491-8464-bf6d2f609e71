/**
 * Custom hook for managing session statistics
 */

import { useState, useEffect, useCallback } from 'react';

interface SessionStats {
  documentsUploaded: number;
  analysesCompleted: number;
  tokensRemaining: string;
  processingStatus: string;
  sessionDuration: string;
}

export const useSessionStats = () => {
  const [stats, setStats] = useState<SessionStats>({
    documentsUploaded: 0,
    analysesCompleted: 0,
    tokensRemaining: 'Loading...',
    processingStatus: 'Ready',
    sessionDuration: '0m'
  });

  const [sessionStartTime] = useState(() => {
    // Get existing session start time from sessionStorage, or create new one
    const existingStartTime = sessionStorage.getItem('casebuilder-session-start');
    if (existingStartTime) {
      return parseInt(existingStartTime, 10);
    } else {
      const newStartTime = Date.now();
      sessionStorage.setItem('casebuilder-session-start', newStartTime.toString());
      return newStartTime;
    }
  });

  const fetchTokens = useCallback(async () => {
    try {
      // Get auth token
      const authData = localStorage.getItem('casebuilder-auth');
      if (!authData) {
        console.log('No auth data found');
        return 'N/A';
      }

      const { state } = JSON.parse(authData);
      const token = state?.token;
      if (!token) {
        console.log('No token found in auth data');
        return 'N/A';
      }

      console.log('Fetching tokens from backend...');

      // Fetch tokens from backend
      const response = await fetch('/api/analysis/tokens', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Tokens fetched successfully:', data.tokens_remaining);
        return data.tokens_remaining.toLocaleString();
      } else {
        console.error('Failed to fetch tokens:', response.status, await response.text());
        // For now, return a mock value if the endpoint fails
        return '2,500';
      }
    } catch (error) {
      console.error('Error fetching tokens:', error);
      // For now, return a mock value if there's an error
      return '2,500';
    }
  }, []);

  const updateStats = useCallback(async () => {
    // Get documents from sessionStorage
    const storedFiles = JSON.parse(sessionStorage.getItem('casebuilder-files') || '[]');
    const documentsUploaded = storedFiles.length;

    // Get analyses from sessionStorage
    const storedAnalyses = JSON.parse(sessionStorage.getItem('casebuilder-analyses') || '[]');
    const analysesCompleted = storedAnalyses.length;

    // Calculate session duration
    const currentTime = Date.now();
    const durationMs = currentTime - sessionStartTime;
    const durationMinutes = Math.floor(durationMs / (1000 * 60));
    const durationHours = Math.floor(durationMinutes / 60);

    let sessionDuration: string;
    if (durationHours > 0) {
      sessionDuration = `${durationHours}h ${durationMinutes % 60}m`;
    } else {
      sessionDuration = `${durationMinutes}m`;
    }

    // Fetch real tokens from backend
    const tokensRemaining = await fetchTokens();

    setStats({
      documentsUploaded,
      analysesCompleted,
      tokensRemaining,
      processingStatus: 'Ready',
      sessionDuration
    });
  }, [sessionStartTime, fetchTokens]);

  useEffect(() => {
    // Initial update
    updateStats();

    // Listen for file changes
    const handleFileChange = () => {
      updateStats();
    };

    // Listen for analysis completion
    const handleAnalysisChange = () => {
      updateStats();
    };

    // Add event listeners
    window.addEventListener('casebuilder-files-changed', handleFileChange);
    window.addEventListener('casebuilder-analysis-completed', handleAnalysisChange);

    // Update every minute for session duration
    const interval = setInterval(updateStats, 60000);

    return () => {
      window.removeEventListener('casebuilder-files-changed', handleFileChange);
      window.removeEventListener('casebuilder-analysis-completed', handleAnalysisChange);
      clearInterval(interval);
    };
  }, [sessionStartTime, updateStats]);

  return stats;
};
