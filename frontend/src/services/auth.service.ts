/**
 * Authentication service for CaseBuilder AI
 */

import { LoginRequest, LoginResponse, User } from '../types/auth.types';
import { apiClient } from './api';

// Utility function to get cookie value
const getCookie = (name: string): string | null => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
  return null;
};

export const authService = {
  /**
   * Login user with credentials
   */
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    try {
      const response = await apiClient.post('/auth/login', credentials);

      // Store session ID from cookie if available
      const sessionId = getCookie('session_id');
      if (sessionId) {
        localStorage.setItem('session_id', sessionId);
      }

      return {
        success: true,
        access_token: response.data.access_token,
        token_type: response.data.token_type,
        expires_in: response.data.expires_in,
        user: {
          username: response.data.user.username,
          first_name: response.data.user.first_name,
          last_name: response.data.user.last_name,
          email: response.data.user.email,
          user_id: response.data.user.user_id,
          session_id: sessionId || ''
        },
        message: response.data.message,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      throw {
        response: {
          data: {
            detail: error.response?.data?.detail || 'Login failed'
          }
        }
      };
    }
  },

  /**
   * Logout current user
   */
  logout: async (): Promise<void> => {
    try {
      await apiClient.post('/auth/logout');
      // Clear session ID
      localStorage.removeItem('session_id');
    } catch (error) {
      // Even if logout fails on backend, clear local data
      localStorage.removeItem('session_id');
    }
  },

  /**
   * Get current user information
   */
  getCurrentUser: async (): Promise<{ user: User; message: string }> => {
    const response = await apiClient.get('/auth/me');
    return response.data;
  },

  /**
   * Refresh authentication token
   */
  refreshToken: async (): Promise<LoginResponse> => {
    const response = await apiClient.post<LoginResponse>('/auth/refresh');
    return response.data;
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    try {
      const authData = localStorage.getItem('casebuilder-auth');
      if (!authData) return false;

      const { state } = JSON.parse(authData);
      return !!(state?.token && state?.isAuthenticated);
    } catch {
      return false;
    }
  },

  /**
   * Get stored token
   */
  getToken: (): string | null => {
    try {
      const authData = localStorage.getItem('casebuilder-auth');
      if (!authData) return null;

      const { state } = JSON.parse(authData);
      return state?.token || null;
    } catch {
      return null;
    }
  },

  /**
   * Clear authentication data
   */
  clearAuth: (): void => {
    localStorage.removeItem('casebuilder-auth');
  }
};
