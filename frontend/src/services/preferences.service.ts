/**
 * Preferences service for CaseBuilder AI
 */

import { apiClient } from './api';
import {
  PreferencesResponse,
  PreferencesUpdateRequest,
  AnalysisTypesResponse,
  DEFAULT_USER_PREFERENCES,
  AnalysisType
} from '../types/preferences.types';

// Mock data for development
const mockAnalysisTypes: AnalysisTypesResponse = {
  analysis_types: {
    [AnalysisType.POLICE_REPORT_SUMMARY]: {
      name: "Police Report Summary",
      description: "Generate a comprehensive summary of police report contents",
      category: "police_report"
    },
    [AnalysisType.FACTS_LIABILITY]: {
      name: "Facts & Liability Analysis",
      description: "Analyze facts and determine liability in the case",
      category: "liability"
    },
    [AnalysisType.IN_DEPTH_LIABILITY]: {
      name: "In-Depth Liability Analysis",
      description: "Detailed liability analysis focusing on facts and applicable laws",
      category: "liability"
    },
    [AnalysisType.MEDICAL_ANALYSIS]: {
      name: "Medical Analysis",
      description: "Comprehensive analysis of medical records and treatments",
      category: "medical"
    },
    [AnalysisType.MEDICAL_EXPENSES]: {
      name: "Medical Expenses",
      description: "Calculate and analyze medical expenses from records",
      category: "medical"
    },
    [AnalysisType.FUTURE_TREATMENT]: {
      name: "Future Treatment",
      description: "Analyze future medical treatment needs and costs",
      category: "medical"
    },
    [AnalysisType.ANALYZE_INJURIES]: {
      name: "Analyze Injuries",
      description: "Detailed analysis of injuries and their impact",
      category: "medical"
    },
    [AnalysisType.ACCIDENT_SCENE]: {
      name: "Accident Scene",
      description: "Analyze accident scene details and circumstances",
      category: "accident"
    },
    [AnalysisType.PROPERTY_DAMAGE]: {
      name: "Property Damage",
      description: "Assess and analyze property damage claims",
      category: "property"
    },
    [AnalysisType.GENERATE_DEMAND_LETTER]: {
      name: "Generate Demand Letter",
      description: "Create comprehensive demand letter with all case information",
      category: "generation"
    }
  },
  categories: {
    "police_report": "Police Report Analysis",
    "liability": "Liability Analysis",
    "medical": "Medical Analysis",
    "accident": "Accident Analysis",
    "property": "Property Analysis",
    "generation": "Document Generation"
  }
};

export const preferencesService = {
  /**
   * Get current user preferences
   */
  getPreferences: async (): Promise<PreferencesResponse> => {
    // Mock response for development
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          preferences: DEFAULT_USER_PREFERENCES,
          message: "Preferences retrieved successfully",
          timestamp: new Date().toISOString()
        });
      }, 500);
    });
  },

  /**
   * Update user preferences
   */
  updatePreferences: async (update: PreferencesUpdateRequest): Promise<PreferencesResponse> => {
    // Mock response for development
    return new Promise((resolve) => {
      setTimeout(() => {
        const updatedPreferences = {
          ...DEFAULT_USER_PREFERENCES,
          ...update.analysis && { analysis: { ...DEFAULT_USER_PREFERENCES.analysis, ...update.analysis } },
          ...update.demand_letter && { demand_letter: { ...DEFAULT_USER_PREFERENCES.demand_letter, ...update.demand_letter } }
        };

        resolve({
          success: true,
          preferences: updatedPreferences,
          message: "Preferences updated successfully",
          timestamp: new Date().toISOString()
        });
      }, 500);
    });
  },

  /**
   * Reset preferences to defaults
   */
  resetPreferences: async (): Promise<PreferencesResponse> => {
    // Mock response for development
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          preferences: DEFAULT_USER_PREFERENCES,
          message: "Preferences reset to defaults",
          timestamp: new Date().toISOString()
        });
      }, 500);
    });
  },

  /**
   * Get available analysis types and their descriptions
   */
  getAnalysisTypes: async (): Promise<AnalysisTypesResponse> => {
    // Mock response for development
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockAnalysisTypes);
      }, 300);
    });
  },

  /**
   * Get default preferences
   */
  getDefaultPreferences: async (): Promise<PreferencesResponse> => {
    // Mock response for development
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          preferences: DEFAULT_USER_PREFERENCES,
          message: "Default preferences retrieved successfully",
          timestamp: new Date().toISOString()
        });
      }, 300);
    });
  }
};
