/**
 * Preferences store using Zustand
 */

import { create } from 'zustand';
import { 
  UserPreferences, 
  PreferencesUpdateRequest,
  DEFAULT_USER_PREFERENCES,
  AnalysisType,
  AnalysisTypesResponse
} from '../types/preferences.types';
import { preferencesService } from '../services/preferences.service';
import toast from 'react-hot-toast';

interface PreferencesState {
  preferences: UserPreferences;
  analysisTypes: AnalysisTypesResponse | null;
  isLoading: boolean;
  error: string | null;
}

interface PreferencesActions {
  loadPreferences: () => Promise<void>;
  updatePreferences: (update: PreferencesUpdateRequest) => Promise<void>;
  resetPreferences: () => Promise<void>;
  loadAnalysisTypes: () => Promise<void>;
  toggleAnalysisType: (analysisType: AnalysisType) => void;
  setDetailLevel: (level: string) => void;
  setContentEmphasis: (emphasis: string) => void;
  setAnalysisStyle: (style: string) => void;
  setDemandLetterLength: (length: string) => void;
  setDemandLetterTone: (tone: string) => void;
  setDemandLetterEmphasis: (emphasis: string) => void;
  toggleDemandLetterSection: (section: string) => void;
  clearError: () => void;
}

type PreferencesStore = PreferencesState & PreferencesActions;

export const usePreferencesStore = create<PreferencesStore>((set, get) => ({
  // State
  preferences: DEFAULT_USER_PREFERENCES,
  analysisTypes: null,
  isLoading: false,
  error: null,

  // Actions
  loadPreferences: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await preferencesService.getPreferences();
      
      set({
        preferences: response.preferences,
        isLoading: false,
        error: null
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to load preferences';
      
      set({
        isLoading: false,
        error: errorMessage
      });
      
      toast.error(errorMessage);
    }
  },

  updatePreferences: async (update: PreferencesUpdateRequest) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await preferencesService.updatePreferences(update);
      
      set({
        preferences: response.preferences,
        isLoading: false,
        error: null
      });
      
      toast.success('Preferences updated successfully');
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to update preferences';
      
      set({
        isLoading: false,
        error: errorMessage
      });
      
      toast.error(errorMessage);
      throw error;
    }
  },

  resetPreferences: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await preferencesService.resetPreferences();
      
      set({
        preferences: response.preferences,
        isLoading: false,
        error: null
      });
      
      toast.success('Preferences reset to defaults');
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to reset preferences';
      
      set({
        isLoading: false,
        error: errorMessage
      });
      
      toast.error(errorMessage);
    }
  },

  loadAnalysisTypes: async () => {
    try {
      const response = await preferencesService.getAnalysisTypes();
      
      set({
        analysisTypes: response,
        error: null
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to load analysis types';
      
      set({
        error: errorMessage
      });
      
      console.error('Failed to load analysis types:', error);
    }
  },

  toggleAnalysisType: (analysisType: AnalysisType) => {
    const { preferences } = get();
    const currentAnalyses = preferences.analysis.selected_analyses;
    
    const updatedAnalyses = currentAnalyses.includes(analysisType)
      ? currentAnalyses.filter(type => type !== analysisType)
      : [...currentAnalyses, analysisType];
    
    const updatedPreferences = {
      ...preferences,
      analysis: {
        ...preferences.analysis,
        selected_analyses: updatedAnalyses
      }
    };
    
    set({ preferences: updatedPreferences });
  },

  setDetailLevel: (level: string) => {
    const { preferences } = get();
    
    const updatedPreferences = {
      ...preferences,
      analysis: {
        ...preferences.analysis,
        detail_level: level as any
      }
    };
    
    set({ preferences: updatedPreferences });
  },

  setContentEmphasis: (emphasis: string) => {
    const { preferences } = get();
    
    const updatedPreferences = {
      ...preferences,
      analysis: {
        ...preferences.analysis,
        content_emphasis: emphasis as any
      }
    };
    
    set({ preferences: updatedPreferences });
  },

  setAnalysisStyle: (style: string) => {
    const { preferences } = get();
    
    const updatedPreferences = {
      ...preferences,
      analysis: {
        ...preferences.analysis,
        analysis_style: style as any
      }
    };
    
    set({ preferences: updatedPreferences });
  },

  setDemandLetterLength: (length: string) => {
    const { preferences } = get();
    
    const updatedPreferences = {
      ...preferences,
      demand_letter: {
        ...preferences.demand_letter,
        length: length as any
      }
    };
    
    set({ preferences: updatedPreferences });
  },

  setDemandLetterTone: (tone: string) => {
    const { preferences } = get();
    
    const updatedPreferences = {
      ...preferences,
      demand_letter: {
        ...preferences.demand_letter,
        tone: tone as any
      }
    };
    
    set({ preferences: updatedPreferences });
  },

  setDemandLetterEmphasis: (emphasis: string) => {
    const { preferences } = get();
    
    const updatedPreferences = {
      ...preferences,
      demand_letter: {
        ...preferences.demand_letter,
        content_emphasis: emphasis as any
      }
    };
    
    set({ preferences: updatedPreferences });
  },

  toggleDemandLetterSection: (section: string) => {
    const { preferences } = get();
    const currentSections = preferences.demand_letter.sections;
    
    const updatedSections = currentSections.includes(section as any)
      ? currentSections.filter(s => s !== section)
      : [...currentSections, section as any];
    
    const updatedPreferences = {
      ...preferences,
      demand_letter: {
        ...preferences.demand_letter,
        sections: updatedSections
      }
    };
    
    set({ preferences: updatedPreferences });
  },

  clearError: () => {
    set({ error: null });
  }
}));
