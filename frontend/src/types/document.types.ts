/**
 * Document related types for CaseBuilder AI
 */

export interface Document {
  document_id: string;
  filename: string;
  content_type: string;
  file_size: number;
  document_type?: string;
  processing_status: DocumentStatus;
  uploaded_at?: string;
}

export enum DocumentStatus {
  UPLOADED = "uploaded",
  PROCESSING = "processing",
  COMPLETED = "completed",
  ERROR = "error"
}

export interface DocumentUploadRequest {
  filename: string;
  content_type: string;
  file_size: number;
}

export interface DocumentResponse {
  success: boolean;
  document_id: string;
  filename: string;
  content_type: string;
  file_size: number;
  document_type?: string;
  processing_status: DocumentStatus;
  message: string;
  timestamp: string;
}

export interface DocumentListResponse {
  success: boolean;
  documents: Document[];
  total_count: number;
  message: string;
  timestamp: string;
}

export interface ProgressResponse {
  success: boolean;
  task_id: string;
  status: string;
  progress: number;
  message?: string;
  data?: Record<string, any>;
  started_at: number;
  updated_at: number;
  timestamp: string;
}

// File upload types
export interface FileUpload {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

export interface DropzoneFile extends File {
  preview?: string;
  id?: string;
}

// Supported file types
export const SUPPORTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'image/png': ['.png'],
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/jpg': ['.jpg'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
};

export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

export const FILE_TYPE_LABELS = {
  'application/pdf': 'PDF Document',
  'image/png': 'PNG Image',
  'image/jpeg': 'JPEG Image',
  'image/jpg': 'JPG Image',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document'
};

export const DOCUMENT_TYPE_LABELS = {
  'police_report': 'Police Report',
  'medical_record': 'Medical Record',
  'image': 'Image/Photo',
  'other': 'Other Document',
  'unknown': 'Unknown'
};
