import base64
import io
import aiohttp
import os
from dotenv import load_dotenv

load_dotenv()


def encode_image(image):
    img_byte_arr = io.BytesIO(image.read())
    return base64.b64encode(img_byte_arr.getvalue()).decode('utf-8')

async def process_image_with_gpt4o(image, instruction="Describe the contents of this image"):
    base64_image = encode_image(image)

    payload = {
        "model": "gpt-4o-2024-05-13",
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": instruction},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}"
                        }
                    }
                ]
            }
        ],
        "max_tokens": 4096
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"
    }

    async with aiohttp.ClientSession() as session:
        async with session.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload) as response:
            if response.status == 200:
                response_json = await response.json()
                choices = response_json.get('choices', [])
                if choices:
                    return choices[0]['message']['content']
                else:
                    return "No response generated"
            else:
                return "Error: " + await response.text()

async def generate_content_with_gpt4o(extracted_text, prompt):
    payload = {
        "model": "gpt-4o-mini",  # Using gpt-4o-mini
        "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": extracted_text}
        ],
        "max_tokens": 4096
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"
    }
    async with aiohttp.ClientSession() as session:
        async with session.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload) as response:
            if response.status == 200:
                response_json = await response.json()
                choices = response_json.get('choices', [])
                if choices:
                    return choices[0]['message']['content']
                else:
                    return "No response generated"
            else:
                return "Error: " + await response.text()

async def generate_content_with_reasoning(
    extracted_text: str,
    prompt: str,
    model: str = "o4-mini",
    reasoning_effort: str = "high",
) -> str:

    payload = {
        "model": model,
        "reasoning_effort": reasoning_effort,
        "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": extracted_text}
        ],
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(
            "https://api.openai.com/v1/chat/completions",
            headers=headers,
            json=payload
        ) as response:
            if response.status == 200:
                response_json = await response.json()
                choices = response_json.get('choices', [])
                if choices:
                    return choices[0]['message']['content']
                else:
                    return "No response generated."
            else:
                return "Error: " + await response.text()
