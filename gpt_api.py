import json
import aiohttp
import os
from dotenv import load_dotenv

load_dotenv()

MODEL_CONFIG = {
    "gpt4o": {
        "model_name": "gpt-4o",
        "use_system_prompt": True,
        "use_max_completion_tokens": False,
        "reasoning_effort": None,
    },
    "gpt4omini": {
        "model_name": "gpt-4o-mini",
        "use_system_prompt": True,
        "use_max_completion_tokens": False,
        "reasoning_effort": None,
    },

    # o4-mini model (new addition)
    "o4-mini": {
        "model_name": "o4-mini",
        "use_system_prompt": True,
        "use_max_completion_tokens": False,
        "reasoning_effort": None,
    },

    # Legacy model aliases (all OpenAI models)
    "o1": {
        "model_name": "o4-mini",  # Updated to use o4-mini
        "use_system_prompt": True,
        "use_max_completion_tokens": False,
        "reasoning_effort": None,
    },
    "o3": {
        "model_name": "o4-mini",  # Updated to use o4-mini
        "use_system_prompt": True,
        "use_max_completion_tokens": False,
        "reasoning_effort": None,
    }
}

async def chat_with_model(
    model_alias: str,
    system_prompt: str,
    user_content: str,
    max_tokens: int = 200000
) -> str:
    """
    Llama al endpoint de OpenAI con diferentes configuraciones,
    unificando gpt4, o1 y o3 en una sola función.

    :param model_alias: "gpt4o", "o1", o "o3"
    :param system_prompt: Texto que irá como 'system' (o se puede combinar para o1, o3)
    :param user_content: Texto que irá como 'user'
    :param max_tokens: Límite de tokens para la respuesta; se usará como 'max_tokens'
                       o 'max_completion_tokens' según el modelo
    :return: Contenido de la respuesta del modelo o string con error
    """

    config = MODEL_CONFIG.get(model_alias)
    if not config:
        return f"Error: Unknown model alias '{model_alias}'."

    model_name = config["model_name"]
    messages = []

    if config["use_system_prompt"]:
        messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": user_content})
    else:
        combined_content = f"{system_prompt}\n\n{user_content}"
        messages.append({"role": "user", "content": combined_content})

    data = {
        "model": model_name,
        "messages": messages,
    }

    if config["use_max_completion_tokens"]:
        data["max_completion_tokens"] = int(max_tokens)
    else:
        data["max_tokens"] = int(max_tokens)

    if config["reasoning_effort"]:
        data["reasoning_effort"] = config["reasoning_effort"]

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(
            "https://api.openai.com/v1/chat/completions",
            headers=headers,
            data=json.dumps(data)
        ) as response:
            if response.status == 200:
                response_json = await response.json()
                choices = response_json.get("choices", [])
                if choices:
                    return choices[0]["message"]["content"]
                else:
                    return "No response generated"
            else:
                error_text = await response.text()
                return f"Error: {error_text}"

