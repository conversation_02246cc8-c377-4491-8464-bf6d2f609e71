import base64
import io
import aiohttp
import os
from pdf2image import convert_from_bytes
import asyncio
from PIL import Image
from dotenv import load_dotenv

load_dotenv()

def encode_image_to_base64(image: Image.Image, max_width=1200, max_height=1200) -> str:
    # Make a copy to avoid modifying the original
    img_copy = image.copy()

    # Convert to grayscale for better OCR and smaller size
    if img_copy.mode != 'L':  # 'L' is grayscale mode
        img_copy = img_copy.convert('L')

    # Enhance contrast for better OCR
    from PIL import ImageEnhance
    enhancer = ImageEnhance.Contrast(img_copy)
    img_copy = enhancer.enhance(1.5)  # Increase contrast by 50%

    # Apply additional sharpening for better OCR
    enhancer = ImageEnhance.Sharpness(img_copy)
    img_copy = enhancer.enhance(1.3)  # Increase sharpness by 30%

    # Resize image if necessary
    img_copy.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)

    # Convert to base64 with JPEG format (smaller than PNG for documents)
    buffered = io.BytesIO()
    img_copy.save(buffered, format="JPEG", quality=75)
    return base64.b64encode(buffered.getvalue()).decode('utf-8')

def estimate_tokens(image: Image.Image) -> int:
    width, height = image.size
    tile_size = 512
    num_tiles = (max(width // tile_size, 1)) * (max(height // tile_size, 1))
    base_tokens = 85
    tokens_per_tile = 170
    total_tokens = base_tokens + tokens_per_tile * num_tiles
    return min(total_tokens, 128000)

async def extract_text_from_image(
    image_base64: str,
    session: aiohttp.ClientSession,
    max_tokens: int,
    prompt_text: str = None,
    model_name: str = "gpt-4o-mini"
) -> str:

    if prompt_text is None:
        prompt_text = (
            "You are an AI assistant tasked with extracting textual information from legal documents.\n\n"
            "- Focus on extracting main content such as incident details, medical findings, treatments, billing items,\n"
            "  and other critical information for legal analysis.\n"
            "- Omit irrelevant sections like headers, footers, page numbers, and repeated content.\n\n"
            "Provide the extracted text in its raw form without adding any summaries or commentary."
        )
    payload = {
        "model": model_name,
        "messages": [{
            "role": "user",
            "content": [
                {"type": "text", "text": prompt_text},
                {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}}
            ]
        }],
        "max_tokens": max_tokens,
        "temperature": 0
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"
    }

    try:
        async with session.post(
            "https://api.openai.com/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=60  # Add explicit timeout
        ) as response:
            if response.status == 200:
                response_json = await response.json()
                choices = response_json.get('choices', [])
                if choices and choices[0].get('message', {}).get('content'):
                    return choices[0]['message']['content']
                else:
                    print(f"Warning: No valid content in API response: {response_json}")
                    return "No valid content in response"
            else:
                error_message = await response.text()
                print(f"API Error (status {response.status}): {error_message}")
                return f"Error: API returned status {response.status}"
    except asyncio.TimeoutError:
        print("API request timed out")
        return "Error: API request timed out"
    except aiohttp.ClientError as e:
        print(f"Network error: {str(e)}")
        return f"Error: Network issue - {str(e)}"
    except Exception as e:
        print(f"Unexpected error in extract_text_from_image: {str(e)}")
        return f"Error: {str(e)}"

async def process_pdf(file) -> str:
    file.seek(0)
    pdf_content = file.read()

    # Use simplified settings for PDF conversion to ensure compatibility
    images = convert_from_bytes(
        pdf_content,
        dpi=100,  # Lower DPI for faster processing
        thread_count=4,  # Use multiple threads for conversion
        use_cropbox=True,  # Use cropbox instead of mediabox for slightly smaller images
        grayscale=True  # Convert to grayscale to reduce size and improve OCR
    )

    # Process in batches for better memory management
    batch_size = 20  # Process 20 pages at a time
    page_batches = [images[i:i + batch_size] for i in range(0, len(images), batch_size)]

    # Set up session with timeout
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
        all_text_segments = []
        total_batches = len(page_batches)

        # Process each batch
        for batch_idx, batch in enumerate(page_batches):
            print(f"Processing batch {batch_idx+1}/{total_batches} ({len(batch)} pages)")

            # Determine concurrency based on batch size
            max_concurrency = min(12, len(batch))
            semaphore = asyncio.Semaphore(max_concurrency)

            async def process_image_with_semaphore(image, idx):
                async with semaphore:
                    base64_img = encode_image_to_base64(image)
                    max_tokens = estimate_tokens(image)
                    try:
                        result = await extract_text_from_image(base64_img, session, max_tokens)
                        print(f"Completed page {idx+1} in batch {batch_idx+1}")
                        return result
                    except Exception as e:
                        print(f"Error processing page {idx+1} in batch {batch_idx+1}: {e}")
                        # Retry once
                        try:
                            await asyncio.sleep(2)
                            result = await extract_text_from_image(base64_img, session, max_tokens)
                            print(f"Retry successful for page {idx+1} in batch {batch_idx+1}")
                            return result
                        except:
                            return f"[Error processing page {idx+1} in batch {batch_idx+1}]"

            # Create tasks for current batch
            tasks = [
                process_image_with_semaphore(image, i)
                for i, image in enumerate(batch)
            ]

            # Process current batch in parallel
            batch_results = await asyncio.gather(*tasks)
            batch_text = ' '.join(batch_results)
            all_text_segments.append(batch_text)

            # Free up memory
            tasks.clear()

        # Combine all segments
        final_text = ' '.join(all_text_segments)

    return final_text

async def process_pdf_with_metadata(file) -> str:
    file.seek(0)
    pdf_content = file.read()

    # Use simplified settings for PDF conversion to ensure compatibility
    images = convert_from_bytes(
        pdf_content,
        dpi=100,  # Lower DPI for faster processing
        thread_count=4,  # Use multiple threads for conversion
        use_cropbox=True,  # Use cropbox instead of mediabox for slightly smaller images
        grayscale=True  # Convert to grayscale to reduce size and improve OCR
    )

    # Process in batches for better memory management
    batch_size = 20  # Process 20 pages at a time
    total_pages = len(images)

    # Create page number and image pairs
    page_items = [(i+1, images[i]) for i in range(total_pages)]
    page_batches = [page_items[i:i + batch_size] for i in range(0, len(page_items), batch_size)]

    # Set up session with timeout
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
        all_results = []
        total_batches = len(page_batches)

        # Process each batch
        for batch_idx, batch in enumerate(page_batches):
            print(f"Processing metadata batch {batch_idx+1}/{total_batches} ({len(batch)} pages)")

            # Determine concurrency based on batch size
            max_concurrency = min(12, len(batch))
            semaphore = asyncio.Semaphore(max_concurrency)

            async def process_page_with_semaphore(page_number, image):
                async with semaphore:
                    base64_img = encode_image_to_base64(image)
                    max_tokens = estimate_tokens(image)
                    try:
                        text = await extract_text_from_image(base64_img, session, max_tokens)
                        print(f"Completed metadata page {page_number} in batch {batch_idx+1}")
                        return page_number, text
                    except Exception as e:
                        print(f"Error processing metadata page {page_number} in batch {batch_idx+1}: {e}")
                        # Retry once
                        try:
                            await asyncio.sleep(2)
                            text = await extract_text_from_image(base64_img, session, max_tokens)
                            print(f"Retry successful for metadata page {page_number} in batch {batch_idx+1}")
                            return page_number, text
                        except Exception as retry_e:
                            return page_number, f"Error processing page {page_number}: {str(retry_e)}"

            # Create tasks for current batch
            tasks = [
                process_page_with_semaphore(page_number, image)
                for page_number, image in batch
            ]

            # Process current batch in parallel
            batch_results = await asyncio.gather(*tasks)
            all_results.extend(batch_results)

            # Free up memory
            tasks.clear()

        # Sort by page number
        all_results.sort(key=lambda x: x[0])

        # Format results with page numbers
        all_text_segments = [f"[Page {page}] {text}" for page, text in all_results]

        # Join all segments with newlines
        final_text = "\n\n".join(all_text_segments)

    return final_text
