"""
Utility functions for handling tables in Streamlit.
"""

import re
import streamlit as st

def extract_markdown_tables(text):
    """
    Extract markdown tables from text.

    Args:
        text (str): Text containing markdown tables

    Returns:
        list: List of extracted markdown tables
    """
    # Pattern to match markdown tables
    table_pattern = r'(\|[^\n]+\|\n\|[-:| ]+\|\n(?:\|[^\n]+\|\n)+)'

    # Find all tables in the text
    tables = re.findall(table_pattern, text)

    # If no tables found, check if there might be ICD codes mentioned in the text
    # and try to extract them to create a table
    if not tables and ("ICD" in text or "icd" in text.lower()):
        # Try to find ICD codes in the text using a pattern
        icd_pattern = r'(?:ICD(?:-10)? code[s]?:?\s*|diagnosis code[s]?:?\s*)((?:[A-Z]\d+\.\d+(?:X{1,4})?(?:A|D)?(?:,\s*|\s+and\s+|\s+|\s*\(|\s*\)|\s*\[|\s*\]|\s*;|\s*\.|$))+)'
        icd_matches = re.findall(icd_pattern, text, re.IGNORECASE)

        if icd_matches:
            # Extract individual codes
            all_codes = []
            for match in icd_matches:
                codes = re.findall(r'([A-Z]\d+\.\d+(?:X{1,4})?(?:A|D)?)', match)
                all_codes.extend(codes)

            # If we found codes, create a markdown table
            if all_codes:
                # Add a clear header to identify this as an ICD table
                table_lines = ["| Treatment/Injury | ICD-10 Code | Page Reference |"]
                table_lines.append("| --- | --- | --- |")

                # Try to associate codes with conditions by looking for context
                for code in all_codes:
                    # Enhanced pattern to find conditions associated with ICD codes
                    condition_patterns = [
                        # Pattern 1: Look for condition followed by ICD code
                        r'([\w\s,]+(?:strain|sprain|pain|injury|trauma|fracture|contusion|disorder|syndrome|disease|condition))(?:[^.]*?ICD[^.]*?' + re.escape(code) + ')',
                        # Pattern 2: Look for ICD code followed by condition
                        r'' + re.escape(code) + r'[^.]*?(?:for|of)[^.]*?([\w\s,]+(?:strain|sprain|pain|injury|trauma|fracture|contusion|disorder|syndrome|disease|condition))',
                        # Pattern 3: Look for diagnosis followed by ICD code
                        r'(?:diagnosed with|diagnosis of)\s+([\w\s,]+)(?:[^.]*?ICD[^.]*?' + re.escape(code) + ')',
                        # Pattern 4: Simple proximity search
                        r'([\w\s,]+(?:strain|sprain|pain|injury|trauma|fracture|contusion|disorder|syndrome|disease|condition))[^.]{0,50}' + re.escape(code)
                    ]

                    condition = "Unknown condition"
                    for pattern in condition_patterns:
                        condition_match = re.search(pattern, text, re.IGNORECASE)
                        if condition_match:
                            # Use the first group that contains text
                            for group in condition_match.groups():
                                if group and group.strip():
                                    condition = group.strip()
                                    break
                            break

                    page_ref = "N/A"

                    # Look for page reference with enhanced pattern
                    page_patterns = [
                        r'' + re.escape(code) + r'[^.]*?\[Page\s+(\d+)\]',
                        r'\[Page\s+(\d+)\][^.]*?' + re.escape(code),
                        r'page\s+(\d+)[^.]*?' + re.escape(code),
                        r'' + re.escape(code) + r'[^.]*?page\s+(\d+)'
                    ]

                    for pattern in page_patterns:
                        page_match = re.search(pattern, text, re.IGNORECASE)
                        if page_match:
                            page_ref = page_match.group(1)
                            break

                    table_lines.append(f"| {condition.strip()} | {code} | {page_ref} |")

                # Join the table lines without adding the title marker
                tables.append("\n".join(table_lines))

    return tables

def parse_markdown_table(markdown_table):
    """
    Parse a markdown table into headers and rows.

    Args:
        markdown_table (str): Markdown table string

    Returns:
        tuple: (headers, rows) where headers is a list of column names and rows is a list of lists
    """
    # Split the table into lines
    lines = markdown_table.strip().split('\n')

    # Check if the first line is a table header marker (like "ICD DIAGNOSIS CODES TABLE")
    start_index = 0
    if lines[0].strip() and not lines[0].strip().startswith('|'):
        # This is a header line, not part of the table
        start_index = 1

    # Extract headers (first line of the actual table)
    headers = [cell.strip() for cell in lines[start_index].split('|')[1:-1]]

    # Skip the separator line (second line of the actual table)

    # Extract data rows
    rows = []
    for line in lines[(start_index + 2):]:
        if line.strip() and line.strip().startswith('|'):
            row = [cell.strip() for cell in line.split('|')[1:-1]]
            rows.append(row)

    return headers, rows

def display_markdown_tables(text):
    """
    Display markdown tables from text using Streamlit's markdown component.

    Args:
        text (str): Text containing markdown tables

    Returns:
        str: Text with tables removed (to be displayed separately)
    """
    # Check for ICD table markers in the text
    has_icd_table = "ICD DIAGNOSIS CODES TABLE" in text or "PROJECTED ICD DIAGNOSIS CODES TABLE" in text

    # If we have an ICD table marker but no markdown table, try to extract it manually
    if has_icd_table and "|" in text:
        # Find the ICD table section
        icd_section_pattern = r'(ICD DIAGNOSIS CODES TABLE|PROJECTED ICD DIAGNOSIS CODES TABLE)[\s\S]*?(\|.*\|[\s\S]*?)(?:\n\n|\Z)'
        icd_section_match = re.search(icd_section_pattern, text)

        if icd_section_match:
            icd_section = icd_section_match.group(0)

            # Extract the table lines
            table_lines = []
            for line in icd_section.split('\n'):
                if line.strip() and '|' in line:
                    table_lines.append(line)

            if len(table_lines) >= 3:  # Header, separator, and at least one data row
                # Create a proper markdown table
                icd_table = '\n'.join(table_lines)

                # Replace the original section with our cleaned table
                text = text.replace(icd_section_match.group(0), "ICD DIAGNOSIS CODES TABLE\n" + icd_table + "\n\n")

    # Extract tables
    tables = extract_markdown_tables(text)

    if not tables:
        return text, []

    # Parse tables
    parsed_tables = []
    for table in tables:
        try:
            # Check if this is an ICD table
            is_icd_table = "ICD DIAGNOSIS CODES TABLE" in table or "PROJECTED ICD DIAGNOSIS CODES TABLE" in table

            headers, rows = parse_markdown_table(table)
            parsed_tables.append((headers, rows, is_icd_table))
        except Exception as e:
            st.warning(f"Error parsing table: {str(e)}")

    # Remove tables from text
    text_without_tables = text
    for table in tables:
        text_without_tables = text_without_tables.replace(table, '')

    # Clean up any double newlines created by table removal
    text_without_tables = re.sub(r'\n\s*\n\s*\n', '\n\n', text_without_tables)

    return text_without_tables, parsed_tables

def create_html_table(headers, rows):
    """
    Create an HTML table from headers and rows.

    Args:
        headers (list): List of column headers
        rows (list): List of rows, where each row is a list of cells

    Returns:
        str: HTML table
    """
    html = '<table style="width:100%; border-collapse: collapse;">'

    # Add header row
    html += '<thead><tr>'
    for header in headers:
        html += f'<th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #22d3ee; color: white;">{header}</th>'
    html += '</tr></thead>'

    # Add data rows - using a single consistent background color instead of alternating
    html += '<tbody>'
    for row in rows:
        # Using a consistent light background for all rows
        html += f'<tr style="background-color: rgba(255, 255, 255, 0.1);">'
        for cell in row:
            html += f'<td style="border: 1px solid #ddd; padding: 8px; text-align: left; color: white;">{cell}</td>'
        html += '</tr>'
    html += '</tbody></table>'

    return html

def extract_icd_codes_directly(text):
    """
    Extract ICD codes directly from text, even if they're not in a proper table format.

    Args:
        text (str): Text containing ICD codes

    Returns:
        tuple: (headers, rows, found) where headers is a list of column names,
               rows is a list of lists, and found is a boolean indicating if codes were found
    """
    # Default headers for ICD table
    headers = ["Treatment/Injury", "ICD-10 Code", "Page Reference"]
    rows = []
    found = False

    # Check if text contains ICD codes
    if not ("ICD" in text or "icd" in text.lower()):
        return headers, rows, found

    # Pattern to find ICD codes
    icd_pattern = r'([A-Z]\d+\.\d+(?:X{1,4})?(?:A|D)?)'
    icd_codes = re.findall(icd_pattern, text)

    # Remove duplicates while preserving order
    seen = set()
    unique_codes = [code for code in icd_codes if not (code in seen or seen.add(code))]

    if unique_codes:
        found = True
        # For each code, try to find associated condition and page reference
        for code in unique_codes:
            try:
                # Look for condition near the code (within 100 characters)
                context = re.search(r'(.{0,100})' + re.escape(code) + r'(.{0,100})', text)
                if context:
                    before = context.group(1)
                    after = context.group(2)

                    # Try to extract condition
                    condition_patterns = [
                        # Look for diagnosis followed by ICD code
                        r'(?:diagnosed with|diagnosis of)\s+([\w\s,]+)(?:[^.]*?' + re.escape(code) + ')',
                        # Look for condition followed by ICD code
                        r'([\w\s,]+(?:strain|sprain|pain|injury|trauma|fracture|contusion|disorder|syndrome|disease|condition))(?:[^.]*?' + re.escape(code) + ')',
                        # Look for ICD code followed by condition
                        r'' + re.escape(code) + r'[^.]*?(?:for|of)[^.]*?([\w\s,]+(?:strain|sprain|pain|injury|trauma|fracture|contusion|disorder|syndrome|disease|condition))',
                        # General medical terms before code
                        r'([\w\s,]+(?:cervical|thoracic|lumbar|shoulder|knee|ankle|wrist|elbow|hip|back|neck|head|migraine|headache|dizziness|vertigo|nausea|vomiting|insomnia|anxiety|depression))(?:[^.]*?' + re.escape(code) + ')'
                    ]

                    condition = "Unknown condition"
                    for pattern in condition_patterns:
                        match = re.search(pattern, before + " " + after, re.IGNORECASE)
                        if match and match.group(1).strip():
                            condition = match.group(1).strip()
                            break

                    # Try to extract page reference
                    page_ref = "N/A"
                    page_patterns = [
                        r'[Pp]age\s+(\d+)',
                        r'\[Page\s+(\d+)\]',
                        r'\(Page\s+(\d+)\)'
                    ]

                    for pattern in page_patterns:
                        page_match = re.search(pattern, before + after)
                        if page_match:
                            page_ref = f"Page {page_match.group(1)}"
                            break

                    rows.append([condition, code, page_ref])
            except Exception as e:
                # If there's an error processing this code, just add it with minimal info
                rows.append(["Unknown condition", code, "N/A"])

    return headers, rows, found

def display_text_with_tables(text, style='tabular'):
    """
    Display text with proper handling of tables based on style preference.

    Args:
        text (str): Text to display, potentially containing markdown tables
        style (str): Style preference ('tabular', 'narrative', etc.)

    Returns:
        None
    """
    # Check if there are any ICD code tables in the text
    has_icd_table = "ICD DIAGNOSIS CODES TABLE" in text or "PROJECTED ICD DIAGNOSIS CODES TABLE" in text

    # We'll keep the marker for detection purposes but remove it for display later
    # This is handled in the specific extraction functions

    # Direct extraction of ICD table if present
    if has_icd_table:
        # First, try to extract the ICD table directly
        # This pattern is more flexible to handle various formats
        icd_table_patterns = [
            # Pattern 1: Standard markdown table format
            r'ICD DIAGNOSIS CODES TABLE\s*\n((?:\|[^\n]+\|\n)+)',
            # Pattern 2: More flexible pattern that can handle tables with extra newlines
            r'ICD DIAGNOSIS CODES TABLE\s*\n\s*((?:\|[^\n]+\|\n\s*)+)',
            # Pattern 3: Even more flexible pattern for tables with inconsistent formatting
            r'ICD DIAGNOSIS CODES TABLE[\s\S]*?((?:\|[^\n]+\|[\s\S]*?){3,}?)(?:\n\n|\Z)'
        ]

        icd_table_match = None
        for pattern in icd_table_patterns:
            match = re.search(pattern, text)
            if match:
                icd_table_match = match
                break

        if icd_table_match:
            # We found a markdown table after the ICD header
            icd_table_text = icd_table_match.group(1)

            # Extract the table rows
            rows = []
            headers = []

            # Clean up the table text
            clean_table_text = re.sub(r'\n\s*\n', '\n', icd_table_text)
            lines = [line for line in clean_table_text.strip().split('\n') if line.strip() and '|' in line]

            # Process each line
            header_found = False
            separator_found = False

            for line in lines:
                if '|' in line:
                    cells = [cell.strip() for cell in line.split('|')[1:-1]]

                    # Skip empty lines or lines with empty cells
                    if not cells or all(not cell for cell in cells):
                        continue

                    # Check if this is a separator line (contains only -, :, or |)
                    if not header_found:
                        # This is the header row
                        headers = cells
                        header_found = True
                    elif not separator_found and all(re.match(r'^[-:]+$', cell) for cell in cells):
                        # This is the separator line
                        separator_found = True
                    elif separator_found or len(rows) > 0:
                        # This is a data row
                        rows.append(cells)

            # If we didn't find headers or rows properly, try a simpler approach
            if not headers or not rows:
                # Reset and try again with a simpler approach
                headers = []
                rows = []
                data_started = False

                for line in lines:
                    if '|' in line:
                        cells = [cell.strip() for cell in line.split('|')[1:-1]]

                        # Skip empty lines
                        if not cells or all(not cell for cell in cells):
                            continue

                        if not headers:
                            # First non-empty line is headers
                            headers = cells
                        elif not data_started and all(re.match(r'^[-:]+$', cell) for cell in cells):
                            # This is the separator line
                            data_started = True
                        else:
                            # This is a data row
                            rows.append(cells)

            # Display the text without the ICD table
            # First, remove the entire table including the title
            text_without_icd = text.replace(icd_table_match.group(0), '')

            # Also remove any standalone "ICD DIAGNOSIS CODES TABLE" text that might remain
            text_without_icd = re.sub(r'ICD DIAGNOSIS CODES TABLE\s*', '', text_without_icd)

            if style.lower() == 'tabular':
                # For tabular style, display the text content as markdown
                if text_without_icd.strip():
                    st.markdown(text_without_icd)
            else:
                # For non-tabular styles, display the text content in a text area
                if text_without_icd.strip():
                    st.text_area(
                        label="Analysis Text",
                        value=text_without_icd,
                        height=400,
                        label_visibility="collapsed",
                        key=f"text_area_{hash(text_without_icd)}"
                    )

            # Display the ICD table using st.table
            if headers and rows:
                st.subheader("ICD Diagnosis Codes")

                # Create an HTML table for better display
                html_table = create_html_table(headers, rows)
                st.markdown(html_table, unsafe_allow_html=True)

                # We've handled the ICD table, so return
                return

        # If we couldn't extract the table with regex, try a more direct approach
        # This is a last resort for when the table format is really non-standard
        if "ICD DIAGNOSIS CODES TABLE" in text:
            # Find the position of the ICD table header
            icd_header_pos = text.find("ICD DIAGNOSIS CODES TABLE")

            # Extract all lines after the header
            text_after_header = text[icd_header_pos:]

            # Look for lines with pipe characters (|) which indicate table rows
            table_lines = []
            for line in text_after_header.split('\n'):
                if '|' in line:
                    table_lines.append(line)
                elif table_lines and not line.strip():
                    # Empty line after we've started collecting table lines means end of table
                    break

            if len(table_lines) >= 3:  # Need at least header, separator, and one data row
                # Process the table lines
                headers = []
                rows = []

                # First line is headers
                if '|' in table_lines[0]:
                    headers = [cell.strip() for cell in table_lines[0].split('|')[1:-1]]

                # Skip the separator line (second line)

                # Process data rows (third line onwards)
                for line in table_lines[2:]:
                    if '|' in line:
                        cells = [cell.strip() for cell in line.split('|')[1:-1]]
                        rows.append(cells)

                if headers and rows:
                    # Display the text without the ICD table
                    text_without_table = text.replace('\n'.join(table_lines), '')

                    # Also remove any standalone "ICD DIAGNOSIS CODES TABLE" text that might remain
                    text_without_table = re.sub(r'ICD DIAGNOSIS CODES TABLE\s*', '', text_without_table)

                    if style.lower() == 'tabular':
                        # For tabular style, display the text content as markdown
                        if text_without_table.strip():
                            st.markdown(text_without_table)
                    else:
                        # For non-tabular styles, display the text content in a text area
                        if text_without_table.strip():
                            st.text_area(
                                label="Analysis Text",
                                value=text_without_table,
                                height=400,
                                label_visibility="collapsed",
                                key=f"text_area_{hash(text_without_table)}"
                            )

                    # Display the ICD table
                    st.subheader("ICD Diagnosis Codes")

                    # Create an HTML table for better display
                    html_table = create_html_table(headers, rows)
                    st.markdown(html_table, unsafe_allow_html=True)

                    # We've handled the ICD table, so return
                    return

    # If we didn't find and handle an ICD table directly, fall back to the regular table handling

    # Try to extract ICD codes directly from the text
    icd_headers, icd_rows, found_icd_codes = extract_icd_codes_directly(text)

    # Determine if we need to handle tables
    should_handle_tables = style.lower() == 'tabular' or has_icd_table or found_icd_codes

    if should_handle_tables:
        # Extract and display tables
        text_without_tables, parsed_tables = display_markdown_tables(text)

        if style.lower() == 'tabular':
            # For tabular style, display the text content as markdown
            if text_without_tables.strip():
                st.markdown(text_without_tables)
        else:
            # For non-tabular styles with ICD tables, display the text content in a text area
            if text_without_tables.strip():
                # Use a text area with a proper label
                st.text_area(
                    label="Analysis Text",
                    value=text_without_tables,
                    height=400,
                    label_visibility="collapsed",
                    key=f"text_area_{hash(text_without_tables)}"
                )

        # Flag to track if we've already displayed an ICD table
        icd_table_displayed = False

        # Display each table
        for i, table_data in enumerate(parsed_tables):
            # Unpack the table data - handle both old and new formats
            try:
                if len(table_data) == 3:
                    headers, rows, is_icd_table = table_data
                else:
                    headers, rows = table_data
                    # Fallback detection if is_icd_table wasn't provided
                    is_icd_table = any("ICD" in header for header in headers) or any("ICD" in str(row[0]) for row in rows if row)
            except ValueError:
                # If unpacking fails, just use the data as is
                headers, rows = table_data
                is_icd_table = any("ICD" in header for header in headers) or any("ICD" in str(row[0]) for row in rows if row)

            if style.lower() == 'tabular' or is_icd_table:
                # For ICD tables or in tabular style, display the table
                if is_icd_table:
                    st.subheader("ICD Diagnosis Codes")
                    icd_table_displayed = True

                    # Create an HTML table for better display
                    html_table = create_html_table(headers, rows)
                    st.markdown(html_table, unsafe_allow_html=True)
                else:
                    st.subheader(f"Table {i+1}")
                    # For non-ICD tables, use the HTML table for consistent styling
                    html_table = create_html_table(headers, rows)
                    st.markdown(html_table, unsafe_allow_html=True)

        # If we found ICD codes directly but didn't display an ICD table yet, show them now
        if found_icd_codes and not icd_table_displayed and len(icd_rows) > 0:
            st.subheader("ICD Diagnosis Codes")

            # Create an HTML table for better display
            html_table = create_html_table(icd_headers, icd_rows)
            st.markdown(html_table, unsafe_allow_html=True)
    else:
        # For non-tabular styles without ICD tables, just display the text
        # Use a text area with a proper label
        st.text_area(
            label="Analysis Text",
            value=text,
            height=400,
            label_visibility="collapsed",
            key=f"text_area_{hash(text)}"
        )
