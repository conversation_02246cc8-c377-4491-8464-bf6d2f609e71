#!/usr/bin/env python3
"""
Script de verificación de funcionalidad para CaseBuilder.AI.
Este script prueba la funcionalidad crítica después de la refactorización
para asegurar que todo funciona correctamente antes de proceder con
la última fase.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('functionality_test')

# Añadir el directorio principal al path para poder importar módulos
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importar módulos originales
from text_processing import process_text_in_segments
from gpt_api import chat_with_model
from gpt4o_vision import process_image_with_gpt4o

# Importar módulos de la arquitectura hexagonal
from casebuilder.adapters.ai.openai_adapter import OpenAIAdapter
from casebuilder.infrastructure.app_initializer import AppInitializer
from casebuilder.core.services.text_processor_service import TextProcessorService
from casebuilder.infrastructure.dependency_injection import ServiceLocator

SAMPLE_TEXT = """
TRAFFIC COLLISION REPORT
Date: 2023-06-15
Time: 14:30
Location: Main St & Oak Ave, Los Angeles, CA

Driver 1: John Smith (Toyota Corolla)
Driver 2: Jane Doe (Honda Civic)

Narrative: Driver 1 was traveling northbound on Main St. when Driver 2, traveling westbound on Oak Ave,
failed to stop at the red light and collided with Driver 1's vehicle. Driver 1 was transported to
Memorial Hospital with complaints of neck and back pain.

MEDICAL RECORD
Patient: John Smith
Date: 2023-06-15

Diagnosis: Cervical strain, lumbar sprain, contusion to left shoulder
Treatment: Pain medication, physical therapy recommended 3x/week for 8 weeks
Cost: $3,500 for emergency treatment
Follow-up: Patient reported ongoing neck pain and difficulty sleeping
"""

class FunctionalityTest:
    """Clase para probar la funcionalidad de la aplicación."""
    
    def __init__(self):
        """Inicializar el entorno de prueba."""
        self.results = {}
        self.test_count = 0
        self.passed_count = 0
        self.failed_count = 0
        self.start_time = datetime.now()
        
        # Verificar la existencia de la clave API
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            logger.error("❌ No se encontró la clave API de OpenAI en las variables de entorno.")
            logger.error("Por favor, asegúrese de que el archivo .env contiene OPENAI_API_KEY.")
            sys.exit(1)
        
        logger.info(f"🔑 Clave API de OpenAI encontrada")
        logger.info(f"🚀 Iniciando pruebas de funcionalidad")
    
    async def test_original_api(self):
        """Probar las funciones de la API original."""
        logger.info("📋 Prueba 1: API original (gpt_api)")
        
        try:
            # Prueba de chat_with_model
            system_prompt = "Summarize the following text in 3 sentences."
            result = await chat_with_model(
                model_alias="gpt-4o-mini",  # Changed from gpt4omini
                system_prompt=system_prompt,
                user_content=SAMPLE_TEXT,
                max_tokens=1000
            )
            
            # Verificar resultado
            assert result, "No result returned"
            assert len(result.strip()) > 0, "Empty result returned"
            
            self.results['original_api'] = "✅ PASSED"
            self.passed_count += 1
            logger.info(f"✅ API original funcionando correctamente")
            return True
            
        except Exception as e:
            self.results['original_api'] = f"❌ FAILED: {str(e)}"
            self.failed_count += 1
            logger.error(f"❌ Error en API original: {str(e)}")
            return False
    
    async def test_hex_adapter(self):
        """Probar el adaptador OpenAI de la arquitectura hexagonal."""
        logger.info("📋 Prueba 2: Adaptador OpenAI (arquitectura hexagonal)")
        
        try:
            # Crear instancia del adaptador
            adapter = OpenAIAdapter()
            
            # Verificar que la clave API se cargó correctamente
            assert adapter.api_key, "API key not loaded"
            
            # Probar process_text
            result = await adapter.process_text(
                model_alias="gpt-4o-mini",  # Changed from gpt4omini
                system_prompt="Summarize the following text in 3 sentences.",
                user_content=SAMPLE_TEXT,
                max_tokens=1000
            )
            
            # Verificar resultado
            assert result, "No result returned"
            assert len(result.strip()) > 0, "Empty result returned"
            
            self.results['hex_adapter'] = "✅ PASSED"
            self.passed_count += 1
            logger.info(f"✅ Adaptador OpenAI funcionando correctamente")
            return True
            
        except Exception as e:
            self.results['hex_adapter'] = f"❌ FAILED: {str(e)}"
            self.failed_count += 1
            logger.error(f"❌ Error en adaptador OpenAI: {str(e)}")
            return False
    
    async def test_app_initializer(self):
        """Probar el inicializador de la aplicación."""
        logger.info("📋 Prueba 3: AppInitializer")
        
        try:
            # Crear instancia del inicializador
            initializer = AppInitializer()
            
            # Inicializar la aplicación
            adapter = initializer.initialize()
            
            # Verificar que el adaptador se creó correctamente
            assert adapter, "Adapter not created"
            
            self.results['app_initializer'] = "✅ PASSED"
            self.passed_count += 1
            logger.info(f"✅ AppInitializer funcionando correctamente")
            return True
            
        except Exception as e:
            self.results['app_initializer'] = f"❌ FAILED: {str(e)}"
            self.failed_count += 1
            logger.error(f"❌ Error en AppInitializer: {str(e)}")
            return False
    
    async def test_service_locator(self):
        """Probar el ServiceLocator."""
        logger.info("📋 Prueba 4: ServiceLocator")
        
        try:
            # Crear instancia del ServiceLocator
            locator = ServiceLocator()
            
            # Registrar un servicio
            api_key = os.getenv('OPENAI_API_KEY')
            adapter = OpenAIAdapter(api_key=api_key)
            locator.register_service("IAIService", adapter)
            
            # Obtener el servicio
            service = locator.get_service("IAIService")
            
            # Verificar que el servicio se recuperó correctamente
            assert service, "Service not retrieved"
            assert isinstance(service, OpenAIAdapter), "Retrieved service is not OpenAIAdapter"
            
            self.results['service_locator'] = "✅ PASSED"
            self.passed_count += 1
            logger.info(f"✅ ServiceLocator funcionando correctamente")
            return True
            
        except Exception as e:
            self.results['service_locator'] = f"❌ FAILED: {str(e)}"
            self.failed_count += 1
            logger.error(f"❌ Error en ServiceLocator: {str(e)}")
            return False
    
    async def test_text_processor(self):
        """Probar el procesador de texto."""
        logger.info("📋 Prueba 5: TextProcessorService")
        
        try:
            # Crear instancia del adaptador
            adapter = OpenAIAdapter()
            
            # Crear instancia del procesador de texto
            processor = TextProcessorService(ai_service=adapter)
            
            # Probar process_text
            prompt = "Summarize the following text in 3 sentences:"
            result = await processor.process_text_in_segments(
                model_alias="gpt-4o-mini",  # Changed from gpt4omini
                prompt=prompt,
                text=SAMPLE_TEXT
            )
            
            # Verificar resultado
            assert result, "No result returned"
            assert len(result.strip()) > 0, "Empty result returned"
            
            self.results['text_processor'] = "✅ PASSED"
            self.passed_count += 1
            logger.info(f"✅ TextProcessorService funcionando correctamente")
            return True
            
        except Exception as e:
            self.results['text_processor'] = f"❌ FAILED: {str(e)}"
            self.failed_count += 1
            logger.error(f"❌ Error en TextProcessorService: {str(e)}")
            return False
    
    async def run_tests(self):
        """Ejecutar todas las pruebas."""
        # Prueba 1: API original
        self.test_count += 1
        await self.test_original_api()
        
        # Prueba 2: Adaptador OpenAI
        self.test_count += 1
        await self.test_hex_adapter()
        
        # Prueba 3: AppInitializer
        self.test_count += 1
        await self.test_app_initializer()
        
        # Prueba 4: ServiceLocator
        self.test_count += 1
        await self.test_service_locator()
        
        # Prueba 5: TextProcessorService
        self.test_count += 1
        await self.test_text_processor()
        
        # Mostrar resultados
        self._print_results()
    
    def _print_results(self):
        """Imprimir los resultados de las pruebas."""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        print("\n" + "="*80)
        print(f"RESULTADOS DE PRUEBAS DE FUNCIONALIDAD")
        print(f"Tiempo de ejecución: {duration:.2f} segundos")
        print("="*80)
        
        # Imprimir resultados individuales
        for test, result in self.results.items():
            print(f"{test}: {result}")
        
        # Imprimir resumen
        print("\n" + "-"*80)
        print(f"RESUMEN: {self.passed_count} pruebas pasaron, {self.failed_count} fallaron")
        print("-"*80)
        
        # Determinar si todas las pruebas pasaron
        if self.failed_count == 0:
            print("\n✅ TODAS LAS PRUEBAS PASARON")
            print("La aplicación está lista para la última fase de refactorización.")
        else:
            print("\n❌ ALGUNAS PRUEBAS FALLARON")
            print("Por favor, corrija los errores antes de continuar con la refactorización.")

async def main():
    """Función principal."""
    test = FunctionalityTest()
    await test.run_tests()

# Ejecutar si se llama como script
if __name__ == "__main__":
    asyncio.run(main())
