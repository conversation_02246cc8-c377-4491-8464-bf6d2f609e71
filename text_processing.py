import asyncio
from gpt_api import chat_with_model #chat_with_gpt4, chat_with_o1, chat_with_o3
import aiohttp
import tiktoken


MAX_TOTAL_TOKENS_MAP = {
    "gpt4o": 128000,
    "gpt4omini": 128000,
    "o4-mini": 128000,
    "o1": 128000,
    "o3": 200000
}

MAX_COMPLETION_TOKENS_MAP = {
    "gpt4o": 16384,
    "gpt4omini": 16384,
    "o4-mini": 16384,
    "o1": 16384,
    "o3": 16384
}

def split_text_into_segments(text, max_tokens=8000):
    enc = tiktoken.get_encoding("cl100k_base")
    tokens = enc.encode(text)

    segments = []
    current_segment = []
    current_tokens = 0

    for token in tokens:
        current_segment.append(token)
        current_tokens += 1
        if current_tokens >= max_tokens:
            segments.append(enc.decode(current_segment))
            current_segment = []
            current_tokens = 0

    # Cualquier sobrante final
    if current_segment:
        segments.append(enc.decode(current_segment))

    return segments

async def process_text_in_segments(model_alias: str, text: str, prompt: str) -> str:
    """
    Procesa 'text' en segmentos y llama al modelo definido por 'model_alias'
    usando 'prompt' como 'system_prompt' o 'user_prompt' (dependiendo de la config).

    1. Divide el texto en segmentos
    2. Para cada segmento, llama al modelo con un sub-prompt, p.ej. "extract_information(...)"
    3. Concatena los resultados
    4. Hace una llamada final de 'consolidation' para unificar y remover duplicados

    Devuelve un 'string' con el texto final.
    """
    enc = tiktoken.get_encoding("cl100k_base")
    prompt_tokens = len(enc.encode(prompt))
    text_segments = split_text_into_segments(text, max_tokens=8000)  # ajusta el chunk_size si deseas

    max_total = MAX_TOTAL_TOKENS_MAP.get(model_alias, 100000)
    max_completion = MAX_COMPLETION_TOKENS_MAP.get(model_alias, 50000)

    summaries = []

    # 1) Proceso de cada segmento
    for segment in text_segments:
        segment_tokens = len(enc.encode(segment))
        # Cálculo "disponible" para la respuesta
        # (restando prompt_tokens + segment_tokens + un margen de 1000)
        available_for_completion = min(
            max_completion,
            max_total - prompt_tokens - segment_tokens - 1000
        )
        if available_for_completion > 0:
            # Llamada al modelo con "extract_information()"
            # - Como gpt4o usa system_prompt + user_content
            #   y o1/o3 combinan, chat_with_model se encarga.
            # - Ponemos en 'user_content' el f"extract_information('{segment}')"
            result = await chat_with_model(
                model_alias=model_alias,
                system_prompt=prompt,  # Este 'prompt' es la "base"
                user_content=f"extract_information('{segment}')",
                max_tokens=available_for_completion
            )
            summaries.append(result)
        else:
            print("Skipping segment due to insufficient tokens for completion.")

    full_text = " ".join(summaries)

    consolidation_prompt = (
        f"Review and refine the following text, removing any duplicate information while maintaining the narrative flow and professional tone. Present the final result as a clean, cohesive analysis without mentioning the consolidation process:\n'{full_text}'"
    )
    final_summary_tokens = min(
        max_completion,
        max_total - len(enc.encode(consolidation_prompt)) - 1000
    )
    final_summary = await chat_with_model(
        model_alias=model_alias,
        system_prompt=prompt,
        user_content=consolidation_prompt,
        max_tokens=final_summary_tokens
    )
    return final_summary


async def process_full_prompt(model_alias: str, prompt: str) -> str:
    result = await chat_with_model(
        model_alias=model_alias,
        system_prompt=prompt,
        user_content="Please write the demand letter as instructed above.",
        max_tokens=MAX_COMPLETION_TOKENS_MAP.get(model_alias, 16384)
    )
    return result
