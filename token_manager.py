from user import get_clicks, add_click_button

class TokenManager:
    def __init__(self):
        self.DEMAND_VALUE_TOKEN = 5
        self.SIMPLE_VALUE_TOKEN = 1
        
    def get_token_cost(self, action_type: str) -> int:
        """Get the token cost for a specific action type."""
        if action_type == "demand_letter":
            return self.DEMAND_VALUE_TOKEN
        else:
            return self.SIMPLE_VALUE_TOKEN
    
    def get_user_tokens(self, user_name: str) -> int:
        """Get the available tokens for a user."""
        result = get_clicks(user_name, "compose_demand")
        if result:
            max_clicks_allowed = int(result[0][4])
            used_clicks = int(result[0][3])
            available_clicks = max_clicks_allowed - used_clicks
            return available_clicks
        return 0
    
    def deduct_tokens(self, user_name: str, token_cost: int) -> bool:
        """Deduct tokens from a user's account."""
        result = get_clicks(user_name, "compose_demand")
        if result and len(result) > 0:
            button_id = result[0][0]
            click_button = int(result[0][3])
            next_count = click_button + token_cost
            add_click_button(button_id, next_count)
            return True
        return False
        
    def check_and_deduct_tokens(self, user_name: str, action_type: str) -> bool:
        """Check if user has enough tokens and deduct them if so."""
        token_cost = self.get_token_cost(action_type)
        available_tokens = self.get_user_tokens(user_name)
        
        if available_tokens >= token_cost:
            self.deduct_tokens(user_name, token_cost)
            return True
        return False
