import os
from dotenv import load_dotenv
import mysql.connector

load_dotenv()

# def get_con():
#     conn = mysql.connector.connect( host=f"{os.getenv('DATABASE_HOST')}",
#                                     port=f"{os.getenv('DATABASE_PORT')}",
#                                     user=f"{os.getenv('DATABASE_USER')}",
#                                     passwd=f"{os.getenv('DATABASE_PASSWORD')}",
#                                     db=f"{os.getenv('DATABASE_NAME')}"
#                                   )
#     return conn


# def get_con():
#     db_user = os.getenv("DATABASE_USER")
#     db_pass = os.getenv("DATABASE_PASSWORD")
#     db_name = os.getenv("DATABASE_NAME")
#     db_socket_dir = os.getenv("DB_SOCKET_DIR", "/cloudsql")
#     cloud_sql_connection_name = os.getenv("INSTANCE_CONNECTION_NAME")

#     conn = mysql.connector.connect(
#         user=db_user,
#         password=db_pass,
#         database=db_name,
#         unix_socket=f"{db_socket_dir}/{cloud_sql_connection_name}"
#     )
#     return conn


def get_con():
    db_user = os.getenv("DATABASE_USER")
    db_pass = os.getenv("DATABASE_PASSWORD")
    db_name = os.getenv("DATABASE_NAME")

    # Detecta si estamos en Cloud Run o en local
    if os.getenv("INSTANCE_CONNECTION_NAME"):
        # Estamos en producción usando socket
        db_socket_dir = os.getenv("DB_SOCKET_DIR", "/cloudsql")
        cloud_sql_connection_name = os.getenv("INSTANCE_CONNECTION_NAME")

        conn = mysql.connector.connect(
            user=db_user,
            password=db_pass,
            database=db_name,
            unix_socket=f"{db_socket_dir}/{cloud_sql_connection_name}"
        )
    else:
        # Estamos en local usando IP y puerto
        db_host = os.getenv("DATABASE_HOST", "127.0.0.1")
        db_port = int(os.getenv("DATABASE_PORT", 3306))

        conn = mysql.connector.connect(
            user=db_user,
            password=db_pass,
            database=db_name,
            host=db_host,
            port=db_port
        )
    return conn

def login_user(username,password):
    conn = get_con()
    c = conn.cursor()
    c.execute('SELECT * FROM user WHERE username = %s AND password = %s',(username,password))
    data = c.fetchall()
    c.close()
    conn.close()
    return data


def get_clicks(username,button_name):
    conn = get_con()
    c = conn.cursor()
    c.execute('SELECT * FROM button_clicks WHERE username = %s AND button_name = %s',(username,button_name))
    data = c.fetchall()
    c.close()
    conn.close()
    return data


def add_click_button(button_id,count):
    conn = get_con()
    c2 = conn.cursor()
    c2.execute('UPDATE button_clicks SET click_count = %s WHERE id = %s',(count,button_id))
    conn.commit()
    c2.close()
    conn.close()

def update_last_login(username):
    """
    Update the last_login timestamp for a user with GMT-7 timezone.
    Creates the last_login column if it doesn't exist.

    Parameters:
    - username: The username to update
    """
    conn = get_con()
    c = conn.cursor()

    # Ensure last_login column exists
    c.execute(
        """
        SELECT COUNT(*)
        FROM information_schema.columns
        WHERE table_schema = DATABASE() AND table_name = 'user' AND column_name = 'last_login'
        """
    )
    if c.fetchone()[0] == 0:
        c.execute("ALTER TABLE user ADD COLUMN last_login TIMESTAMP NULL")

    # Update the last_login timestamp with GMT-7 timezone
    c.execute('UPDATE user SET last_login = CONVERT_TZ(NOW(), @@session.time_zone, "-07:00") WHERE username = %s', (username,))
    conn.commit()
    c.close()
    conn.close()


def insert_feedback(name, email, feedback_text, rating="⭐️⭐️⭐️⭐️⭐️", feedback_type="Comment"):
    """
    Insert user feedback into the database. Creates the feedback table if it does not exist.

    Parameters:
    - name: User's name (optional)
    - email: User's email (optional)
    - feedback_text: The feedback content
    - rating: Star rating from "⭐️" to "⭐️⭐️⭐️⭐️⭐️"
    - feedback_type: Type of feedback (Bug, Suggestion, Comment)
    """
    conn = get_con()
    c = conn.cursor()
    # Ensure feedback table exists
    c.execute(
        '''
        CREATE TABLE IF NOT EXISTS feedback (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255),
            email VARCHAR(255),
            feedback TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB
        '''
    )
    # Ensure rating column exists
    c.execute(
        """
        SELECT COUNT(*)
        FROM information_schema.columns
        WHERE table_schema = DATABASE() AND table_name = 'feedback' AND column_name = 'rating'
        """
    )
    if c.fetchone()[0] == 0:
        c.execute("ALTER TABLE feedback ADD COLUMN rating VARCHAR(50) DEFAULT %s", (rating,))
    # Ensure feedback_type column exists
    c.execute(
        """
        SELECT COUNT(*)
        FROM information_schema.columns
        WHERE table_schema = DATABASE() AND table_name = 'feedback' AND column_name = 'feedback_type'
        """
    )
    if c.fetchone()[0] == 0:
        c.execute("ALTER TABLE feedback ADD COLUMN feedback_type VARCHAR(50) DEFAULT %s", (feedback_type,))
    # Insert the feedback record
    # Insert the feedback record
    insert_sql = 'INSERT INTO feedback (name, email, feedback, rating, feedback_type) VALUES (%s, %s, %s, %s, %s)'
    c.execute(insert_sql, (name, email, feedback_text, rating, feedback_type))
    conn.commit()
    c.close()
    conn.close()

