# CaseBuilder – Privacy Verification Kit

## Why this matters

At CaseBuilder, we do not store or retain any of your documents, case data, or sensitive information. Everything is processed in real time and deleted immediately after use. But don't just take our word for it – this kit allows your firm to independently verify that no data is saved, logged, or retained in any form.

---

## Tools You Can Use to Audit CaseBuilder

Below are two tools your team can use to independently validate that CaseBuilder does not store or retain any files, personally identifiable information (PII), or sensitive client data after processing. These tools can be run locally on your own system to observe the behavior of our platform during and after document analysis.

### 1. <PERSON><PERSON> (Recommended)

<PERSON><PERSON> Vault scans apps, logs, and storage paths for traces of Personally Identifiable Information (PII).

**Steps:**
1. Make sure you have Docker installed.
2. Run this command:
   ```
   docker run --rm -p 8080:8080 piiano/pvault
   ```
3. Visit `http://localhost:8080` in your browser.
4. While using CaseBuilder, monitor whether any data is written or retained.
5. Optionally, scan directories like `/tmp`, `Downloads`, or your browser cache for leaks.

More info: [https://www.piiano.com](https://www.piiano.com)

---

### 2. DataSniff (Lightweight PII Scanner)

DataSniff is a command-line tool that scans folders and files for any PII (names, dates, SSNs, emails, etc.).

**Steps:**
1. Install Python 3 if not already installed.
2. Download the tool:
   ```
   git clone https://github.com/13o-bbr-bbq/DataSniff.git
   cd DataSniff
   pip install -r requirements.txt
   ```
3. Run the scan:
   ```
   python3 datasniff.py --path /tmp
   ```
4. Repeat the scan for `~/Downloads`, `~/Documents`, or any temporary file storage location.

Purpose: Confirm that no data remains after using CaseBuilder.

---

## What to Expect (PASS Criteria)

If CaseBuilder is working as designed, the tools should report:
- No saved PDF or document files
- No traces of client names, dates of birth, or case details
- No unauthorized file writes or database logs

---

## Optional: Request a Technical Privacy Report

We are happy to provide a signed 1-page privacy architecture overview upon request, detailing:
- Our no-retention policy
- Data flow architecture
- Session-based ephemeral storage logic

---

## Questions?

Feel free to reach out to **<EMAIL>** with any questions or if you'd like help running these tools.

---

## Appendix: Privacy Architecture Overview

**Client:** ..............................................................  
**Date:** ..............................................................  
**Reviewer:** ..............................................................

### 1. No-Retention Policy
CaseBuilder is built on a strict no-retention policy. Documents uploaded by the user are never stored, indexed, or retained after processing. No backups or shadow copies are created at any point.

### 2. Data Flow Architecture
User uploads → File is processed in memory → AI returns structured results → File is deleted immediately.

No document or case data is written to disk or sent to long-term storage systems.

### 3. Session-Based Ephemeral Storage Logic
- Any temporary data is stored only in RAM.
- Sessions are auto-terminated after inactivity.
- Closing the browser or logging out triggers complete cleanup.

All activity is isolated per session and no cross-session data persistence occurs.

---

**Signed by:** CaseBuilder Privacy & Security Team
